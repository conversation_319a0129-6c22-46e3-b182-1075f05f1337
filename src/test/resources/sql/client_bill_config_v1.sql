-- -----------------------------------
-- 商家网店下账设置: store_bill_config
-- -----------------------------------
DROP TABLE IF EXISTS `store_bill_config`;

CREATE TABLE IF NOT EXISTS `store_bill_config` (
	`id` BIGINT(20) NOT NULL AUTO_INCREMENT,
	`name` VARCHAR(128) NOT NULL COMMENT '配置名称',
	`mer_code` VARCHAR(32) NOT NULL COMMENT '企业编码',
	`platform_code` VARCHAR(32) NOT NULL COMMENT '平台编码',
	`client_code` VARCHAR(64) NOT NULL COMMENT '网店编码',
	`freight_fee_fetch` INT(3) DEFAULT '1' COMMENT '配送费, 收取方式: 1-平台收取, 2-商家收取',
	`freight_fee_inventory` TINYINT(1) DEFAULT '0' COMMENT '配送费, 是否下账: 0-不下账, 1-下账',
	`package_fee_fetch` INT(3) DEFAULT '2' COMMENT '包装费, 收取方式: 1-平台收取, 2-商家收取',
	`package_fee_inventory` TINYINT(1) DEFAULT '1' COMMENT '包装费, 是否下账: 0-不下账, 1-下账',
	`mc_discount_inventory` TINYINT(1) DEFAULT '1' COMMENT '商家优惠金额, 是否下账: 0-不下账, 1-下账',
	`mc_discount_share` TINYINT(1) DEFAULT '0' COMMENT '商家优惠金额, 是否分摊: 0-下账商家优惠金额, 1-分摊至商品明细',
	`ptf_discount_inventory` TINYINT(1) DEFAULT '0' COMMENT '平台优惠金额, 是否下账: 0-不下账, 1-下账',
	`ptf_discount_share` TINYINT(1) DEFAULT NULL COMMENT '平台优惠金额, 是否分摊: 0-下账平台优惠金额, 1-分摊至商品明细',
	`ptf_commission_inventory` TINYINT(1) DEFAULT '1' COMMENT '平台收取佣金, 是否下账: 0-不下账, 1-下账',
	`ptf_commission_share` TINYINT(1) DEFAULT '0' COMMENT '平台收取佣金, 是否分摊: 0-下账平台佣金, 1-分摊至商品明细',
	`mc_dtl_discount_inventory` TINYINT(1) DEFAULT '1' COMMENT '商家明细优惠金额, 是否下账: 0-不下账, 1-下账',
	`mc_dtl_discount_share` TINYINT(1) DEFAULT '1' COMMENT '商家明细优惠金额, 是否分摊: 0-下账商品明细优惠金额, 1-分摊至商品明细',
	`cashier_source` INT(3) DEFAULT '2' COMMENT '收银员取值: 1-同拣货员, 2-ERP中获取',
	`creator` VARCHAR(32) NOT NULL COMMENT '创建人',
	`create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`modify_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '末次修改时间',
	PRIMARY KEY (`id`),
	INDEX `idx_merchant_platform_client` (`mer_code`, `platform_code`, `client_code`),
	INDEX `idx_create_time`(`create_time`)
) ENGINE=INNODB AUTO_INCREMENT=1 COMMENT='商家网店下账配置信息表';
