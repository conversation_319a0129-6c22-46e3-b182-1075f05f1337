package cn.hydee.middle.business.order.service;

import cn.hydee.middle.business.order.dto.req.OrderLedgerPageReqDto;
import cn.hydee.middle.business.order.util.DateUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * cn.hydee.middle.business.order.service
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/8/25 10:43
 **/
@Slf4j
public class OrderInfoServiceTest extends BaseServiceTest{

    /** 下账列表测试 **/
    @Test
    public void testGetOrderLedgerAllListPage(){
        Date date = new Date();
        OrderLedgerPageReqDto reqDto = new OrderLedgerPageReqDto();
        reqDto.setMerCode("888888");
        List<String>  organizationList = new ArrayList<>();
        organizationList.add("2001");
        orderInfoService.getOrderLedgerAllListPage(reqDto,organizationList,null);
        Date dateLater =  new Date();
        log.info("OrderInfoServiceTest: 毫秒:{}，秒：{}", dateLater.getTime()-date.getTime(),(dateLater.getTime()-date.getTime())/1000.0);
        //log.info("OrderInfoServiceTest: data: {}", JSONObject.toJSONString(orderInfoService.getOrderLedgerAllListPage(reqDto,organizationList)));
    }
}
