package cn.hydee.middle.business.order.mapper;

import cn.hydee.middle.business.order.Enums.DsErrorType;
import cn.hydee.middle.business.order.dto.req.OnlineStoreReqDto;
import cn.hydee.middle.business.order.dto.req.baseinfo.DsOnlineStoreConfCreateUpdateDTO;
import cn.hydee.middle.business.order.dto.req.baseinfo.DsStoreOrderConfCreateUpdateDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.DsOnlineStoreResDTO;
import cn.hydee.middle.business.order.entity.DsOnlineStore;
import cn.hydee.middle.business.order.entity.DsOnlineStoreConfig;
import cn.hydee.middle.business.order.entity.DsStoreOrderConfig;
import cn.hydee.middle.business.order.service.baseinfo.DsOnlineStoreService;
import cn.hydee.middle.business.order.util.ExceptionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.stereotype.Service;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * cn.hydee.middle.business.order.mapper
 *
 * <AUTHOR> Li
 * @version 1.0
 * @date 2020/8/8 15:01
 **/
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@Slf4j
public class DsOnlineStoreConfigMapperTest {

    @Autowired
    private DsOnlineStoreConfigRepo dsOnlineStoreConfigRepo;
    @Autowired
    private DsOnlineStoreRepo dsOnlineStoreRepo;

    @Test
    public void testInsertOrUpdate(){
        OnlineStoreReqDto dto = new OnlineStoreReqDto();
        dto.setClientCode("41c9e98ce4884f4880c5e2925864bb50");
        dto.setMerCode("888888");
        dto.setOnlineStoreCode("2002");
        dto.setPlatformCode("43");
        //查询唯一门店索引：mer_code, platform_code, online_client_code, online_store_code
        QueryWrapper<DsOnlineStore> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(DsOnlineStore::getMerCode, dto.getMerCode())
                .eq(DsOnlineStore::getPlatformCode, dto.getPlatformCode())
                .eq(DsOnlineStore::getOnlineClientCode, dto.getClientCode())
                .eq(DsOnlineStore::getOnlineStoreCode, dto.getOnlineStoreCode());
        DsOnlineStore dsOnlineStore = dsOnlineStoreRepo.selectOne(queryWrapper);
        //5.查询对应的线上门店配置（merCode、online_store_id）
        QueryWrapper<DsOnlineStoreConfig> storeConfWrapper = new QueryWrapper<>();
        storeConfWrapper.lambda()
                .eq(DsOnlineStoreConfig::getMerCode,dsOnlineStore.getMerCode())
                .eq(DsOnlineStoreConfig::getOnlineStoreId,dsOnlineStore.getId());
        DsOnlineStoreConfig onlineStoreConfig = dsOnlineStoreConfigRepo.selectOne(storeConfWrapper);
        //更新 插入
        DsOnlineStoreConfCreateUpdateDTO updateDto = new DsOnlineStoreConfCreateUpdateDTO();
        //修改线上门店配置信息
        DsOnlineStoreConfig dsOnlineStoreConfig = onlineStoreConfig;
        dsOnlineStoreConfig.setWhetherInventoryLocked(1);
        LambdaUpdateWrapper<DsOnlineStoreConfig> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.eq(DsOnlineStoreConfig::getId,dsOnlineStoreConfig.getId());
        dsOnlineStoreConfigRepo.update(dsOnlineStoreConfig, lambdaUpdateWrapper);
    }

    @Test
    public void testSearch(){
        OnlineStoreReqDto dto = new OnlineStoreReqDto();
        dto.setClientCode("41c9e98ce4884f4880c5e2925864bb50");
        dto.setMerCode("888888");
        dto.setOnlineStoreCode("2002");
        dto.setPlatformCode("43");
        //查询唯一门店索引：mer_code, platform_code, online_client_code, online_store_code
        QueryWrapper<DsOnlineStore> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(DsOnlineStore::getMerCode, dto.getMerCode())
                .eq(DsOnlineStore::getPlatformCode, dto.getPlatformCode())
                .eq(DsOnlineStore::getOnlineClientCode, dto.getClientCode())
                .eq(DsOnlineStore::getOnlineStoreCode, dto.getOnlineStoreCode());
        DsOnlineStore dsOnlineStore = dsOnlineStoreRepo.selectOne(queryWrapper);
        //5.查询对应的线上门店配置（merCode、online_store_id）
        QueryWrapper<DsOnlineStoreConfig> storeConfWrapper = new QueryWrapper<>();
        storeConfWrapper.lambda()
                .eq(DsOnlineStoreConfig::getMerCode,dsOnlineStore.getMerCode())
                .eq(DsOnlineStoreConfig::getOnlineStoreId,dsOnlineStore.getId());
        DsOnlineStoreConfig onlineStoreConfig = dsOnlineStoreConfigRepo.selectOne(storeConfWrapper);

    }

    @Test
    public void testDsOnlineStoreRep(){
        log.info("Data: {}",dsOnlineStoreConfigRepo.getAllMerchantClientBillSetting());
    }
}
