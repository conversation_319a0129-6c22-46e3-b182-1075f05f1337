package cn.hydee.middle.business.order.mapper;

import cn.hydee.middle.business.order.dto.StoreQueryBase;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.bytebuddy.asm.Advice;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * cn.hydee.middle.business.order.mapper
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/8/5 20:07
 **/
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@Slf4j
public class StoreBillConfigMapperTest {
    public static String MER_CODE="888888";
    public static String PLATFORM_CODE="43";
    public static String CLIENT_CODE="41c9e98ce4884f4880c5e2925864bb50";
    public static String STORE_CODE="2002";

    @Autowired
    private StoreBillConfigMapper storeBillConfigMapper;

    /**
     * 部分改动测试查询是否ok  插入部分 已在swagger测试
     */
    @Test
    public void testGet(){
        StoreQueryBase queryBase = new StoreQueryBase();
        queryBase.setMerCode(MER_CODE);
        queryBase.setPlatformCode(PLATFORM_CODE);
        queryBase.setStoreCode(STORE_CODE);
        queryBase.setClientCode(CLIENT_CODE);
        log.info(""+this.getClass().getName()+" getdata: {}", JSONObject.toJSONString(storeBillConfigMapper.getLatestBillConfig(queryBase)));
    }

    @Test
    public void testGetById(){
        log.info(""+this.getClass().getName()+" getdata: {}", JSONObject.toJSONString(storeBillConfigMapper.getBillConfigById(569L)));
    }
}
