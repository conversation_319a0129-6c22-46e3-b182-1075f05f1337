package cn.hydee.middle.business.order.feign;

import cn.hydee.middle.business.order.BaseTest;
import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.dto.commodity.AveragePriceQuery;
import cn.hydee.middle.business.order.dto.commodity.AveragePriceVO;
import cn.hydee.middle.business.order.dto.commodity.CommodityStockInfoDto;
import cn.hydee.middle.business.order.dto.commodity.QueryCommodityStockDto;
import cn.hydee.middle.business.order.util.JsonUtil;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import com.google.common.collect.Lists;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> <PERSON> (moatkon)
 * @date 2024年01月29日 14:57
 * @email: <EMAIL>
 */
public class MiddleMerchandiseClientTest extends BaseTest {
    @Resource
    private MiddleMerchandiseClient middleMerchandiseClient;

    @Test
    public void test() {
        for (; ; ) {
            try {
                QueryCommodityStockDto dto = QueryCommodityStockDto.buildBean("500001", "D100", Arrays.asList("125257"));
                ResponseBase<PageDTO<CommodityStockInfoDto>> testUserId = middleMerchandiseClient.queryCommodityStock("testUserId", dto);
                System.out.println();
            } catch (Exception e) {
                System.out.println(e);
            }
        }

    }

    @Test
    public void test2() {
        for (; ; ) {
            try {
                AveragePriceQuery averagePriceQuery = AveragePriceQuery.buildBean("500001", "D100", Lists.newArrayList("125257"));
                ResponseBase<List<AveragePriceVO>> testUserId = middleMerchandiseClient.queryAveragePrice(DsConstants.SYSTEM, averagePriceQuery);
                System.out.println(JsonUtil.object2Json(testUserId));
//                {
//                    "code": "10000",
//                        "msg": "操作成功",
//                        "data": [
//                    {
//                        "erpCode": "125257",
//                            "averagePrice": 15.46,
//                            "specId": "1779061905856462595"
//                    }
//    ],
//                    "timestamp": 1706580427829
//                }
                System.out.println();
            } catch (Exception e) {
                System.out.println(e);
            }
        }

    }
}
