package cn.hydee.middle.business.order.sql;

import cn.hydee.middle.business.order.Enums.ErpStateEnum;
import cn.hydee.middle.business.order.Enums.OrderStateEnum;
import cn.hydee.middle.business.order.Enums.TableConstant;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.service.OrderInfoServiceTest;
import cn.hydee.middle.business.order.service.impl.OrderInfoServiceImpl;
import cn.hydee.middle.business.order.util.sqlbuild.CustomSqlFactory;
import cn.hydee.middle.business.order.util.sqlbuild.CustomUpdateWrapper;
import cn.hydee.middle.business.order.util.sqlbuild.sqlcheck.ICustomSqlCheck;

/**
 * cn.hydee.middle.business.order.sql
 *
 * <AUTHOR> <PERSON>
 * @version 1.0
 * @date 2020/8/26 21:28
 **/
public class TestSql {

    public static void main(String[] args) {
        //模拟注入攻击
        CustomUpdateWrapper<OrderInfo> customUpdateWrapper = new CustomUpdateWrapper<>();
        customUpdateWrapper.update(TableConstant.ORDER_INFO)
                .set()
                .eq(OrderInfo::getErpState,ErpStateEnum.CANCELED.getCode())
                .where()
                .eq(OrderInfo::getMerCode,"888888 \';drop table order_info; -- or 1=1\'")
                .and()
                .ge(OrderInfo::getOrderState,OrderStateEnum.CLOSED.getCode())
                .and()
                .le(OrderInfo::getOrderState,OrderStateEnum.CANCEL.getCode())
                .and()
                .ge(OrderInfo::getErpState,ErpStateEnum.WAIT_PICK.getCode())
                .and()
                .le(OrderInfo::getErpState,ErpStateEnum.WAIT_SALE.getCode())
                .semicolon();
        System.out.println(customUpdateWrapper.getUpdateSql());
        ICustomSqlCheck customSqlCheck = CustomSqlFactory.createCustomSqlCheck();
        if(!customSqlCheck.sqlCheck(customUpdateWrapper.getUpdateSql().toString()))
        {
            System.out.println("验证失败");
        }
    }
}
