package cn.hydee.middle.business.order.yxtadapter.infrastructure.domainserviceimpl.kcpos;

import cn.hydee.middle.business.order.Enums.ErpStateEnum;
import cn.hydee.middle.business.order.Enums.RefundErpStateEnum;
import cn.hydee.middle.business.order.entity.RefundOrder;
import cn.hydee.middle.business.order.entity.b2c.OmsOrderInfo;
import cn.hydee.middle.business.order.mapper.RefundOrderMapper;
import cn.hydee.middle.business.order.mapper.b2c.OmsOrderInfoMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年01月08日 11:50
 * @email: <EMAIL>
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class KcPosOrderAccountingAdapterTest {
    @Autowired
    private OmsOrderInfoMapper omsOrderInfoMapper;

    @Autowired
    private RefundOrderMapper refundOrderMapper;

    @Test
    public void test () {
        RefundOrder refundOrder = refundOrderMapper.selectById(453694);
        RefundOrder reUpdate = new RefundOrder().setBillTime(new Date())
                .setRefundNo(refundOrder.getRefundNo())
                .setErpRefundNo(refundOrder.getRefundNo().toString())
                .setErpRefundNo("1111")
                .setErpState(RefundErpStateEnum.HAS_REFUND.getCode())
                .setBillType(1)
                .setRefundType(String.valueOf(1));
        refundOrderMapper.updateByRefundNo(reUpdate);
    }

    @Test
    public void testUpdateOmsOrderInfo(){
        OmsOrderInfo updateOmsOrderInfo = new OmsOrderInfo();
        updateOmsOrderInfo.setOrderNo(1785694569002117376L);
        updateOmsOrderInfo.setErpStatus(ErpStateEnum.HAS_SALE.getCode());
        updateOmsOrderInfo.setBillTime(LocalDateTime.now());
        updateOmsOrderInfo.setErpSaleNo("test posNo");
        updateOmsOrderInfo.setBillOperatorId("");
        updateOmsOrderInfo.setBillOperatorName("");
        int update = omsOrderInfoMapper.update(updateOmsOrderInfo,
                new QueryWrapper<OmsOrderInfo>().lambda().eq(OmsOrderInfo::getOrderNo, 1785694569002117376L));
        System.out.println(update);
    }

}
