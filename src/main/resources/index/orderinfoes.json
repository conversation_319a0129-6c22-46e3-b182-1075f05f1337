{"properties": {"merCode": {"type": "keyword"}, "orderNo": {"type": "long"}, "thirdPlatformCode": {"type": "keyword"}, "thirdOrderNo": {"type": "keyword"}, "orderState": {"type": "integer"}, "lockFlag": {"type": "integer"}, "createTime": {"type": "long"}, "onlineStoreCode": {"type": "keyword"}, "memberNo": {"type": "keyword"}, "organizationCode": {"type": "keyword"}, "organizationName": {"type": "keyword"}, "sourceOrganizationCode": {"type": "keyword"}, "sourceOrganizationName": {"type": "keyword"}, "acceptTime": {"type": "long"}, "billTime": {"type": "long"}, "created": {"type": "long"}, "completeTime": {"type": "long"}, "pickTime": {"type": "long"}, "isPrescription": {"type": "integer"}, "prescriptionStatus": {"type": "integer"}, "remindFlag": {"type": "integer"}, "erpState": {"type": "integer"}, "erpSaleNo": {"type": "keyword"}, "serviceMode": {"type": "keyword"}, "appointment": {"type": "integer"}, "appointmentBusinessFlag": {"type": "integer"}, "appointmentBusinessType": {"type": "integer"}, "orderType": {"type": "keyword"}, "medicalInsurance": {"type": "integer"}, "transferDelivery": {"type": "integer"}, "dayNum": {"type": "keyword"}, "buyerRemark": {"type": "keyword"}, "isChangeStore": {"type": "integer"}, "tags": {"type": "keyword"}, "commodityCountAll": {"type": "integer"}, "esCreateTime": {"type": "long"}, "detailList": {"type": "nested", "properties": {"erpCode": {"type": "keyword"}, "commodityName": {"type": "keyword"}, "commodityCount": {"type": "integer"}, "barCode": {"type": "keyword"}, "directDeliveryType": {"type": "integer"}, "status": {"type": "integer"}}}, "payType": {"type": "keyword"}, "totalAmount": {"type": "double"}, "buyerActualAmount": {"type": "double"}, "receiverName": {"type": "keyword"}, "receiverMobile": {"type": "keyword"}, "receiveProvince": {"type": "keyword"}, "receiveCity": {"type": "keyword"}, "receiveDistrict": {"type": "keyword"}, "receiveTown": {"type": "keyword"}, "deliveryType": {"type": "keyword"}, "deliveryPlatName": {"type": "keyword"}, "deliveryState": {"type": "integer"}, "deliveryId": {"type": "long"}, "deliveryTimeType": {"type": "integer"}, "deliveryCallTime": {"type": "long"}, "integralFlag": {"type": "keyword"}, "newCustomerFlag": {"type": "keyword"}, "selfVerifyCode": {"type": "keyword"}, "prescriptionFlag": {"type": "integer"}, "receiverLat": {"type": "keyword"}, "receiverLng": {"type": "keyword"}, "word1": {"type": "keyword"}, "word2": {"type": "keyword"}, "word3": {"type": "keyword"}, "geoPoint": {"type": "geo_point"}, "sourceChannelType": {"type": "integer"}}}