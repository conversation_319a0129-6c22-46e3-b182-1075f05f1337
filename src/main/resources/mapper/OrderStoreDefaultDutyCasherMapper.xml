<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hydee.middle.business.order.mapper.OrderStoreDefaultDutyCasherMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.hydee.middle.business.order.entity.OrderStoreDefaultDutyCasher">
        <id column="id" property="id" />
        <result column="mer_code" property="merCode" />
        <result column="organization_code" property="organizationCode" />
        <result column="open_flag" property="openFlag" />
        <result column="type" property="type" />
        <result column="pick_operator_id" property="pickOperatorId" />
        <result column="pick_operator_code" property="pickOperatorCode" />
        <result column="pick_operator_name" property="pickOperatorName" />
        <result column="cashier_source" property="cashierSource" />
        <result column="cashier_source_name" property="cashierSourceName" />
        <result column="duty_id" property="dutyId" />
        <result column="duty_name" property="dutyName" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>


</mapper>
