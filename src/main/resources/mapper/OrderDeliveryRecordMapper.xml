<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hydee.middle.business.order.mapper.OrderDeliveryRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.hydee.middle.business.order.entity.OrderDeliveryRecord">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="state" property="state" />
        <result column="rider_order_no" property="riderOrderNo" />
        <result column="delivery_type" property="deliveryType" />
        <result column="delivery_plat_name" property="deliveryPlatName" />
        <result column="delivery_client_code" property="deliveryClientCode" />
        <result column="delivery_platform" property="deliveryStoreCode" />
        <result column="delivery_tip" property="deliveryTip" />
        <result column="rider_name" property="riderName" />
        <result column="rider_phone" property="riderPhone" />
        <result column="rider_staff_code" property="riderStaffCode" />
        <result column="rider_address" property="riderAddress" />
        <result column="latitude" property="latitude" />
        <result column="longitude" property="longitude" />
        <result column="call_time" property="callTime" />
        <result column="accept_time" property="acceptTime" />
        <result column="pick_time" property="pickTime" />
        <result column="cancel_from" property="cancelFrom" />
        <result column="cancel_reason" property="cancelReason" />
        <result column="cancel_detail" property="cancelDetail" />
        <result column="exception_reason" property="exceptionReason" />
        <result column="actual_delivery_fee" property="actualDeliveryFee"/>
        <result column="delivery_fee_total" property="deliveryFeeTotal"/>
        <result column="cancel_flag" property="cancelFlag"/>
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="logistics_company" property="logisticsCompany" />
        <result column="logistics_no" property="logisticsNo" />
        <result column="logistics_name" property="logisticsName" />
        <result column="extra_info" property="extraInfo" />
        <result column="delay_state" property="delayState"/>
        <result column="pre_call_flag" property="preCallFlag"/>
    </resultMap>

    <resultMap id="SearchDeliveryPage" type="cn.hydee.middle.business.order.dto.rsp.SearchDeliveryRspDto">
        <result column="order_no" property="orderNo" />
        <result column="order_state" property="orderState" />
        <result column="third_platform_code" property="thirdPlatformCode" />
        <result column="third_order_no" property="thirdOrderNo" />
        <result column="online_store_code" property="onlineStoreCode" />
        <result column="online_store_name" property="onlineStoreName" />
        <result column="organization_code" property="organizationCode" />
        <result column="organization_name" property="organizationName" />
        <result column="created" property="created" />
        <result column="lock_flag" property="lockFlag" />
    </resultMap>

    <sql id="orderDeliveryRecordColumns">
        `id`,
        `order_no`,
        `state`,
        `rider_order_no`,
        `delivery_type`,
        `delivery_plat_name`,
        `delivery_client_code`,
        `delivery_store_code`,
        `delivery_tip`,
        `rider_name`,
        `rider_phone`,
        `rider_staff_code`,
        `rider_address`,
        `latitude`,
        `longitude`,
        `call_time`,
        `accept_time`,
        `pick_time`,
        `cancel_from`,
        `cancel_reason`,
        `cancel_detail`,
        `exception_reason`,
        `actual_delivery_fee`,
        `delivery_fee_total`,
        `cancel_flag`,
        `logistics_company`,
        `logistics_no`,
        `logistics_name`,
        `create_time`,
        `modify_time`,
         extra_info,
         `delay_state`
    </sql>

    <select id="selectByOrderNo" resultMap="BaseResultMap">
        select
        <include refid="orderDeliveryRecordColumns"/>
        from order_delivery_record
        where order_no=#{orderNo, jdbcType=BIGINT}
    </select>

    <select id="selectByOrderNoList" resultMap="BaseResultMap">
        select
        <include refid="orderDeliveryRecordColumns"/>
        from order_delivery_record
        where order_no in
        <foreach collection="orderNoList" item="orderNo" index="index" open="(" close=")" separator=",">
            #{orderNo}
        </foreach>
    </select>

    <select id="searchDeliveryAllPage" resultType="cn.hydee.middle.business.order.dto.rsp.SearchDeliveryRspDto">
        select
        oi.order_no,oi.order_state,oi.third_platform_code,oi.third_order_no,oi.online_store_code,oi.online_store_name,oi.organization_code,
        oi.organization_name,oi.lock_flag
        from order_delivery_record odr
        join order_info oi on oi.order_no = odr.order_no
        where odr.delivery_type = '3' and oi.service_mode = 'O2O'
        <if test="param.thirdOrderNo != null and param.thirdOrderNo != ''">
            and oi.third_order_no=#{param.thirdOrderNo}
        </if>
        <if test="param.orderNo != null and param.orderNo != ''">
            and oi.order_no=#{param.orderNo}
        </if>
        <if test="param.merCode != null and param.merCode != ''">
            and oi.mer_code=#{param.merCode}
        </if>
        <if test="platformCodeList != null and platformCodeList.size() > 0">
            and oi.third_platform_code in
            <foreach collection="platformCodeList" item="platform" index="index" open="(" close=")" separator=",">
                #{platform}
            </foreach>
        </if>
        <if test="organizationCodeList != null and organizationCodeList.size() > 0">
            and oi.organization_code in
            <foreach collection="organizationCodeList" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="param.deliveryState != null">
            and odr.state in
            <foreach collection="param.deliveryState.deliveryStateList" item="item" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="param.onlineStoreCode != null and param.onlineStoreCode != ''">
            and oi.online_store_code=#{param.onlineStoreCode}
        </if>
        <if test="param.deliveryPlatName != null and param.deliveryPlatName != ''">
            and odr.delivery_plat_name=#{param.deliveryPlatName}
        </if>
        <if test="param.deliveryPlatName == null or param.deliveryPlatName == ''">
            and odr.delivery_plat_name in ("蜂鸟骑手","美团骑手","达达骑手","顺丰同城","蜂鸟即配","其他配送中心")
        </if>
        <if test="param.beginTime != null">
            <![CDATA[ and odr.create_time>=#{param.beginTime} ]]>
        </if>
        <if test="param.endTime != null">
            <![CDATA[ and odr.create_time<=#{param.endTime} ]]>
        </if>
        order by oi.id desc
    </select>

    <select id="countSearchDeliveryAllPage" resultType="java.lang.Integer">
        select
        count(oi.order_no)
        from order_delivery_record odr
        join order_info oi on oi.order_no = odr.order_no
        where odr.delivery_type = '3' and oi.service_mode = 'O2O'
        <if test="param.thirdOrderNo != null and param.thirdOrderNo != ''">
            and oi.third_order_no=#{param.thirdOrderNo}
        </if>
        <if test="param.orderNo != null and param.orderNo != ''">
            and oi.order_no=#{param.orderNo}
        </if>
        <if test="param.merCode != null and param.merCode != ''">
            and oi.mer_code=#{param.merCode}
        </if>
        <if test="platformCodeList != null and platformCodeList.size() > 0">
            and oi.third_platform_code in
            <foreach collection="platformCodeList" item="platform" index="index" open="(" close=")" separator=",">
                #{platform}
            </foreach>
        </if>
        <if test="organizationCodeList != null and organizationCodeList.size() > 0">
            and oi.organization_code in
            <foreach collection="organizationCodeList" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="param.deliveryState != null">
            and odr.state in
            <foreach collection="param.deliveryState.deliveryStateList" item="item" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="param.onlineStoreCode != null and param.onlineStoreCode != ''">
            and oi.online_store_code=#{param.onlineStoreCode}
        </if>
        <if test="param.deliveryPlatName != null and param.deliveryPlatName != ''">
            and odr.delivery_plat_name=#{param.deliveryPlatName}
        </if>
        <if test="param.deliveryPlatName == null or param.deliveryPlatName == ''">
            and odr.delivery_plat_name in ("蜂鸟骑手","美团骑手","达达骑手","顺丰同城","蜂鸟即配","其他配送中心")
        </if>
        <if test="param.beginTime != null">
            <![CDATA[ and odr.create_time>=#{param.beginTime} ]]>
        </if>
        <if test="param.endTime != null">
            <![CDATA[ and odr.create_time<=#{param.endTime} ]]>
        </if>
    </select>

    <select id="searchDelivery" resultMap="SearchDeliveryPage">
        select
        oi.order_no,oi.order_state,oi.third_platform_code,oi.third_order_no,oi.online_store_code,oi.online_store_name,oi.organization_code,
        oi.organization_name,oi.lock_flag
        from order_delivery_record odr
        join order_info oi on oi.order_no = odr.order_no
        where odr.delivery_type = '3' and oi.service_mode = 'O2O'
        <if test="param.thirdOrderNo != null and param.thirdOrderNo != ''">
            and oi.third_order_no=#{param.thirdOrderNo}
        </if>
        <if test="param.orderNo != null and param.orderNo != ''">
            and oi.order_no=#{param.orderNo}
        </if>
        <if test="param.merCode != null and param.merCode != ''">
            and oi.mer_code=#{param.merCode}
        </if>
        <if test="platformCodeList != null and platformCodeList.size() > 0">
            and oi.third_platform_code in
            <foreach collection="platformCodeList" item="platform" index="index" open="(" close=")" separator=",">
                #{platform}
            </foreach>
        </if>
        <if test="organizationCodeList != null and organizationCodeList.size() > 0">
            and oi.organization_code in
            <foreach collection="organizationCodeList" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="param.deliveryState != null">
            and odr.state in
            <foreach collection="param.deliveryState.deliveryStateList" item="item" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="param.onlineStoreCode != null and param.onlineStoreCode != ''">
            and oi.online_store_code=#{param.onlineStoreCode}
        </if>
        <if test="param.deliveryPlatName != null and param.deliveryPlatName != ''">
            and odr.delivery_plat_name=#{param.deliveryPlatName}
        </if>
        <if test="param.deliveryPlatName == null or param.deliveryPlatName == ''">
            and odr.delivery_plat_name in ("蜂鸟骑手","美团骑手","达达骑手","顺丰同城","蜂鸟即配","其他配送中心")
        </if>
        <if test="param.beginTime != null">
            <![CDATA[ and odr.create_time>=#{param.beginTime} ]]>
        </if>
        <if test="param.endTime != null">
            <![CDATA[ and odr.create_time<=#{param.endTime} ]]>
        </if>
        order by oi.id desc
        limit #{param.offset}, #{param.pageSize}
    </select>

    <!-- orderDeliveryRecordMapper.searchDeliveryAllPage 衍生 -->
    <select id="searchDeliveryAllPageByOrderNos"
            resultType="cn.hydee.middle.business.order.dto.rsp.SearchDeliveryRspDto">
        select
        oi.order_no,oi.order_state,oi.third_platform_code,oi.third_order_no,oi.online_store_code,oi.online_store_name,oi.organization_code,
        oi.organization_name,oi.lock_flag
        from order_delivery_record odr
        join order_info oi on oi.order_no = odr.order_no
        where oi.order_no in
        <foreach collection="orderNos" item="orderNo" index="index" open="(" close=")" separator=",">
            #{orderNo}
        </foreach>
        order by oi.id desc
    </select>

    <update id="updateStateException">
        update order_delivery_record
        set state=#{state,jdbcType=TINYINT},`exception_reason`=#{exceptionReason}
        <if test="deliveryPlatName != null">
        ,delivery_plat_name = #{deliveryPlatName}
        </if>
        <if test="riderName != null">
            ,rider_name = #{riderName}
        </if>
        <if test="riderPhone != null">
            ,rider_phone = #{riderPhone}
        </if>
        <if test="riderAddress != null">
            ,rider_address = #{riderAddress}
        </if>
        where order_no=#{orderNo,jdbcType=BIGINT}
    </update>
    <update id="updateDeliveryRecord">
        update order_delivery_record
        <set>
            <if test="deliveryFeeTotal != null">
                delivery_fee_total=#{deliveryFeeTotal},
            </if>
            <if test="actualDeliveryFee != null">
                actual_delivery_fee=#{actualDeliveryFee},
            </if>
            <if test="riderOrderNo != null">
                rider_order_no=#{riderOrderNo},
            </if>
            <if test="riderName != null">
                rider_name=#{riderName},
            </if>
            <if test="riderPhone != null">
                rider_phone=#{riderPhone},
            </if>
            <if test="riderStaffCode != null">
                rider_staff_code=#{riderStaffCode},
            </if>
            <if test="riderAddress != null">
                rider_address=#{riderAddress},
            </if>
            <if test="state != null">
                state=#{state},
            </if>
            <if test="latitude != null">
                latitude=#{latitude},
            </if>
            <if test="longitude != null">
                longitude=#{longitude},
            </if>
            <if test="callTime != null">
                call_time=#{callTime},
            </if>
            <if test="acceptTime != null">
                accept_time=#{acceptTime},
            </if>
            <if test="pickTime != null">
                pick_time=#{pickTime},
            </if>
            <if test="cancelFrom != null">
                cancel_from=#{cancelFrom},
            </if>
            <if test="cancelReason != null">
                cancel_reason=#{cancelReason},
            </if>
            <if test="cancelDetail != null">
                cancel_detail=#{cancelDetail},
            </if>
            <if test="deliveryPlatName != null">
                delivery_plat_name=#{deliveryPlatName},
            </if>
            <if test="deliveryType != null">
                delivery_type=#{deliveryType},
            </if>
            <if test="cancelFlag != null">
                cancel_flag = #{cancelFlag},
            </if>
            <if test="exceptionReason != null">
                exception_reason = #{exceptionReason},
            </if>
            <if test="logisticsCompany != null">
                logistics_company = #{logisticsCompany},
            </if>
            <if test="logisticsNo != null">
                logistics_no = #{logisticsNo},
            </if>
            <if test="logisticsName != null">
                logistics_name = #{logisticsName},
            </if>
        </set>
        where order_no = #{orderNo, jdbcType=BIGINT}
    </update>

    <update id="updateTCGDeliveryState">
        update order_delivery_record
        set state = #{status} where order_no = #{orderNo}
    </update>

    <update id="updateExtraInfo">
        update order_delivery_record
        set extra_info = #{extraInfo} where order_no = #{orderNo}
    </update>

    <update id="updateDelayState">
        update order_delivery_record
        set delay_state = #{delayState} where order_no = #{orderNo}
    </update>

    <update id="updateUploadFlag">
        update order_delivery_record
        set upload_location_flag = 0 where order_no = #{orderNo}
    </update>
</mapper>
