<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hydee.middle.business.order.mapper.DsStoreFenceSyncConfigMapper">

    <resultMap id="BaseResultMap" type="cn.hydee.middle.business.order.entity.DsStoreFenceSyncConfig">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="storeCode" column="store_code" jdbcType="VARCHAR"/>
            <result property="syncPlatfomList" column="sync_platfom_list" jdbcType="OTHER"/>
            <result property="mtMinPrice" column="mt_min_price" jdbcType="DECIMAL"/>
            <result property="mtDeliveryFee" column="mt_delivery_fee" jdbcType="DECIMAL"/>
            <result property="elmMinPrice" column="elm_min_price" jdbcType="DECIMAL"/>
            <result property="elmDeliveryFee" column="elm_delivery_fee" jdbcType="DECIMAL"/>
            <result property="elmMaxWegiht" column="elm_max_wegiht" jdbcType="BIGINT"/>
            <result property="enable" column="enable" jdbcType="VARCHAR"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
            <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,store_code,sync_platfom_list,
        mt_min_price,mt_delivery_fee,elm_min_price,
        elm_delivery_fee,elm_max_wegiht,enable,
        created_by,updated_by,created_time,
        updated_time
    </sql>
</mapper>
