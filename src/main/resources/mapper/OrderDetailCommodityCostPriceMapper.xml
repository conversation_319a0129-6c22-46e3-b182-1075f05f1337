<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hydee.middle.business.order.mapper.OrderDetailCommodityCostPriceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.hydee.middle.business.order.entity.OrderDetailCommodityCostPrice">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="erp_code" property="erpCode" />
        <result column="make_no" property="makeNo" />
        <result column="batch_no" property="batchNo" />
        <result column="cost_price" property="costPrice" />
        <result column="average_price" property="averagePrice" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

</mapper>
