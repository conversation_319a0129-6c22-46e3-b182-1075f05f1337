<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hydee.middle.business.order.mapper.UserExportColumnMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.hydee.middle.business.order.entity.UserExportColumn">
        <id column="id" property="id" />
        <result column="type" property="type" />
        <result column="user_id" property="userId" />
        <result column="columns" property="columns" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, type, user_id, columns, create_time, modify_time
    </sql>

</mapper>
