<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hydee.middle.business.order.mapper.OrderDeliveryLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.hydee.middle.business.order.entity.OrderDeliveryLog">
        <id column="id" property="id"/>
        <result column="order_no" property="orderNo"/>
        <result column="state" property="state"/>
        <result column="delivery_plat_name" property="deliveryPlatName"/>
        <result column="rider_name" property="riderName"/>
        <result column="rider_phone" property="riderPhone"/>
        <result column="description" property="description"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
    </resultMap>

    <resultMap id="OrderDeliveryLogMap" type="cn.hydee.middle.business.order.dto.rsp.OrderDeliveryLogRspDto"
               extends="BaseResultMap">

    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , order_no, state, delivery_plat_name, rider_name, rider_phone, description, create_time, modify_time
    </sql>

    <select id="selectByOrderNo" resultMap="OrderDeliveryLogMap">
        select
        <include refid="Base_Column_List"></include>
        from order_delivery_log
        where order_no = #{orderNo, jdbcType=BIGINT}
        and state > 0
        order by create_time desc
    </select>
</mapper>
