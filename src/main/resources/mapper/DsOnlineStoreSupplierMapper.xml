<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hydee.middle.business.order.mapper.DsOnlineStoreSupplierRepo">

    <resultMap id="BaseResultMap" type="cn.hydee.middle.business.order.entity.DsOnlineStoreSupplier">
        <id column="id" property="id"/>
        <result column="ectype" property="ectype"/>
        <result column="app_id" property="appId"/>
        <result column="shop_id" property="shop_id"/>
        <result column="shop_name" property="shop_name"/>
        <result column="shop_addr" property="shop_addr"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="phone" property="phone"/>
        <result column="status" property="status"/>
        <result column="shopstatus" property="shopstatus"/>
        <result column="city" property="city"/>
        <result column="out_shop_id" property="out_shop_id"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
    </resultMap>

    <sql id="BaseColumnList">
        `id`,`ectype`,`app_id`,`shop_id`,`shop_name`,`shop_addr`,`longitude`,`latitude`,
        `phone`,`status`,`shopstatus`,`city`,`out_shop_id`,`create_time`,`modify_time`
    </sql>

    <insert id="batchSaveStoreSupplier">
        insert into ds_online_store_supplier
            (`ectype`,`app_id`,`shop_id`,`shop_name`,`shop_addr`,`longitude`,`latitude`,
             `phone`,`status`,`shopstatus`,`city`,`out_shop_id`)
         values
            <foreach collection="params" item="item" index="index" separator=",">
                (#{item.ectype},#{item.appId},#{item.shop_id},#{item.shop_name},#{item.shop_addr},
                #{item.longitude},#{item.latitude},#{item.phone},#{item.status},#{item.shopstatus},#{item.city},#{item.out_shop_id})
            </foreach>
    </insert>

    <update id="batchUpdateStoreSupplier">
        <foreach collection="params" item="item" separator=";">
            update ds_online_store_supplier
            <set>
                <if test="item.shop_name != null and item.shop_name != ''">
                    shop_name = #{item.shop_name},
                </if>
                <if test="item.shop_addr != null and item.shop_addr != ''">
                    shop_addr = #{item.shop_addr},
                </if>
                <if test="item.longitude != null and item.longitude != ''">
                    longitude = #{item.longitude},
                </if>
                <if test="item.latitude != null and item.latitude != ''">
                    latitude = #{item.latitude},
                </if>
                <if test="item.phone != null and item.phone != ''">
                    phone = #{item.phone},
                </if>
                <if test="item.status != null and item.status != ''">
                    status = #{item.status},
                </if>
                <if test="item.shopstatus != null and item.shopstatus != ''">
                    shopstatus = #{item.shopstatus},
                </if>
                <if test="item.city != null and item.city != ''">
                    city = #{item.city},
                </if>
                <if test="item.out_shop_id != null and item.out_shop_id != ''">
                    out_shop_id = #{item.out_shop_id},
                </if>
            </set>
            where ectype = #{item.ectype}
              and shop_id = #{item.shop_id}
        </foreach>
    </update>

    <select id="getShopList" resultType="cn.hydee.middle.business.order.dto.rsp.baseinfo.GetShopListResDto">
        select `shop_id` as shop_id,`shop_name` as shop_name,`shop_addr` as shop_addr,`longitude` as longitude,`latitude` as latitude,
               `phone` as phone,`status` as status,`shopstatus` as shopstatus,`city` as city,`out_shop_id` as out_shop_id
          from ds_online_store_supplier
         where ectype = #{ectype,jdbcType=VARCHAR}
           and app_id = #{appId,jdbcType=VARCHAR}
    </select>

    <select id="getSupplierStoreCount" resultType="java.lang.Integer">
        select count(id)
          from ds_online_store_supplier
         where ectype = #{param.platformCode,jdbcType=VARCHAR}
         <if test="param.appId != null and param.appId != ''">
             and app_id = #{param.appId,jdbcType=VARCHAR}
         </if>
         <if test="param.shopIdList != null and param.shopIdList.size() > 0">
            and shop_id in
            <foreach collection="param.shopIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
         </if>
    </select>

    <select id="getSupplierStorePage" resultMap="BaseResultMap">
        select <include refid="BaseColumnList"/>
          from ds_online_store_supplier
         where ectype = #{param.platformCode,jdbcType=VARCHAR}
         <if test="param.appId != null and param.appId != ''">
            and app_id = #{param.appId,jdbcType=VARCHAR}
         </if>
        <if test="param.shopIdList != null and param.shopIdList.size() > 0">
            and shop_id in
            <foreach collection="param.shopIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by id
    </select>

</mapper>