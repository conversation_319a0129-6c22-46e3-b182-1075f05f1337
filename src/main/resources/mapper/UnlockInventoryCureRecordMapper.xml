<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hydee.middle.business.order.mapper.UnlockInventoryCureRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.hydee.middle.business.order.entity.UnlockInventoryCureRecord">
        <id column="id" property="id" />
        <result column="md5_code" property="md5Code" />
        <result column="mer_code" property="merCode" />
        <result column="third_platform_code" property="thirdPlatformCode" />
        <result column="order_no" property="orderNo" />
        <result column="third_order_no" property="thirdOrderNo" />
        <result column="type" property="type" />
        <result column="param" property="param" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>
    
    <update id="batchUpdateStatusByMd5Codes">
        update unlock_inventory_cure_record 
       	set status=#{status}
           where md5_code IN
        <foreach collection="md5CodeList" item="md5Code" index="index" open="(" close=")" separator=",">
            #{md5Code}
        </foreach>
    </update>
</mapper>
