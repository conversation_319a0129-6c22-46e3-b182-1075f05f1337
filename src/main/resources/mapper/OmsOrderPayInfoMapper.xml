<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hydee.middle.business.order.mapper.doris.OmsOrderPayInfoMapper">

    <sql id="baseTableName">
        <if test=" param.payTableName != null and param.payTableName !='' ">
            ${param.payTableName}
        </if>
        <if test=" param.payTableName == null or param.payTableName =='' ">
            oms_order_pay_info
        </if>
    </sql>

    <sql id="queryPropertyAmount">
      `order_no`  as orderNo,
      `total_amount`  as totalAmount,
      `buyer_actual_amount`  as buyerActualAmount,
      `merchant_actual_amount`  as merchantActualAmount,
      `merchant_delivery_fee`  as merchantDeliveryFee,
      `platform_delivery_fee`  as platformDeliveryFee,
      `merchant_pack_fee`  as merchantPackFee,
      `platform_pack_fee`  as platformPackFee,
      `brokerage_amount`  as brokerageAmount,
      `merchant_discount_sum`  as merchantDiscountSum,
      `platform_discount`  as platformDiscount,
      `discount_fee_dtl`  as discountFeeDtl,
      `r_total_food_amount`  as rTotalFoodAmount,
      `r_shop_refund`  as rShopRefund,
      `r_consumer_refund`  as rConsumerRefund,
      `r_merchant_refund_post_fee`  as rMerchantRefundPostFee,
      `r_merchant_refund_pack_fee`  as rMerchantRefundPackFee,
      `r_platform_refund_pack_fee`  as rPlatformRefundPackFee,
      `r_platform_refund_delivery_fee`  as rPlatformRefundDeliveryFee,
      `r_shop_discount_refund`  as rShopDiscountRefund,
      `r_fee_refund`   as rFeeRefund,
      `r_detail_discount_amount`  as rDetailDiscountAmount,
      `r_platform_discount_refund`  as rPlatformDiscountRefund,
      `commodity_cost_total`  as commodityCostTotal
    </sql>

    <sql id="queryPropertyOrderPay">
        sum(case when order_state >100 then 0 else order_num end ) as availableCount,
        sum(case when order_state >100 then order_num else 0 end ) as unAvailableCount,
        sum(case when order_state >100 then 0 else total_amount + merchant_delivery_fee + platform_delivery_fee + merchant_pack_fee + platform_pack_fee
            - r_total_food_amount-r_merchant_refund_post_fee-r_merchant_refund_pack_fee-r_platform_refund_pack_fee-r_platform_refund_delivery_fee end) orderTotalAmount,
        sum(case when order_state >100 then 0 else buyer_actual_amount-r_consumer_refund end ) as buyerActualAmount,
        sum(case when order_state >100 then 0 else merchant_actual_amount-r_shop_refund end ) as merchantActualAmount,
        sum(case when order_state >100 then 0 else total_amount-r_total_food_amount end ) as commodityTotalAmount,
        sum(case when order_state >100 then 0 else merchant_delivery_fee-r_merchant_refund_post_fee end ) as merchantDeliveryFee,
        sum(case when order_state >100 then 0 else platform_delivery_fee-r_platform_refund_delivery_fee end ) as platformDeliveryFee,
        sum(case when order_state >100 then 0 else merchant_pack_fee-r_merchant_refund_pack_fee end ) as merchantPackFee,
        sum(case when order_state >100 then 0 else platform_pack_fee-r_platform_refund_pack_fee end ) as platformPackFee,
        sum(case when order_state >100 then 0 else brokerage_amount-r_fee_refund end ) as brokerageAmount,
        sum(case when order_state >100 then 0 else merchant_discount_sum-r_shop_discount_refund end ) as merchantDiscountSum,
        sum(case when order_state >100 then 0 else platform_discount-r_platform_discount_refund end ) as platformDiscount,
        sum(case when order_state >100 then 0 else discount_fee_dtl-r_detail_discount_amount end ) as discountFeeDtl,
        sum(case when order_state >100 then 0 else commodity_cost_total end ) as commodityCostTotal
    </sql>

    <sql id="queryWhereByOrderNo">
        <![CDATA[ created_day>= #{param.timeStart} ]]>
        <![CDATA[ and created_day<= #{param.timeEnd} ]]>
        and order_no = #{param.orderNo}
        and order_num = 1
    </sql>

    <sql id="orderByCustom">
        <!-- 支持自定义排序 -->
        <if test=" param.sortType != 1 and param.sortField != null and param.sortField !=''">
            order by
            <bind name="bindSortField" value="param.sortField"></bind>
            ${bindSortField}
            <choose>
                <when test="param.sortSequence =='asc'">
                    asc
                </when>
                <otherwise>
                    desc
                </otherwise>
            </choose>
        </if>
        <if test=" param.sortType == 1 or param.sortField == null or param.sortField =='' ">
            order by merchantActualAmount desc
        </if>
    </sql>

    <sql id="queryWhereOrderPayAllState">
        <![CDATA[ created_day>= #{param.timeStart} ]]>
        <![CDATA[ and created_day<= #{param.timeEnd} ]]>
        and mer_code = #{param.merCode}
        <if test="param.organizationCodeList != null and param.organizationCodeList.size() > 0">
            and organization_code in
            <foreach collection="param.organizationCodeList" item="organizationCode" index="index" open="(" close=")" separator=",">
                #{organizationCode}
            </foreach>
        </if>
        <if test="param.organizationCodeListPart != null and param.organizationCodeListPart.size() > 0">
            or organization_code in
            <foreach collection="param.organizationCodeListPart" item="organizationCode" index="index" open="(" close=")" separator=",">
                #{organizationCode}
            </foreach>
        </if>
        <if test="param.platformCodeList != null and param.platformCodeList.size() > 0">
            and third_platform_code in
            <foreach collection="param.platformCodeList" item="thirdPlatformCode" index="index" open="(" close=")" separator=",">
                #{thirdPlatformCode}
            </foreach>
        </if>
    </sql>

    <select id="queryOrderPayDataByRealTime" resultType="cn.hydee.middle.business.order.doris.dto.OrderPayQueryDataByTimeDto">
        select
            created_hour tradeTime,
            <include refid="queryPropertyOrderPay"></include>
        from
            <include refid="baseTableName"></include>
        where
            <include refid="queryWhereOrderPayAllState"></include>
        group by
            tradeTime
        order by
            tradeTime
    </select>

    <select id="queryOrderPayDataByHistoryTimeDay" resultType="cn.hydee.middle.business.order.doris.dto.OrderPayQueryDataByTimeDto">
        select
            created_day tradeTime,
            <include refid="queryPropertyOrderPay"></include>
        from
            <include refid="baseTableName"></include>
        where
            <include refid="queryWhereOrderPayAllState"></include>
        group by
            tradeTime
        order by
            tradeTime
    </select>

    <select id="queryOrderPayDataByHistoryTimeWeek" resultType="cn.hydee.middle.business.order.doris.dto.OrderPayQueryDataByTimeDto">
        select
            DATE_FORMAT(created,'%x-%v') tradeTime,
            <include refid="queryPropertyOrderPay"></include>
        from
            <include refid="baseTableName"></include>
        where
            <include refid="queryWhereOrderPayAllState"></include>
        group by
            tradeTime
        order by
            tradeTime
    </select>

    <select id="queryOrderPayDataByHistoryTimeMonth" resultType="cn.hydee.middle.business.order.doris.dto.OrderPayQueryDataByTimeDto">
        select
            DATE_FORMAT(created,'%Y-%m') tradeTime,
            <include refid="queryPropertyOrderPay"></include>
        from
            <include refid="baseTableName"></include>
        where
            <include refid="queryWhereOrderPayAllState"></include>
        group by
            tradeTime
        order by
            tradeTime
    </select>

    <select id="queryOrderPayDataByOrg" resultType="cn.hydee.middle.business.order.doris.dto.OrderPayQueryDataByOrgDto">
        select
            organization_code as organizationCode,
            <include refid="queryPropertyOrderPay"></include>
        from
            <include refid="baseTableName"></include>
        where
            <include refid="queryWhereOrderPayAllState"></include>
        group by
            organization_code
            <include refid="orderByCustom"></include>
    </select>

    <select id="queryOrderPayDataByPlatformOnlineStore" resultType="cn.hydee.middle.business.order.doris.dto.OrderPayQueryDataByStoreDto">
        select
            organization_code as organizationCode,
            third_platform_code as platformCode,
            online_store_code as onlineStoreCode,
            <include refid="queryPropertyOrderPay"></include>
        from
            <include refid="baseTableName"></include>
        where
            <include refid="queryWhereOrderPayAllState"></include>
        group by
            organization_code,
            third_platform_code,
            online_store_code
            <include refid="orderByCustom"></include>
    </select>

    <select id="queryPayAmount" resultType="cn.hydee.middle.business.order.doris.entity.OmsOrderPayInfo">
        select
        <include refid="queryPropertyAmount"></include>
        from
        <include refid="baseTableName"></include>
        where
        <include refid="queryWhereByOrderNo"></include>
    </select>

    <select id="queryAllOrderPayDataByOrg" resultType="cn.hydee.middle.business.order.doris.dto.OrderPayQueryDataByOrgDto">
        select
        organization_code as organizationCode,
        <include refid="queryPropertyOrderPay"></include>
        from
        <include refid="baseTableName"></include>
        where
        <include refid="queryWhereOrderPayAllState"></include>
        group by
        organization_code
        <include refid="orderByCustom"></include>
    </select>
</mapper>
