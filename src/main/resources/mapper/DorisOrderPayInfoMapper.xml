<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hydee.middle.business.order.mapper.DorisOrderPayInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.hydee.middle.business.order.doris.entity.DorisOrderPayInfo">
        <id column="id" property="id" />
        <result column="created_day" property="createdDay" />
        <result column="created_hour" property="createdHour" />
        <result column="mer_code" property="merCode" />
        <result column="third_platform_code" property="thirdPlatformCode" />
        <result column="online_store_code" property="onlineStoreCode" />
        <result column="order_no" property="orderNo" />
        <result column="third_order_no" property="thirdOrderNo" />
        <result column="online_store_name" property="onlineStoreName" />
        <result column="created" property="created" />
        <result column="organization_code" property="organizationCode" />
        <result column="organization_name" property="organizationName" />
        <result column="order_state" property="orderState" />
        <result column="total_amount" property="totalAmount" />
        <result column="buyer_actual_amount" property="buyerActualAmount" />
        <result column="merchant_actual_amount" property="merchantActualAmount" />
        <result column="merchant_delivery_fee" property="merchantDeliveryFee" />
        <result column="platform_delivery_fee" property="platformDeliveryFee" />
        <result column="merchant_pack_fee" property="merchantPackFee" />
        <result column="platform_pack_fee" property="platformPackFee" />
        <result column="brokerage_amount" property="brokerageAmount" />
        <result column="merchant_discount_sum" property="merchantDiscountSum" />
        <result column="platform_discount" property="platformDiscount" />
        <result column="discount_fee_dtl" property="discountFeeDtl" />
        <result column="r_total_food_amount" property="rTotalFoodAmount" />
        <result column="r_shop_refund" property="rShopRefund" />
        <result column="r_consumer_refund" property="rConsumerRefund" />
        <result column="r_merchant_refund_post_fee" property="rMerchantRefundPostFee" />
        <result column="r_merchant_refund_pack_fee" property="rMerchantRefundPackFee" />
        <result column="r_platform_refund_pack_fee" property="rPlatformRefundPackFee" />
        <result column="r_platform_refund_delivery_fee" property="rPlatformRefundDeliveryFee" />
        <result column="r_shop_discount_refund" property="rShopDiscountRefund" />
        <result column="r_fee_refund" property="rFeeRefund" />
        <result column="r_detail_discount_amount" property="rDetailDiscountAmount" />
        <result column="r_platform_discount_refund" property="rPlatformDiscountRefund" />
        <result column="order_num" property="orderNum" />
        <result column="commodity_cost_total" property="commodityCostTotal" />
        <result column="tag_prescription" property="tagPrescription" />
        <result column="tag_integral" property="tagIntegral" />
        <result column="tag_medical_insurance" property="tagMedicalInsurance" />
        <result column="tag_appoint" property="tagAppoint" />
        <result column="tag_new_customer" property="tagNewCustomer" />
        <result column="tag_transfer_delivery" property="tagTransferDelivery" />
        <result column="tag_pay_type" property="tagPayType" />
        <result column="tag_prescription" property="tagPrescription" />
        <result column="tag_delivery_type" property="tagDeliveryType" />
        <result column="tag_post_type" property="tagPostType" />
    </resultMap>
    <insert id="saveOrUpdateByDuplicateKey">
        insert into doris_order_pay_info (created_day, created_hour,
        mer_code, organization_code, third_platform_code,
        order_state, online_store_code, order_no,
        third_order_no, organization_name, online_store_name,
        created, total_amount, buyer_actual_amount,
        merchant_actual_amount, merchant_delivery_fee,
        platform_delivery_fee, merchant_pack_fee, platform_pack_fee,
        brokerage_amount, merchant_discount_sum, platform_discount,
        discount_fee_dtl, r_total_food_amount, r_shop_refund,
        r_consumer_refund, r_merchant_refund_post_fee,
        r_merchant_refund_pack_fee, r_platform_refund_pack_fee,
        r_platform_refund_delivery_fee, r_shop_discount_refund,
        r_fee_refund, r_detail_discount_amount, r_platform_discount_refund,
        commodity_cost_total, tag_prescription, tag_integral,
        tag_medical_insurance, tag_appoint, tag_new_customer,
        tag_transfer_delivery, tag_delivery_type, tag_pay_type,
        tag_change_store, tag_post_type, order_num
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.createdDay,jdbcType=DATE}, #{item.createdHour,jdbcType=VARCHAR},
            #{item.merCode,jdbcType=VARCHAR}, #{item.organizationCode,jdbcType=VARCHAR}, #{item.thirdPlatformCode,jdbcType=VARCHAR},
            #{item.orderState,jdbcType=TINYINT}, #{item.onlineStoreCode,jdbcType=VARCHAR}, #{item.orderNo,jdbcType=BIGINT},
            #{item.thirdOrderNo,jdbcType=VARCHAR}, #{item.organizationName,jdbcType=VARCHAR}, #{item.onlineStoreName,jdbcType=VARCHAR},
            #{item.created,jdbcType=TIMESTAMP}, #{item.totalAmount,jdbcType=DECIMAL}, #{item.buyerActualAmount,jdbcType=DECIMAL},
            #{item.merchantActualAmount,jdbcType=DECIMAL}, #{item.merchantDeliveryFee,jdbcType=DECIMAL},
            #{item.platformDeliveryFee,jdbcType=DECIMAL}, #{item.merchantPackFee,jdbcType=DECIMAL}, #{item.platformPackFee,jdbcType=DECIMAL},
            #{item.brokerageAmount,jdbcType=DECIMAL}, #{item.merchantDiscountSum,jdbcType=DECIMAL}, #{item.platformDiscount,jdbcType=DECIMAL},
            #{item.discountFeeDtl,jdbcType=DECIMAL}, #{item.rTotalFoodAmount,jdbcType=DECIMAL}, #{item.rShopRefund,jdbcType=DECIMAL},
            #{item.rConsumerRefund,jdbcType=DECIMAL}, #{item.rMerchantRefundPostFee,jdbcType=DECIMAL},
            #{item.rMerchantRefundPackFee,jdbcType=DECIMAL}, #{item.rPlatformRefundPackFee,jdbcType=DECIMAL},
            #{item.rPlatformRefundDeliveryFee,jdbcType=DECIMAL}, #{item.rShopDiscountRefund,jdbcType=DECIMAL},
            #{item.rFeeRefund,jdbcType=DECIMAL}, #{item.rDetailDiscountAmount,jdbcType=DECIMAL}, #{item.rPlatformDiscountRefund,jdbcType=DECIMAL},
            #{item.commodityCostTotal,jdbcType=DECIMAL}, #{item.tagPrescription,jdbcType=TINYINT}, #{item.tagIntegral,jdbcType=VARCHAR},
            #{item.tagMedicalInsurance,jdbcType=TINYINT}, #{item.tagAppoint,jdbcType=BIT}, #{item.tagNewCustomer,jdbcType=VARCHAR},
            #{item.tagTransferDelivery,jdbcType=BIT}, #{item.tagDeliveryType,jdbcType=TINYINT}, #{item.tagPayType,jdbcType=INTEGER},
            #{item.tagChangeStore,jdbcType=INTEGER}, #{item.tagPostType,jdbcType=INTEGER}, #{item.orderNum,jdbcType=INTEGER}
            )
        </foreach>
        on duplicate key update
        created_day = values(created_day),
        created_hour = values(created_hour),
        mer_code = values(mer_code),
        organization_code = values(organization_code),
        third_platform_code = values(third_platform_code),
        order_state = values(order_state),
        online_store_code = values(online_store_code),
        order_no = values(order_no),
        third_order_no = values(third_order_no),
        organization_name = values(organization_name),
        online_store_name = values(online_store_name),
        created = values(created),
        total_amount = values(total_amount),
        buyer_actual_amount = values(buyer_actual_amount),
        merchant_actual_amount = values(merchant_actual_amount),
        merchant_delivery_fee = values(merchant_delivery_fee),
        platform_delivery_fee = values(platform_delivery_fee),
        merchant_pack_fee = values(merchant_pack_fee),
        platform_pack_fee = values(platform_pack_fee),
        brokerage_amount = values(brokerage_amount),
        merchant_discount_sum = values(merchant_discount_sum),
        platform_discount = values(platform_discount),
        discount_fee_dtl = values(discount_fee_dtl),
        r_total_food_amount = values(r_total_food_amount),
        r_shop_refund = values(r_shop_refund),
        r_consumer_refund = values(r_consumer_refund),
        r_merchant_refund_post_fee = values(r_merchant_refund_post_fee),
        r_merchant_refund_pack_fee = values(r_merchant_refund_pack_fee),
        r_platform_refund_pack_fee = values(r_platform_refund_pack_fee),
        r_platform_refund_delivery_fee = values(r_platform_refund_delivery_fee),
        r_shop_discount_refund = values(r_shop_discount_refund),
        r_fee_refund = values(r_fee_refund),
        r_detail_discount_amount = values(r_detail_discount_amount),
        r_platform_discount_refund = values(r_platform_discount_refund),
        commodity_cost_total = values(commodity_cost_total),
        tag_prescription = values(tag_prescription),
        tag_integral = values(tag_integral),
        tag_medical_insurance = values(tag_medical_insurance),
        tag_appoint = values(tag_appoint),
        tag_new_customer = values(tag_new_customer),
        tag_transfer_delivery = values(tag_transfer_delivery),
        tag_delivery_type = values(tag_delivery_type),
        tag_pay_type = values(tag_pay_type),
        tag_change_store = values(tag_change_store),
        tag_post_type = values(tag_post_type),
        order_num = values(order_num)
    </insert>

</mapper>
