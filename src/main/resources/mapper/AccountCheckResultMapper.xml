<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hydee.middle.business.order.mapper.AccountCheckResultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.hydee.middle.business.order.entity.AccountCheckResult">
        <id column="id" property="id" />
        <result column="mer_code" property="merCode" />
        <result column="third_platform_code" property="thirdPlatformCode" />
        <result column="third_platform_name" property="thirdPlatformName" />
        <result column="online_store_code" property="onlineStoreCode" />
        <result column="online_store_name" property="onlineStoreName" />
        <result column="out_shop_id" property="outShopId" />
        <result column="organization_code" property="organizationCode" />
        <result column="organization_name" property="organizationName" />
        <result column="order_no" property="orderNo" />
        <result column="third_order_no" property="thirdOrderNo" />
        <result column="check_result" property="checkResult" />
        <result column="result_handel_flag" property="resultHandelFlag" />
        <result column="result_handel_type" property="resultHandelType" />
        <result column="merchant_actual_amount_oms" property="merchantActualAmountOms" />
        <result column="merchant_actual_amount_third" property="merchantActualAmountThird" />
        <result column="merchant_actual_amount_diff" property="merchantActualAmountDiff" />
        <result column="total_amount_oms" property="totalAmountOms" />
        <result column="total_amount_third" property="totalAmountThird" />
        <result column="total_amount_diff" property="totalAmountDiff" />
        <result column="platform_discount_oms" property="platformDiscountOms" />
        <result column="platform_discount_third" property="platformDiscountThird" />
        <result column="platform_discount_diff" property="platformDiscountDiff" />
        <result column="merchant_discount_oms" property="merchantDiscountOms" />
        <result column="merchant_discount_third" property="merchantDiscountThird" />
        <result column="merchant_discount_diff" property="merchantDiscountDiff" />
        <result column="brokerage_amount_oms" property="brokerageAmountOms" />
        <result column="brokerage_amount_third" property="brokerageAmountThird" />
        <result column="brokerage_amount_diff" property="brokerageAmountDiff" />
        <result column="platform_delivery_fee_oms" property="platformDeliveryFeeOms" />
        <result column="platform_delivery_fee_third" property="platformDeliveryFeeThird" />
        <result column="platform_delivery_fee_diff" property="platformDeliveryFeeDiff" />
        <result column="merchant_delivery_fee_oms" property="merchantDeliveryFeeOms" />
        <result column="merchant_delivery_fee_third" property="merchantDeliveryFeeThird" />
        <result column="merchant_delivery_fee_diff" property="merchantDeliveryFeeDiff" />
        <result column="platform_pack_fee_oms" property="platformPackFeeOms" />
        <result column="platform_pack_fee_third" property="platformPackFeeThird" />
        <result column="platform_pack_fee_diff" property="platformPackFeeDiff" />
        <result column="merchant_pack_fee_oms" property="merchantPackFeeOms" />
        <result column="merchant_pack_fee_third" property="merchantPackFeeThird" />
        <result column="merchant_pack_fee_diff" property="merchantPackFeeDiff" />
        <result column="account_time" property="accountTime" />
        <result column="order_time" property="orderTime" />
        <result column="bill_time" property="billTime" />
        <result column="job_id" property="jobId" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>
    
    <resultMap id="SummaryResultMap" type="cn.hydee.middle.business.order.entity.AccountCheckResult">
        <result column="order_no" property="orderNo" />
        <result column="check_result" property="checkResult" />
        <result column="merchant_actual_amount_oms" property="merchantActualAmountOms" />
        <result column="merchant_actual_amount_third" property="merchantActualAmountThird" />
        <result column="merchant_actual_amount_diff" property="merchantActualAmountDiff" />
    </resultMap>
    
    <sql id="baseColumns">
        `id`,
		`mer_code`,
		`third_platform_code`,
		`third_platform_name`,
		`online_store_code`, 
		`online_store_name`, 
		`out_shop_id`,
		`organization_code`,
		`organization_name`,
		`order_no`, 
		`third_order_no`, 
		`check_result`, 
		`result_handel_flag`, 
		`result_handel_type`, 
		`merchant_actual_amount_oms`, 
		`merchant_actual_amount_third`,
		`merchant_actual_amount_diff`, 
		`total_amount_oms`, 
		`total_amount_third`, 
		`total_amount_diff`, 
		`platform_discount_oms`, 
		`platform_discount_third`, 
		`platform_discount_diff`, 
		`merchant_discount_oms`, 
		`merchant_discount_third`, 
		`merchant_discount_diff`, 
		`brokerage_amount_oms`, 
		`brokerage_amount_third`, 
		`brokerage_amount_diff`, 
		`platform_delivery_fee_oms`, 
		`platform_delivery_fee_third`, 
		`platform_delivery_fee_diff`, 
		`merchant_delivery_fee_oms`, 
		`merchant_delivery_fee_third`, 
		`merchant_delivery_fee_diff`, 
		`platform_pack_fee_oms`, 
		`platform_pack_fee_third`, 
		`platform_pack_fee_diff`, 
		`merchant_pack_fee_oms`, 
		`merchant_pack_fee_third`, 
		`merchant_pack_fee_diff`, 
		`account_time`,
		`order_time`,
		`bill_time`,
		`job_id`,
		`create_time`,
		`modify_time`
    </sql>
	
	<select id="selectDataForSummary" resultType="cn.hydee.middle.business.order.account.check.base.dto.rsp.SummaryInfo">
		select 
			online_store_code onlineStoreCode,
			online_store_name onlineStoreName,
			third_platform_code platformCode,
			third_platform_name platformName,
			organization_code organizationCode,
			organization_name organizationName,
			count(1 ) validOrderCount,
			count(IF(check_result = 1,1,null)) diffOrderCount,
			sum(merchant_actual_amount_oms) actualAmount,
			sum(merchant_actual_amount_third) platformAmount,
			sum(merchant_actual_amount_diff) diffAmount
		from account_check_result
        where mer_code=#{param.merCode}
        <if test="param.platformCode != null and param.platformCode != ''">
            and third_platform_code=#{param.platformCode}
        </if>
        <if test="param.onlineStoreCode != null and param.onlineStoreCode != ''">
            and online_store_code=#{param.onlineStoreCode}
        </if>
        <if test="param.organizationCodeList != null and param.organizationCodeList.size() > 0">
            and organization_code in
            <foreach collection="param.organizationCodeList" item="organizationCode" index="index" open="(" close=")" separator=",">
                #{organizationCode}
            </foreach>
        </if>
        <if test="param.dataTypeList != null and param.dataTypeList.size() > 0">
            and check_result in
            <foreach collection="param.dataTypeList" item="dataType" index="index" open="(" close=")" separator=",">
                #{dataType}
            </foreach>
        </if>
        <if test="param.orderNo != null and param.orderNo != ''">
            and order_no=#{param.orderNo}
        </if>
        <if test="param.thirdOrderNo != null and param.thirdOrderNo != ''">
            and third_order_no=#{param.thirdOrderNo}
        </if>
        <if test="param.orderTimeStart != null">
            <![CDATA[ and order_time >= #{param.orderTimeStart} ]]>
        </if>
        <if test="param.orderTimeEnd != null">
            <![CDATA[ and order_time <= #{param.orderTimeEnd} ]]>
        </if>
        <if test="param.billTimeStart != null">
            <![CDATA[ and bill_time >= #{param.billTimeStart} ]]>
        </if>
        <if test="param.billTimeEnd != null">
            <![CDATA[ and bill_time <= #{param.billTimeEnd} ]]>
        </if>
        <if test="param.accountTimeStart != null">
            <![CDATA[ and account_time >= #{param.accountTimeStart} ]]>
        </if>
        <if test="param.accountTimeEnd != null">
            <![CDATA[ and account_time <= #{param.accountTimeEnd} ]]>
        </if>
        group by
			online_store_code ,
			third_platform_code ,
			organization_code
        order by online_store_code 
	</select>
	
	<select id="selectSummaryByPage" resultType="cn.hydee.middle.business.order.account.check.base.dto.rsp.SummaryInfo">
		select 
			online_store_code onlineStoreCode,
			online_store_name onlineStoreName,
			third_platform_code platformCode,
			third_platform_name platformName,
			organization_code organizationCode,
			organization_name organizationName,
			count(1 ) validOrderCount,
			count(IF(check_result = 1,1,null)) diffOrderCount,
			sum(merchant_actual_amount_oms) actualAmount,
			sum(merchant_actual_amount_third) platformAmount,
			sum(merchant_actual_amount_diff) diffAmount
		from account_check_result
        where mer_code=#{param.merCode}
        <if test="platformCodeList != null and platformCodeList.size() > 0">
            and third_platform_code in
            <foreach collection="platformCodeList" item="platform" index="index" open="(" close=")" separator=",">
                #{platform}
            </foreach>
        </if>
        <if test="param.onlineStoreCode != null and param.onlineStoreCode != ''">
            and online_store_code=#{param.onlineStoreCode}
        </if>
        <if test="param.organizationCodeList != null and param.organizationCodeList.size() > 0">
            and organization_code in
            <foreach collection="param.organizationCodeList" item="organizationCode" index="index" open="(" close=")" separator=",">
                #{organizationCode}
            </foreach>
        </if>
        <if test="param.dataTypeList != null and param.dataTypeList.size() > 0">
            and check_result in
            <foreach collection="param.dataTypeList" item="dataType" index="index" open="(" close=")" separator=",">
                #{dataType}
            </foreach>
        </if>
        <if test="param.orderNo != null and param.orderNo != ''">
            and order_no=#{param.orderNo}
        </if>
        <if test="param.thirdOrderNo != null and param.thirdOrderNo != ''">
            and third_order_no=#{param.thirdOrderNo}
        </if>
        <if test="param.orderTimeStart != null">
            <![CDATA[ and order_time >= #{param.orderTimeStart} ]]>
        </if>
        <if test="param.orderTimeEnd != null">
            <![CDATA[ and order_time <= #{param.orderTimeEnd} ]]>
        </if>
        <if test="param.billTimeStart != null">
            <![CDATA[ and bill_time >= #{param.billTimeStart} ]]>
        </if>
        <if test="param.billTimeEnd != null">
            <![CDATA[ and bill_time <= #{param.billTimeEnd} ]]>
        </if>
        <if test="param.accountTimeStart != null">
            <![CDATA[ and account_time >= #{param.accountTimeStart} ]]>
        </if>
        <if test="param.accountTimeEnd != null">
            <![CDATA[ and account_time <= #{param.accountTimeEnd} ]]>
        </if>
        group by
			online_store_code ,
			third_platform_code ,
			organization_code
        order by online_store_code 
	</select>
	
	<select id="selectByPage" resultMap="BaseResultMap">
		select 
		<include refid="baseColumns"></include> 
		from account_check_result
        where mer_code=#{param.merCode}
        <if test="param.platformCode != null and param.platformCode != ''">
            and third_platform_code=#{param.platformCode}
        </if>
        <if test="param.onlineStoreCode != null and param.onlineStoreCode != ''">
            and online_store_code=#{param.onlineStoreCode}
        </if>
        <if test="param.organizationCodeList != null and param.organizationCodeList.size() > 0">
            and organization_code in
            <foreach collection="param.organizationCodeList" item="organizationCode" index="index" open="(" close=")" separator=",">
                #{organizationCode}
            </foreach>
        </if>
        <if test="param.dataTypeList != null and param.dataTypeList.size() > 0">
            and check_result in
            <foreach collection="param.dataTypeList" item="dataType" index="index" open="(" close=")" separator=",">
                #{dataType}
            </foreach>
        </if>
        <if test="param.orderNo != null and param.orderNo != ''">
            and order_no=#{param.orderNo}
        </if>
        <if test="param.thirdOrderNo != null and param.thirdOrderNo != ''">
            and third_order_no=#{param.thirdOrderNo}
        </if>
        <if test="param.orderTimeStart != null">
            <![CDATA[ and order_time >= #{param.orderTimeStart} ]]>
        </if>
        <if test="param.orderTimeEnd != null">
            <![CDATA[ and order_time <= #{param.orderTimeEnd} ]]>
        </if>
        <if test="param.billTimeStart != null">
            <![CDATA[ and bill_time >= #{param.billTimeStart} ]]>
        </if>
        <if test="param.billTimeEnd != null">
            <![CDATA[ and bill_time <= #{param.billTimeEnd} ]]>
        </if>
        <if test="param.accountTimeStart != null">
            <![CDATA[ and account_time >= #{param.accountTimeStart} ]]>
        </if>
        <if test="param.accountTimeEnd != null">
            <![CDATA[ and account_time <= #{param.accountTimeEnd} ]]>
        </if>
        order by order_time desc
	</select>
	
	<select id="selectAllData" resultMap="BaseResultMap">
		select 
		<include refid="baseColumns"></include> 
		from account_check_result
        where mer_code=#{param.merCode}
        <if test="param.platformCode != null and param.platformCode != ''">
            and third_platform_code=#{param.platformCode}
        </if>
        <if test="param.onlineStoreCode != null and param.onlineStoreCode != ''">
            and online_store_code=#{param.onlineStoreCode}
        </if>
        <if test="param.organizationCodeList != null and param.organizationCodeList.size() > 0">
            and organization_code in
            <foreach collection="param.organizationCodeList" item="organizationCode" index="index" open="(" close=")" separator=",">
                #{organizationCode}
            </foreach>
        </if>
        <if test="param.dataTypeList != null and param.dataTypeList.size() > 0">
            and check_result in
            <foreach collection="param.dataTypeList" item="dataType" index="index" open="(" close=")" separator=",">
                #{dataType}
            </foreach>
        </if>
        <if test="param.orderNo != null and param.orderNo != ''">
            and order_no=#{param.orderNo}
        </if>
        <if test="param.thirdOrderNo != null and param.thirdOrderNo != ''">
            and third_order_no=#{param.thirdOrderNo}
        </if>
        <if test="param.orderTimeStart != null">
            <![CDATA[ and order_time >= #{param.orderTimeStart} ]]>
        </if>
        <if test="param.orderTimeEnd != null">
            <![CDATA[ and order_time <= #{param.orderTimeEnd} ]]>
        </if>
        <if test="param.billTimeStart != null">
            <![CDATA[ and bill_time >= #{param.billTimeStart} ]]>
        </if>
        <if test="param.billTimeEnd != null">
            <![CDATA[ and bill_time <= #{param.billTimeEnd} ]]>
        </if>
        <if test="param.accountTimeStart != null">
            <![CDATA[ and account_time >= #{param.accountTimeStart} ]]>
        </if>
        <if test="param.accountTimeEnd != null">
            <![CDATA[ and account_time <= #{param.accountTimeEnd} ]]>
        </if>
        order by order_time desc
	</select>
	
</mapper>
