<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hydee.middle.business.order.mapper.OrderDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.hydee.middle.business.order.entity.OrderDetail">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="platform_sku_id" property="platformSkuId" />
        <result column="erp_code" property="erpCode" />
        <result column="bar_code" property="barCode" />
        <result column="commodity_name" property="commodityName" />
        <result column="main_pic" property="mainPic" />
        <result column="commodity_spec" property="commoditySpec" />
        <result column="commodity_count" property="commodityCount" />
        <result column="original_price" property="originalPrice" />
        <result column="price" property="price" />
        <result column="total_amount" property="totalAmount" />
        <result column="discount_amount" property="discountAmount" />
        <result column="actual_amount" property="actualAmount" />
        <result column="discount_share" property="discountShare" />
        <result column="actual_net_amount" property="actualNetAmount" />
        <result column="different_share" property="differentShare" />
        <result column="status" property="status" />
        <result column="manufacture" property="manufacture" />
        <result column="approval_number" property="approvalNumber" />
        <result column="swap_id" property="swapId" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="adjust_amount" property="adjustAmount" />
        <result column="bill_price" property="billPrice" />
        <result column="third_detail_id" property="thirdDetailId" />
        <result column="is_gift" property="isGift" />
        <result column="goods_type" property="goodsType" />
        <result column="refund_count" property="refundCount" />
        <result column="origin_type" property="originType" />
        <result column="st_code" property="stCode" />
        <result column="expect_delivery_time" property="expectDeliveryTime" />
        <result column="direct_delivery_type" property="directDeliveryType"/>
        <result column="original_erp_code" property="originalErpCode"/>
        <result column="chailing" property="chailing"/>
        <result column="first_type_name" property="firstTypeName"/>
        <result column="second_type_name" property="secondTypeName"/>
        <result column="type_name" property="typeName"/>
        <result column="original_erp_code_num" property="originalErpCodeNum"/>
        <result column="chai_ling_original_erp_code" property="chaiLingOriginalErpCode"/>
        <result column="modify_price_diff" property="modifyPriceDiff"/>
        <result column="health_value" property="healthValue" />
        <result column="payment" property="payment" />
        <result column="chai_ling_num" property="chaiLingNum" />
        <result column="detail_discount" property="detailDiscount"/>
        <result column="storage_type" property="storageType"/>
        <result column="is_medicare_item" property="isMedicareItem"/>
        <result column="oms_order_no" property="omsOrderNo"/>
    </resultMap>

    <resultMap id="OrderDetailDomainMap" type="cn.hydee.middle.business.order.domain.OrderDetailDomain" extends="BaseResultMap">
        <collection property="orderPickInfoList"
                     javaType="java.util.List"
                     select="cn.hydee.middle.business.order.mapper.OrderPickInfoMapper.selectListByDetailId"
                     column="{detailId=id}"/>
    </resultMap>

    <sql id="orderDetailColumns">
      `id`,
      `order_no`,
      `platform_sku_id`,
      `erp_code`,
      `bar_code`,
      `commodity_name`,
      `main_pic`,
      `commodity_spec`,
      `commodity_count`,
      `original_price`,
      `price`,
      `total_amount`,
      `actual_amount`,
      `discount_amount`,
      `adjust_amount`,
      `discount_share`,
      `actual_net_amount`,
      `different_share`,
      `status`,
      `manufacture`,
      `approval_number`,
      `swap_id`,
      `bill_price`,
      `third_detail_id`,
      `is_gift`,
      `goods_type`,
      `refund_count`,
      `origin_type`,
      `st_code`,
      `expect_delivery_time`,
      `direct_delivery_type`,
      `original_erp_code`,
      `chailing`,
      `first_type_name`,
      `second_type_name`,
      `type_name`,
      `original_erp_code_num`,
      `chai_ling_original_erp_code`,
      `modify_price_diff`,
      `health_value`,
      `payment`,
      `chai_ling_num`,
      `detail_discount`,
      `storage_type`,
      `is_joint`,
      `drug_type`,
      `old_erp_code`,
      `oms_order_no`,
      `is_medicare_item`,
      `five_class`
  </sql>

    <insert id="insertBatch" useGeneratedKeys="true" keyProperty="list.id" keyColumn="id">
        INSERT INTO order_detail (
        order_no, platform_sku_id, erp_code, bar_code, commodity_name, main_pic,
        commodity_spec, commodity_count, original_price, price, total_amount, discount_amount,
        actual_amount, discount_share, actual_net_amount, different_share, status, manufacture,
        swap_id, bill_price, adjust_amount, third_detail_id, is_gift,goods_type, refund_count,origin_type,
        st_code,expect_delivery_time,direct_delivery_type,detail_discount,storage_type,old_erp_code,approval_number)
        values
        <foreach collection="list" item="item"  separator=",">
            (#{item.orderNo},#{item.platformSkuId},#{item.erpCode},#{item.barCode},#{item.commodityName}, #{item.mainPic},
             #{item.commoditySpec}, #{item.commodityCount}, #{item.originalPrice}, #{item.price}, #{item.totalAmount}, #{item.discountAmount},
             #{item.actualAmount}, #{item.discountShare}, #{item.actualNetAmount}, #{item.differentShare}, #{item.status}, #{item.manufacture},
             #{item.swapId}, #{item.billPrice}, #{item.adjustAmount}, #{item.thirdDetailId}, #{item.isGift}, #{item.goodsType}, #{item.refundCount},
             #{item.originType}, #{item.stCode}, #{item.expectDeliveryTime}, #{item.directDeliveryType},#{item.detailDiscount},#{item.storageType},#{item.oldErpCode},
             #{item.approvalNumber}  )
        </foreach>
    </insert>

    <update id="updateByUniqueKey">
        update order_detail
        <set>
            <if test="status != null">
                status=#{status},
            </if>
            <if test="refundCount != null">
                refund_count=#{refundCount},
            </if>
            <if test="actualAmount != null">
                actual_amount=#{actualAmount},
            </if>
            <if test="discountAmount != null">
                discount_amount=#{discountAmount}
            </if>
        </set>
        where order_no=#{orderNo} and erp_code=#{erpCode} and third_detail_id=#{thirdDetailId}
    </update>


    <select id="selectListByOrderNo"  resultMap="BaseResultMap">
        select
        <include refid="orderDetailColumns"></include>
        from order_detail
        where `order_no`=#{orderNo, jdbcType=BIGINT} and `status` <![CDATA[<]]>10
    </select>

    <select id="selectListWithExchangeByOrderNo"  resultMap="BaseResultMap">
        select
        <include refid="orderDetailColumns"></include>
        from order_detail
        where `order_no`=#{orderNo, jdbcType=BIGINT} and `status` <![CDATA[<=]]>10
    </select>

    <select id="selectAllListByOrderNo" resultMap="BaseResultMap">
        select
        <include refid="orderDetailColumns"></include>
        from order_detail
        where `order_no`=#{orderNo, jdbcType=BIGINT}
        order by id
    </select>

    <select id="selectAllListByOrderNoList" resultMap="BaseResultMap">
        select
        <include refid="orderDetailColumns"></include>
        from order_detail
        where order_no in
        <foreach collection="orderNoList" item="orderNo" index="index" open="(" close=")" separator=",">
            #{orderNo}
        </foreach>
    </select>

    <select id="selectListWithExchangeByOrderNoAndErpCode" resultMap="BaseResultMap">
        select
        <include refid="orderDetailColumns"></include>
        from order_detail
        where `order_no`=#{orderNo, jdbcType=BIGINT} and `erp_code`=#{erpCode} and `status`<![CDATA[<]]>10
    </select>

    <select id="selectByOrderNoAndErpCode" resultMap="BaseResultMap">
      select
      <include refid="orderDetailColumns"></include>
      from order_detail
      where `order_no`=#{orderNo, jdbcType=BIGINT} and `erp_code`=#{erpCode}
    </select>


    <select id="selectDetailDomainListByOrderNo" resultMap="OrderDetailDomainMap">
        select
        <include refid="orderDetailColumns"></include>
        from order_detail
        where `order_no`=#{orderNo, jdbcType=BIGINT} and `status`<![CDATA[<]]>10
        order by id
    </select>

    <select id="selectNormalDetailListByOrderNo" resultMap="BaseResultMap">
        select
        <include refid="orderDetailColumns"></include>
        from order_detail
        where `order_no`=#{orderNo, jdbcType=BIGINT} and `status`<![CDATA[<]]>10
        order by id
    </select>

    <select id="selectDetailDomainListAllByOrderNo" resultMap="OrderDetailDomainMap">
        select
        <include refid="orderDetailColumns"></include>
        from order_detail
        where `order_no`=#{orderNo, jdbcType=BIGINT}
        order by id
    </select>

    <update id="updateSwapInfoById">
        update order_detail
        set status=#{status}
        where id=#{id}
    </update>

    <update id="updateById">
        update order_detail
        <set>
            <if test="erpCode != null">
                erp_code=#{erpCode},
            </if>
            <if test="commodityName != null">
                commodity_name=#{commodityName},
            </if>
            <if test="barCode != null">
                bar_code=#{barCode},
            </if>
            <if test="commoditySpec != null">
                commodity_spec=#{commoditySpec},
            </if>
            <if test="manufacture != null">
                manufacture=#{manufacture},
            </if>
            <if test="mainPic != null">
                main_pic=#{mainPic},
            </if>
            <if test="swapId != null">
                swap_id=#{swapId},
            </if>
            <if test="commodityCount != null">
                commodity_count=#{commodityCount},
            </if>
            <if test="status != null">
                status=#{status},
            </if>
            <if test="discountShare != null">
                discount_share=#{discountShare},
            </if>
            <if test="actualNetAmount != null">
                actual_net_amount=#{actualNetAmount},
            </if>
            <if test="differentShare != null">
                different_share=#{differentShare},
            </if>
            <if test="billPrice != null">
                bill_price=#{billPrice},
            </if>
            <if test="originType != null">
                origin_type=#{originType},
            </if>
            <if test="stCode != null and stCode !='' ">
                st_code=#{stCode},
            </if>
            <if test="expectDeliveryTime != null">
                expect_delivery_time=#{expectDeliveryTime},
            </if>
            <if test="directDeliveryType != null">
                direct_delivery_type=#{directDeliveryType},
            </if>
            <if test="discountAmount != null">
                discount_amount=#{discountAmount},
            </if>
            <if test="price != null">
                price=#{price},
            </if>
            <if test="chailing != null">
                chailing=#{chailing},
            </if>
            <if test="chaiLingNum != null">
                chai_ling_num=#{chaiLingNum},
            </if>
            <if test="firstTypeName != null">
                first_type_name=#{firstTypeName},
            </if>
            <if test="secondTypeName != null">
                second_type_name=#{secondTypeName},
            </if>
            <if test="typeName != null">
                type_name=#{typeName},
            </if>
            <if test="totalAmount != null">
                total_amount=#{totalAmount},
            </if>
            <if test="modifyPriceDiff != null">
                modify_price_diff=#{modifyPriceDiff},
            </if>
            <if test="refundCount != null">
                refund_count=#{refundCount},
            </if>
        </set>
        where id=#{id,jdbcType=BIGINT}
    </update>

    <update id="updateByIdBatch">
        <foreach collection="param" item="item" index="index" open="" close="" separator=" ">
            update order_detail
            <set>
                <if test="item.barCode != null">
                    bar_code=#{item.barCode},
                </if>
                <if test="item.commoditySpec != null">
                    commodity_spec=#{item.commoditySpec},
                </if>
                <if test="item.manufacture != null">
                    manufacture=#{item.manufacture},
                </if>
                <if test="item.mainPic != null">
                    main_pic=#{item.mainPic},
                </if>
                <if test="item.swapId != null">
                    swap_id=#{item.swapId},
                </if>
                <if test="item.commodityCount != null">
                    commodity_count=#{item.commodityCount},
                </if>
                <if test="item.status != null">
                    status=#{item.status},
                </if>
                <if test="item.discountShare != null">
                    discount_share=#{item.discountShare},
                </if>
                <if test="item.actualNetAmount != null">
                    actual_net_amount=#{item.actualNetAmount},
                </if>
                <if test="item.differentShare != null">
                    different_share=#{item.differentShare},
                </if>
                <if test="item.billPrice != null">
                    bill_price=#{item.billPrice},
                </if>
	            <if test="item.originType != null">
	                origin_type=#{item.originType},
	            </if>
	            <if test="item.stCode != null and item.stCode !='' ">
	                st_code=#{item.stCode},
	            </if>
	            <if test="item.expectDeliveryTime != null">
	                expect_delivery_time=#{item.expectDeliveryTime},
	            </if>
	            <if test="item.directDeliveryType != null">
	                direct_delivery_type=#{item.directDeliveryType},
	            </if>
                <if test="item.goodsType != null">
                    goods_type = #{item.goodsType},
                </if>
            </set>
            where id=#{item.id,jdbcType=BIGINT};
        </foreach>
    </update>

    <update id="batchUpdateStatus">
        update order_detail
        set status=#{status}
        where order_no=#{orderNo}
        and erp_code IN
        <foreach collection="erpCodeList" item="erpCode" index="index" open="(" close=")" separator=",">
            #{erpCode,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="selectGoodsCountInOrder" resultType="java.lang.Integer">
        select count(*) from order_detail od left join order_info oi
        on od.order_no=oi.order_no where oi.mer_code=#{merCode}
        and oi.organization_code=#{organizationCode} and oi.lock_flag=0
        <![CDATA[and oi.order_state<40 and od.erp_code=#{erpCode} ]]>
    </select>

    <select id="selectRefundDetailOrderByOrderNo" resultMap="BaseResultMap">
        select
        <include refid="orderDetailColumns"/>
        from order_detail
        where `order_no`=#{orderNo, jdbcType=BIGINT} and refund_count <![CDATA[>]]> 0
    </select>

    <select id="selectSpecifyDetailListByOrderNo" resultMap="BaseResultMap">
        select
        <include refid="orderDetailColumns"/>
        from order_detail
        where `order_no`=#{orderNo, jdbcType=BIGINT}
        <if test="idList != null and idList.size() > 0">
            and id not in
            <foreach collection="idList" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>

    <update id="batchUpdateBarCode">
        <foreach collection="param" item="item" index="index" open="" close="" separator="">
            update order_detail
            <set>
                <if test="item.barCode != null and item.barCode != ''">
                    set bar_code=#{item.barCode},
                </if>
                <if test="item.mainPic != null and item.mainPic != ''">
                    set main_pic=#{item.mainPic},
                </if>
                <if test="item.manufacture != null and item.manufacture != ''">
                    set manufacture=#{item.manufacture},
                </if>
                <if test="item.commoditySpec != null and item.commoditySpec != ''">
                    set commodity_spec=#{item.commoditySpec},
                </if>
            </set>
            where order_no=#{orderNo} and erp_code = #{item.erpCode}
        </foreach>
    </update>

    <update id="updateByOrderNoErpCode">
        update order_detail
        <set>
            <if test="barCode != null and barCode != ''">
                bar_code=#{barCode},
            </if>
            <if test="commoditySpec != null and commoditySpec != ''">
                commodity_spec=#{commoditySpec},
            </if>
            <if test="manufacture != null and manufacture != ''">
                manufacture=#{manufacture},
            </if>
            <if test="mainPic != null and mainPic != ''">
                main_pic=#{mainPic},
            </if>
            <if test="swapId != null">
                swap_id=#{swapId},
            </if>
            <if test="commodityCount != null">
                commodity_count=#{commodityCount},
            </if>
            <if test="status != null">
                status=#{status},
            </if>
        </set>
        where order_no=#{orderNo} and erp_code=#{erpCode}
    </update>
    <update id="updateCountAndAmount">
        update order_detail set commodity_count = #{commodityCount}, refund_count = #{refundCount}, discount_share = #{discountShare},total_amount = #{totalAmount} where id = #{id}
    </update>

    <delete id="deleteOrderDetailsAndPickInfoByOrderNo">
        delete
            od, opi
        from order_detail od
            left join order_pick_info opi on od.id = opi.order_detail_id
        where od.order_no = #{orderNo}
    </delete>

    <select id="selectObOrderDetailByOrderNo" resultMap="BaseResultMap">
        select
        <include refid="orderDetailColumns"></include>
        from order_detail
        where `order_no`=#{orderNo, jdbcType=BIGINT}
        order by expect_delivery_time
    </select>

    <select id="selectChailingOrderDetail" resultMap="BaseResultMap">
        select
        <include refid="orderDetailColumns"></include>
        from order_detail
        where `order_no`=#{orderNo, jdbcType=BIGINT} and `chai_ling_original_erp_code` is not null and `swap_id` is null
    </select>

    <select id="selectDetailForRefund" resultMap="BaseResultMap">
        select
            od.*
        from
            order_detail od
        where
            od.order_no = #{orderNo}
            and od.erp_code not in (
            select
                rd.erp_code as erp_code
            from
                refund_order ro
            join refund_detail rd on
                ro.refund_no = rd.refund_no
                and ro.state in(10, 20)
                and ro.order_no = #{orderNo}
            )
    </select>

    <select id="selectDetailListByOrderNo" resultMap="BaseResultMap">
        select
        <include refid="orderDetailColumns"></include>
        from order_detail
        where `order_no`=#{orderNo, jdbcType=BIGINT} and `status`<![CDATA[<>]]>10
        order by id
    </select>

    <select id="selectOriDetailList" resultType="cn.hydee.middle.business.order.entity.OrderDetail">
        select
        <include refid="orderDetailColumns"></include>
        from order_detail
        where `order_no`=#{orderNo, jdbcType=BIGINT}
        and swap_id is null
    </select>
    <select id="selectPartRefundListByOrderNo" resultType="cn.hydee.middle.business.order.entity.OrderDetail"
            parameterType="java.lang.Long">
        select id,status,erp_code,commodity_spec,platform_sku_id from order_detail where order_no = #{orderNo}
    </select>
  <select id="selectListByOrderNoV2"
          resultType="cn.hydee.middle.business.order.entity.OrderDetail">
    select
    <include refid="orderDetailColumns"></include>
    from order_detail
    where `order_no` IN
    <foreach collection="orderNos" item="orderNo" open="(" close=")" separator=",">
      #{orderNo}
    </foreach>
    and is_joint != 2
    <if test="status != null">
      and status = #{status}
    </if>
  </select>
    <select id="selectRefundListByOrderNo" resultType="cn.hydee.middle.business.order.entity.OrderDetail"
            parameterType="java.lang.Long">
        select
        <include refid="orderDetailColumns"></include>
        from order_detail
        where `order_no`=#{orderNo, jdbcType=BIGINT} and `status` = 11
    </select>

    <select id="selectByOmsOrderNo" resultType="cn.hydee.middle.business.order.entity.OrderDetail"
            parameterType="java.lang.Long">
        select
        <include refid="orderDetailColumns"></include>
        from order_detail
        where `oms_order_no`=#{omsOrderNo, jdbcType=BIGINT} and `goods_type` <![CDATA[<>]]> 4
    </select>
    <select id="selectByOrderNo" resultType="cn.hydee.middle.business.order.entity.OrderDetail"
            parameterType="java.lang.Long">
        select
        <include refid="orderDetailColumns"></include>
        from order_detail
        where `order_no`=#{orderNo, jdbcType=BIGINT} and (`status` = 0 OR `status` = 11)
    </select>
</mapper>
