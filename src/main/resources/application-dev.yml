server:
  port: 12600
api:
  version: 1.0
  version2: 2.0
compensator:
  enabled: true
spring:
  profiles:
    active: dev
    robot-send: true
  application:
    name: hydee-business-order
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    nacos:
      discovery:
        server-addr: http://10.4.3.210:8848;
        namespace: fca9f3f3-c2a7-49ad-a3ea-d5fe988ceb30
        metadata:
          department: NR
        register-enabled: false
  datasource:
    dynamic:
      primary: ordermaster
      strict: false
      datasource:
        ordermaster:
          url: *****************************************************************************************************  #开发
          username: agent
          password: FgRdxNn8ADFC
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
          druid:
            initial-size: 10
            min-idle: 10
            max-active: 50
            max-wait: 60000
            wall:
              multi-statement-allow: true
        orderslave:
          url: *****************************************************************************************************  #开发
          username: agent
          password: FgRdxNn8ADFC
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
          druid:
            initial-size: 10
            min-idle: 10
            max-active: 50
            max-wait: 60000
            wall:
              multi-statement-allow: true
        orderslave2:
          url: *****************************************************************************************************  #开发
          username: agent
          password: FgRdxNn8ADFC
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
          druid:
            initial-size: 10
            min-idle: 10
            max-active: 50
            max-wait: 60000
            wall:
              multi-statement-allow: true
        orderdoris:
          url: ******************************,10.100.6.224:9030,10.100.6.194:9030/doris_hydee_nr?allowMultiQueries=true&serverTimezone=Asia/Shanghai&useSSL=false  #开发
          username: hydee_nr
          password: hydee_nr123
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
          druid:
            initial-size: 10
            min-idle: 10
            max-active: 50
            max-wait: 60000
            wall:
              multi-statement-allow: true
        orderynoms:
          url: **************************************************************************************************  #开发
          username: root
          password: hxyxt-2018
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
          druid:
            initial-size: 10
            min-idle: 10
            max-active: 50
            max-wait: 60000
            wall:
              multi-statement-allow: true
    druid:
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
  elasticsearch:
    rest:
      uris: http://**********:9200
  rabbitmq:
    host: **********
    port: 5672
    username: hydee
    password: ${myEncrypt.des(a8e466da4799f953e0c13f9c1eb55a5fa73114bc359f18d4d809e27dbd7f883e)}
    virtual-host: /business-order
    dead-consumer-time:
      unlock-inventory:
        ready-one: 10000
        ready-two: 20000
        ready-three: 30000
        ready-four: 40000
      refund-front:
        ready-one: 15000
        ready-two: 25000
        ready-three: 35000
        ready-four: 45000


  redis:
    password: yxt_redis123
    lettuce:
      pool:
        max-idle: 200
        min-idle: 50
        max-active: 5000
        max-wait: 1000
    timeout: 2000
    cluster:                                                                                # 此处新增
      nodes: **********:9000,**********:9001,**********:9002,**********:9003,**********:9004,**********:9005     # 此处新增，集群地址（此处为测试环境地址）
      max-redirects: 3
  print:
    mer-codes:
    max-batch-size: 200
  mercodes:
    pick2platform: 888888,444444,666666
    b2cisneedsettlemode: 888888,999999,444444,666666,136664
    queryEs: true

  data:
    mongodb:
      uri: ******************************************************************
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB
  kafka:
    bootstrap-servers: **********:9092 #测试
    producer:
      retries: 3
      batch-size: 16384
      buffer-memory: 33554432
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      topic-order-pay: topic_business_cloud_order_pay_dev
      topic-order-bill: topic_business_cloud_order_bill_dev
      topic-erp-order: topic_business_cloud_erp_order_dev
      topic-erp-order-detail: topic_business_cloud_erp_order_detail_dev
    consumer:
      auto-offset-reset: earliest
      enable-auto-commit: false
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    listener:
      ack-mode: manual
      log-container-config: false
    topic-name: topic_business_cloud_canal_dev
    orderdealtopic-name: orderdeal_topic_dev
    topic-send-es: topic_send_es_dev

  #套餐续费订单拦截
  orderfilter:
    enabled: false
  prescription:
    open: true
    merCodes: 999999
    callback: http://hydee-business-order.svc.k8s.dev.ydjia.cn/1.0/ds/prescription/prescription/callback

mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: cn.hydee.middle.business.order.entity
  configuration:
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    db-config:
      logic-delete-value: 0
      logic-not-delete-value: 1
      update-strategy: not_null

management:
  endpoint:
    mappings:
      enabled: true
    httptrace:
      enabled: true
  endpoints:
    web:
      exposure:
        include: ["*"]
  health:
    mongo:
      enabled: false
  metrics:
    distribution:
      percentiles-histogram[http.server.requests]: true
      maximum-expected-value[http.server.requests]: 10000 #预期最大值
      minimum-expected-value[http.server.requests]: 1 #预期最小值

swagger:
  enable: true


web-log-filter:
  excluded-ant-pattern-uris:
    - /file/_upload*

verification:
  timeout-min: 5

rocketmq:
  name-server: **********:9876
  producer:
    group: PGROUP_${spring.application.name}_${spring.profiles.active}
    customized-trace-topic: TOPIC_BUSINESS_CLOUD_DATA_DEV
    send-message-timeout: 300000
    compress-message-body-threshold: 4096
    max-message-size: 4194304
    retry-times-when-send-failed: 2
    retry-times-when-send-async-failed: 0
    retry-next-server: true
    batch-max-size: 4

message-notify:
  data-topic: TOPIC_BUSINESS_CLOUD_DATA_DEV
  client-topic: TOPIC_BUSINESS_CLOUD_CLIENT_DEV
  market-topic: TOPIC_MIDDLE_MARKET_DATA_DEV
  inner-topic: TOPIC_BUSINESS_CLOUD_INNER_DEV
  cost-price-topic: TOPIC_BUSINESS_CLOUD_COST_PRICE_DEV
  push-to-ws-topic: TOPIC_BUSINESS_CLOUD_PUSH_TO_WS_DEV
  push-to-assist-topic: TOPIC_BUSINESS_CLOUD_PUSH_TO_ASSIST_DEV
  receive-from-ws-topic: TOPIC_BUSINESS_CLOUD_RECEIVE_FROM_WS_DEV
  order-update-topic: GROUP_ORDER_UPDATE_MESSAGE_DEV
  delay-task-topic: TOPIC_BUSINESS_CLOUD_DELAY_TASK_MESSAGE_DEV
  b2c-order-topic: TOPIC_BUSINESS_CLOUD_B2C_ORDER_MESSAGE_DEV
  b2c-refund-topic: TOPIC_BUSINESS_CLOUD_B2C_REFUND_MESSAGE_DEV
  b2c-cancel-order-topic: TOPIC_BUSINESS_CLOUD_B2C_CANCEL_ORDER_MESSAGE_DEV
  order-recover-topic: TOPIC_BUSINESS_CLOUD_ORDER_RECOVER_MESSAGE_DEV
  srm-order-topic: TOPIC_BUSINESS_CLOUD_O2O_SRM_ORDER_DEV
  event-tracking-topic: TOPIC_BUSINESS_CLOUD_EVENT_TRACKING_DEV
  supplier-store-topic: TOPIC_BUSINESS_CLOUD_SUPPLIER_STORE_DEV
  pick-info-topic: TOPIC_BUSINESS_CLOUD_PICK_INFO_DEV
  cure-pick-info-topic: TOPIC_BUSINESS_CLOUD_CURE_PICK_INFO_DEV
  commodity-create-callback-topic: TOPIC_BUSINESS_CLOUD_COMMODITY_CREATE_CALLBACK_DEV
  commodity-exception-analyse-topic: TOPIC_COMMODITY-EXCEPTION-ANALYSE_DEV
  client-delete-sync-topic: DEL_ONLINE_STORE_TOPIC_DEV
  message-center-topic: TOPIC_MERCHANT_PLATFORM_DATA_DEV
  o2o-order-middle-topic: BUSINESS_CLOUD_O2O_ORDER_MIDDLE_MESSAGE
  o2o-refund-order-middle-topic: BUSINESS_CLOUD_O2O_REFUND_ORDER_MIDDLE_MESSAGE
  store-auto-config-topic: TOPIC_BUSINESS_STORE_AUTO_CONFIG
  client-config-notify-merchandise-topic: MERCH_DS_BATCH_DOWN_TOPIC
  store-info-change-topic: TOPIC_MIDDLE_DATA_DEV
  prescription-approve-topic: TOPIC_OMS_PRESCRIPTION_NOTICE
  prescription-approve-result-topic: OMS_PRESCRIPTION_APPROVE_RESULT
  pay-config-topic: TP_PAYMENT_PAY-FINANCE_UPDATE_STATE_DEV
  consumer:
    name-server: **********:9876
    environment: TEST
    retry-count: 4
    charset: utf-8
    api-version: 1.0
    multi-thread: 10
    batch-max-size: 40

hydee-platform:
  url:
    net: https://dev-mall.hxyxt.com/ds-gateway/api/cloud #http://*************:5050/api/cloud
    unified: https://dev-mall.hxyxt.com/ds-gateway/api/cloud
  b2c-url:
    net: https://dev-mall.hxyxt.com/ds-gateway/api/cloud   #测试
    serverurl: http://**************:30002/EcDb/EcDbService.svc #线上

assitNotifyLink:
  pickOvertimeLink: "https://merchants.dev.ydjia.cn/cloud/o2o/order/manage?type=20&orderid={orderNO}"
  deliveryOvertimeLink: "https://merchants.dev.ydjia.cn/cloud/o2o/order/manage?type=30&orderid={orderNO}"
  waitBillLink: "https://merchants.dev.ydjia.cn/cloud/o2o/order/manage?type=200"
  #printDisconnect:

feign:
  hystrix:
    enabled: true
  okhttp:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 60000
        readTimeout: 60000
        loggerLevel: full
      customer-config:
        connectTimeout: 6000
        readTimeout: 6000
        loggerLevel: full
ribbon:
  ConnectTimeout: 60000
  ReadTimeout: 60000
  MaxAutoRetries: 0
  MaxAutoRetriesNextServer: 0
hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            strategy: SEMAPHORE
            timeoutInMilliseconds: 60000

ecs: # 老电商平台ecs, 新电商平台 hems
  api:
    version: 2.0 # 版本
    app-secret: aHlkZWVoZWNtczEwMDg= # 秘钥
    server: http://dev.ebus.hydee.cn/hydee_guanwang # 服务器地址
    ec-code: 9998 # 平台编码：888
    group-id: 1008 # 企业编码：1090
    ol-shop-id: 100841 # 网店编码：109006

aliyun:
  oss:
    endpoint: oss-cn-shanghai.aliyuncs.com
    #多个bucket用逗号隔开，有多少个它就是注册多少ossFileService到spring 容器中去，
    #bean名字是bucket 名字的驼峰方式+"OssFileService"，例如oms-2018-dev=oms2018DevOssFileService"。
    bucket-name: yxt-centermerchant-test
    grant-expire-time: 1800
    #限制上传文件大小，单位为M
    grant-max-content-length: 100
    access-key-id: LTAI5tJTUtwYtpTnLzrWV7Bd
    access-key-secret: ******************************
    #client有默认配置.
    client:
      max-connections: 1024
      socket-timeout: 50000
      connection-timeout: 50000
      connection-request-timeout: 3000
      idle-connection-time: 600000
      max-error-retry: 3
      support-cname: true
      sld-enabled: false
      protocol: https
      user-agent: aliyun-sdk-java
weixin:
  robot-url: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=1a91ec3a-27a0-4302-a5b8-a54b4d587bba # 企业微信机器人地址

erp:
  hcloud:
    url: http://open-dev.hcloud.hydee.cn/gateway
  receive-timeout-ttl: 60
  receive-timeout-count: 5

oms-es:
  es-order-shards: 3
  es-order-replicas: 0
  es-batch-size: 2000
  es-onlineorder-shards: 3
  es-onlineorder-replicas: 0
  es-onlineorder-batch-size: 2000

threadpool:
  logCoreSize: 1
  logMaxSize: 3
  logKeepAliveTime: 1000
  logCapacity: 3000
  erpCoreSize: 10
  erpMaxSize: 10
  erpKeepAliveTime: 100
  erpCapacity: 3000
  ynOrderMoveCoreSize: 4
  ynOrderMoveMaxSize: 8
  ynOrderMoveKeepAliveTime: 100
  ynOrderMoveCapacity: 64
  storeAutoConfigCoreSize: 8
  storeAutoConfigMaxSize: 32
  storeAutoConfigKeepAliveTime: 0
  storeAutoConfigCapacity: 256

beyondHandle: 600 # 秒
callRiderMins: 60 #分
syncOrderInfo: dayNum,selfVerifyCode
prescriptionPlatList: 1001,24
ordercount:
  queryapi: 2.0

timeSpanHandle:
  erpCheck:    # erp对账
    timeSpan: 8          # 小时
    defaultDuration: 24  # 一天
  serviceReport:  # 门店服务报表
    timeSpan: 8          # 小时
    defaultDuration: 24  # 一天

responsible:
  responsibleStaffs:
    hydee-middle-order: 订单中心组 徐思宇(2800)
    hydee-business-order: OMS订单组 楚锋(2910)
    ydjia-merchant-promote: 活动中心组 罗明(2227)
    ydjia-merchant-manager: 微商城B端组 徐思宇(2800)
    ydjia-merchant-customer: 微商城C端组 徐思宇(2800)
    hydee-middle-merchandise: 商品中心组 罗盈龙(2729)
    hydee-merchandise-data-process: 商品同步组 罗盈龙(2729)
    hydee-merchandise-third: 商品第三方组 罗盈龙(2729)
    hydee-middle-baseinfo: 商户中心组 樊映朝(2415)
    hydee-middle-payment: 支付中心组 徐思宇(2800)
    ydjia-operate: 运营平台组 杨光亮(2478)
    ydjia-srm-delivery: 供应商发货 罗盈龙(2729)
    hydee-hems: 三方平台组 孟晋羽123(2282)
    honey-medical: 小蜜组 樊映朝(2415)
    middle-datasync-message: ERP组 456 唐海波(2057) 熊毅(2095)

zero-limit:
  platform-codes: 27,1001

canal:
  filter: dscloud.doris_order_pay_info,dscloud.doris_order_bill_info
  custom: true
  schemaName: dscloud
  pay-table: doris_order_pay_info
  bill-table: doris_order_bill_info
  doris-pay-table: oms_order_pay_info_dev
  doris-bill-table: oms_order_bill_info_dev
sensitive:
  scope:
    addUpdates[0]: cn.hydee.middle.business.order.mapper.OrderDeliveryAddressMapper.updateRecord
    addUpdates[1]: cn.hydee.middle.business.order.mapper.OrderDeliveryAddressMapper.insert
    addUpdates[2]: cn.hydee.middle.business.order.mapper.OrderPrescriptionMapper.insertBatch
    addUpdates[3]: cn.hydee.middle.business.order.mapper.OrderPrescriptionMapper.updateByOrderNo
    addUpdates[4]: cn.hydee.middle.business.order.mapper.OrderPrescriptionMapper.updateByBatch
    selects[0]: cn.hydee.middle.business.order.mapper.OrderDeliveryAddressMapper.selectByOrderNo
    selects[1]: cn.hydee.middle.business.order.mapper.OrderDeliveryAddressMapper.selectList
    selects[2]: cn.hydee.middle.business.order.mapper.OrderPrescriptionMapper.selectListByOrderNo
    selects[3]: cn.hydee.middle.business.order.mapper.OrderPrescriptionMapper.queryNeedDealPrescriptionList
    selects[4]: cn.hydee.middle.business.order.mapper.OrderPrescriptionMapper.selectList
    selects[5]: cn.hydee.middle.business.order.mapper.OrderInfoExportMapper.listOverall
    selects[6]: cn.hydee.middle.business.order.mapper.OrderDeliveryAddressMapper.selectByOrderNoList
    selects[7]: cn.hydee.middle.business.order.mapper.OrderPrescriptionMapper.selectListByOrderNoList
    selects[8]: cn.hydee.middle.business.order.mapper.OrderInfoExportMapper.listOverallByOrderNos

#套餐续费鉴权
oms:
  auth:
    userAccount: 3396
    password: XyADISG3

queryes:
  indexname: basedata_dev
  indexname2: basedata_dev
  doubleWrite: false

match:
  nostore:
    days: 3

call-rider:
  delay-open-flag: true

order-diff:
  open-flag: true

#门店相关配置  天猫、拼多多、京东、药房网、平安健康、饿百、微商城
serverurl:
  url3002: http://**************:30002/EcDb/EcDbService.svc
  url3003: http://pdd.hydee.cn/router/api
  url3004: http://**************/router/api
  url3006: https://api.yaofangwang.com/api_gateway.ashx
  url3001: https://openapi.jk.cn/api/v1/
  url24: https://api-be.ele.me/
  url43: https://middle.hydee.cn/businesses-gateway/datasync
  url9004: https://open.douyin.com

openapi:
  huditTool:
    isEncrypt: http://upgrade-platform-cloud.test.hydee.cn/openapi/huditTool/isEncrypt
isEncrypt: 0

hc:
  prescription:
    url: http://hydee-prescription-api.svc.k8s.dev.hcloud.hydee.cn

jddj:
  b2c:
    merCodes: 666666



business:
  order:
    ynoms:
      url: http://omsapitest.yxtmart.cn/   #雨诺OMS地址
    kcpos:
      callback:
        url: http://omsapitest.yxtmart.cn/  #科传Pos回调接口地址
alarm:
  robot:
    # 是否开启机器人告警，默认开启；非必填
    enable: true
    # 值班人手机号，英文逗号分隔；非必填
    oncallMobile: ***********,***********



#门店缓存时间
storeExpireTime: 5
cluster:
  query:
    es:
      open: true
      merCodes:

#导出Excel最大行数
exportExcelMaxRow: 20000

#是否需要切店
isNeedOpenNew: true
#推送到药师云审方 目前之推送饿了么和京东到家
approvePrescriptionPlatform: 11,24
#待审方订单 打印小票和来单提示延后
print-sound-delay: true

xxl:
  job:
    admin:
      addresses: https://dev-xxl-job-new.hxyxt.com/xxl-job-admin/
    executor:
      appname:
      port:
      ip:
      address:
      logpath: ${user.dir}/logs
      logretentiondays: 7
    accessToken: sk_token