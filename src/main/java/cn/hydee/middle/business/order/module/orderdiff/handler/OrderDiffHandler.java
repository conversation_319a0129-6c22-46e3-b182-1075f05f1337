package cn.hydee.middle.business.order.module.orderdiff.handler;

import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.entity.OrderPayInfo;

/**
* @Author: syuson
* @Date: 2021-12-2
*/
public interface OrderDiffHandler {

    /**
    * @Description: 新旧金额比对，如果存在差异，则重新计算财务数据、下账数据
    * @Param: [orderNo]
    * @return: void
    * @Author: syuson
    * @Date: 2021-12-9
    */
    boolean handle(Long orderNo);

    /**
    * @Description: 重新计算规则
    * @Param: [orderInfo, newOrderPayInfo, storeBillConfig]
    * @return: void
    * @Author: syuson
    * @Date: 2021-12-9
    */
    void reCalculateAmount(OrderInfo orderInfo, OrderPayInfo newOrderPayInfo);
}
