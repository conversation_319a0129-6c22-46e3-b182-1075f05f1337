package cn.hydee.middle.business.order.account.check.service.impl;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.Enums.OrderStateEnum;
import cn.hydee.middle.business.order.Enums.PlatformCodeEnum;
import cn.hydee.middle.business.order.account.check.base.dto.req.QueryOrganizationServiceReportReqDto;
import cn.hydee.middle.business.order.account.check.base.dto.req.QueryStoreServiceReportReqDto;
import cn.hydee.middle.business.order.account.check.batch.list.BatchListHandler;
import cn.hydee.middle.business.order.account.check.service.IStoreServiceReportService;
import cn.hydee.middle.business.order.dto.MerOrganizationCode;
import cn.hydee.middle.business.order.dto.StoreServiceReportAllSnapshotDto;
import cn.hydee.middle.business.order.dto.StoreServiceReportExportReqDto;
import cn.hydee.middle.business.order.entity.DsOnlineStore;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.entity.OrganizationServiceReport;
import cn.hydee.middle.business.order.entity.StoreServiceReport;
import cn.hydee.middle.business.order.mapper.DsOnlineStoreRepo;
import cn.hydee.middle.business.order.mapper.OrderInfoMapper;
import cn.hydee.middle.business.order.mapper.OrderLogMapper;
import cn.hydee.middle.business.order.mapper.StoreServiceReportMapper;
import cn.hydee.middle.business.order.service.CommodityExceptionOrderService;
import cn.hydee.middle.business.order.util.DateUtil;
import cn.hydee.middle.business.order.util.Sequence;
import cn.hydee.middle.business.order.v2.manager.OrderPickManager;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2021年04月14日 16:55
 */
@Slf4j
@Service
public class StoreServiceReportServiceImpl extends ServiceImpl<StoreServiceReportMapper, StoreServiceReport> implements IStoreServiceReportService {
    @Autowired
    private DsOnlineStoreRepo dsOnlineStoreRepo;
    @Autowired
    private OrderInfoMapper orderInfoMapper;
    @Autowired
    private CommodityExceptionOrderService commodityExceptionOrderService;
    @Autowired
    private OrderLogMapper orderLogMapper;
    @Autowired
    private OrderPickManager orderPickManager;

    Sequence<StoreServiceReport> storeComp = Sequence.create(new Sequence<>(), (v) -> {
        v.initComp(StoreServiceReport::getOrganizationCode, Comparator.comparing(StoreServiceReport::getOrganizationCode));
        v.initComp(StoreServiceReport::getOnlineStoreCode, Comparator.comparing(StoreServiceReport::getOnlineStoreCode));
        v.initComp(StoreServiceReport::getValideOrders, Comparator.comparing(StoreServiceReport::getValideOrders));
        v.initCustomComp(StoreServiceReport::getAveragePickMins, Comparator.comparing(StoreServiceReport::convertAveragePickMinsAsc), Comparator.comparing(StoreServiceReport::convertAveragePickMinsDesc));
        v.initComp(StoreServiceReport::getPickOvertimeOrders, Comparator.comparing(StoreServiceReport::getPickOvertimeOrders));
        v.initCustomComp(StoreServiceReport::getAverageDeliveryMins, Comparator.comparing(StoreServiceReport::convertAverageDeliveryMinsAsc), Comparator.comparing(StoreServiceReport::convertAverageDeliveryMinsDesc));
        v.initComp(StoreServiceReport::getDeliveryOvertimeOrders, Comparator.comparing(StoreServiceReport::getDeliveryOvertimeOrders));
        v.initComp(StoreServiceReport::getInvalideOrders, Comparator.comparing(StoreServiceReport::getInvalideOrders));
        v.initComp(StoreServiceReport::getLackCommidityOrders, Comparator.comparing(StoreServiceReport::getLackCommidityOrders));
        v.initCustomComp(StoreServiceReport::getDeliveryOvertimePercent, Comparator.comparing(StoreServiceReport::convertDeliveryOvertimePercentAsc), Comparator.comparing(StoreServiceReport::convertDeliveryOvertimePercentDesc));
        v.initCustomComp(StoreServiceReport::getPickOvertimePercent, Comparator.comparing(StoreServiceReport::convertPickOvertimePercentAsc), Comparator.comparing(StoreServiceReport::convertPickOvertimePercentDesc));
        v.initCustomComp(StoreServiceReport::getInvalidOrdePercent, Comparator.comparing(StoreServiceReport::convertInvalidOrdePercentAsc), Comparator.comparing(StoreServiceReport::convertInvalidOrdePercentDesc));
        v.initCustomComp(StoreServiceReport::getLackCommidityPercent, Comparator.comparing(StoreServiceReport::convertLackCommidityPercentAsc), Comparator.comparing(StoreServiceReport::convertLackCommidityPercentDesc));
    });

    @DS(DsConstants.DB_ORDER_SLAVE_TWO)
    @Override
    public IPage<StoreServiceReport> qryStoreServiceReportByOrg(QueryStoreServiceReportReqDto req) {
        Page<StoreServiceReport> response = new Page<>();
        response.setSize(req.getPageSize());
        response.setCurrent(req.getCurrentPage());
        response.setSearchCount(true);

        ArrayList<StoreServiceReport> records = new ArrayList<>();
        List<StoreServiceReport> storeServiceReports = baseMapper.qryOnlineStoreServiceReportByOrg(req);

        List<DsOnlineStore> merOrgPlatStoreList = dsOnlineStoreRepo.selectDistinctMerOrgPlatStore(req.getMerCode(), req.getOrganizationCodeList());
        Map<String, DsOnlineStore> integrateMap = merOrgPlatStoreList.stream().collect(Collectors.toMap(item -> item.getOrganizationCode() + "_" + item.getPlatformCode() + "_" + item.getOnlineStoreCode(), item -> item, (a, b) -> a));
        for (StoreServiceReport storeServiceReport : storeServiceReports) {
            String key = storeServiceReport.getOrganizationCode() + "_" + storeServiceReport.getThirdPlatformCode() + "_" + storeServiceReport.getOnlineStoreCode();
            if (integrateMap.get(key) != null) {
                storeServiceReport.setOnlineStoreName(integrateMap.get(key).getOnlineStoreName());
                storeServiceReport.setOrganizationName(integrateMap.get(key).getOrganizationName());
                storeServiceReport.calculate();
                integrateMap.remove(key);
            } else {
                DsOnlineStore dsOnlineStore = dsOnlineStoreRepo.selectStore(req.getMerCode(), storeServiceReport.getThirdPlatformCode(), storeServiceReport.getOnlineStoreCode(), null);
                if (dsOnlineStore != null) {
                    storeServiceReport.setOnlineStoreName(dsOnlineStore.getOnlineStoreName());
                }
            }
            if (PlatformCodeEnum.getByCode(storeServiceReport.getThirdPlatformCode()) != null) {
                storeServiceReport.setThirdPlatformName(PlatformCodeEnum.getByCode(storeServiceReport.getThirdPlatformCode()).getType());
            }
        }
        records.addAll(storeServiceReports);

        Stream<StoreServiceReport> stream = records.stream();
        if (req.getSortField() != null && req.getSortSequence() != null && storeComp.getSequence(req.getSortField(), req.getSortSequence()) != null) {
            stream = stream.sorted(storeComp.getSequence(req.getSortField(), req.getSortSequence()));
        } else {
            stream = stream.sorted(storeComp.getSequence("valideOrders", "desc"));
        }

        List<StoreServiceReport> middleResult = stream.collect(Collectors.toList());
        if (!integrateMap.isEmpty()) {
            for (Map.Entry<String, DsOnlineStore> onlineStoreDataEmptyEntry : integrateMap.entrySet()) {
                middleResult.add(buildEmptyStoreEmptyData(onlineStoreDataEmptyEntry.getValue()));
            }
        }

        long id = 0;
        for (StoreServiceReport record : middleResult) {
            record.setId(id);
            id++;
        }

        response.setTotal(middleResult.size());
        List<StoreServiceReport> result = middleResult.stream().skip((req.getCurrentPage() - 1) * req.getPageSize()).limit(req.getPageSize()).collect(Collectors.toList());
        response.setRecords(result);
        return response;
    }

    private StoreServiceReport buildEmptyStoreEmptyData(DsOnlineStore store) {
        StoreServiceReport storeServiceReport = new StoreServiceReport();
        storeServiceReport.setThirdPlatformCode(store.getPlatformCode());
        storeServiceReport.setOrganizationCode(store.getOrganizationCode());
        storeServiceReport.setOrganizationName(store.getOrganizationName());
        storeServiceReport.setOnlineStoreCode(store.getOnlineStoreCode());
        storeServiceReport.setOnlineStoreName(store.getOnlineStoreName());
        storeServiceReport.setThirdPlatformName(PlatformCodeEnum.getByCode(store.getPlatformCode()).getType());
        storeServiceReport.setAverageDeliveryMins("-");
        storeServiceReport.setAveragePickMins("-");
        storeServiceReport.setDeliveryOvertimePercent("-");
        storeServiceReport.setInvalidOrdePercent("-");
        storeServiceReport.setLackCommidityPercent("-");
        storeServiceReport.setPickOvertimePercent("-");
        return storeServiceReport;
    }

    @Override
    public int countReport(QueryStoreServiceReportReqDto reqDTO) {
        return baseMapper.countReport(reqDTO);
    }

    @Override
    public List<StoreServiceReport> qryOnlineStoreServiceReportByOrg(StoreServiceReportExportReqDto reqDTO) {
        return baseMapper.qryOnlineStoreServiceReportByOrg(reqDTO);
    }

    /**
     * 单个线下门店 审计
     *
     * @param orderInfos
     */
    @Override
    public StoreServiceReport report(List<OrderInfo> orderInfos, String integrateKey, LocalDateTime statisticsDate, String jobId) {
        StoreServiceReport storeServiceReport = buildStoreServiceReport(integrateKey, statisticsDate, jobId);

        if (CollectionUtils.isEmpty(orderInfos)) {
            return storeServiceReport;
        }

        new BatchListHandler<OrderInfo>().batchResolve(orderInfos, 500, items -> {
            List<Long> orderNoList = items.stream().map(OrderInfo::getOrderNo).collect(Collectors.toList());
            Map<Long, OrderInfo> orderNoInfoMap = items.stream().collect(Collectors.toMap(OrderInfo::getOrderNo, p -> p, (a, b) -> b));

            // 查询缺货订单
            List<Long> orderNos = commodityExceptionOrderService.batchQryLack(orderNoList);
            int lack = CollectionUtils.isEmpty(orderNos) ? 0 : orderNos.size();
            storeServiceReport.addLackCommidityOrders(lack);

            // 拣货超时订单数
            List<Long> orderNoPickOvertime = orderLogMapper.qryPickOvertimeOrder(orderNoList);
            int pickOvertimeCount = CollectionUtils.isEmpty(orderNoPickOvertime) ? 0 : orderNoPickOvertime.size();
            storeServiceReport.addPickOvertimeCount(pickOvertimeCount);

            // 配送超时订单数
            List<Long> orderNoDeliveryOvertime = orderLogMapper.qryDeliveryOvertimeOrder(orderNoList);
            int deliveryOvertimeCount = CollectionUtils.isEmpty(orderNoDeliveryOvertime) ? 0 : orderNoDeliveryOvertime.size();
            storeServiceReport.addDeliveryOvertimeCount(deliveryOvertimeCount);

            int valideOrders = 0;
            long pickedOrders = 0L, pickTimeUsed = 0L;
            for (OrderInfo orderInfo : items) {
                // 有效订单数  排除订单状态为已取消、已关闭的订单数
                if (orderInfo.getOrderState().intValue() <= OrderStateEnum.COMPLETED.getCode().intValue()) {
                    valideOrders++;
                    if (orderInfo.getAcceptTime() == null) {
                        log.warn("storeServiceReport generate orderInfo.getAcceptTime() null orderNo {}", orderInfo.getOrderNo());
                        continue;
                    }
                    // 拣货时长   -- 排除未拣货的订单，排除预约订单, 排除配送预约单
                    if (orderInfo.getPickTime() != null && orderInfo.getAcceptTime() != null && DsConstants.INTEGER_ZERO.equals(orderInfo.getAppointment()) && !orderPickManager.isAppointDelivery(orderInfo)) {
                        pickedOrders = pickedOrders + 1;
                        pickTimeUsed = pickTimeUsed + orderInfo.getPickTime().getTime() - orderInfo.getAcceptTime().getTime();
                    }
                }
            }
            storeServiceReport.addValideOrders(valideOrders);
            storeServiceReport.addPickedOrders(pickedOrders);
            storeServiceReport.addPickTimeUsed(pickTimeUsed / 1000);

            // 无效订单数  = 已关闭、已取消的订单数
            storeServiceReport.addInvalideOrders(items.size() - valideOrders);

            // 平均配送时长
            long deliveriedOrders = 0;
            long deliveryTimeUsed = 0L;
            List<Long> orderNoDeliveryList = orderInfoMapper.qryDeliveryOrders(orderNoList);
            for (Long orderNo : orderNoDeliveryList) {
                OrderInfo orderInfo = orderNoInfoMap.get(orderNo);
                if (!orderPickManager.isAppointDelivery(orderInfo) && orderInfo.getCompleteTime() != null && orderInfo.getPickTime() != null) {
                    deliveryTimeUsed = deliveryTimeUsed + (orderInfo.getCompleteTime().getTime() - orderInfo.getPickTime().getTime());
                    deliveriedOrders++;
                }
            }
            storeServiceReport.addDeliveriedOrders(deliveriedOrders);
            storeServiceReport.addDeliveryTimeUsed(deliveryTimeUsed / 1000);

        });

        return storeServiceReport;

    }

    @Override
    public OrganizationServiceReport qryOrganizationReport(MerOrganizationCode merOrg, String statisticDate) {
        return baseMapper.getOrganizationReport(merOrg, statisticDate);
    }

    @Override
    public void reCountValideOrder(long minDay) {
        LocalDateTime handleDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).minusDays(minDay);
        String statisticDate = handleDay.minusDays(1).format(DateTimeFormatter.ofPattern(DateUtil.DATE_FORMAT_YYYYMMDD));
        Date begin = Date.from(handleDay.minusDays(1).atZone(ZoneId.systemDefault()).toInstant());
        Date end = Date.from(handleDay.atZone(ZoneId.systemDefault()).toInstant());
        List<StoreServiceReport> storeServiceReports = baseMapper.selectStoreReportByStatisticDate(statisticDate);
        for (StoreServiceReport storeServiceReport : storeServiceReports) {
            int validOrderCount = orderInfoMapper.getValidOrderCount(storeServiceReport, begin, end);
            if (validOrderCount != storeServiceReport.getValideOrders()) {
                int totalOrderCount = storeServiceReport.getValideOrders() + storeServiceReport.getInvalideOrders();
                int invalidOrderCount = totalOrderCount - validOrderCount;
                baseMapper.updateValidOrderCount(storeServiceReport, validOrderCount, invalidOrderCount);
            }

        }
    }

    @Override
    public int getValidOrderCount(OrganizationServiceReport organizationServiceReport) {
        return baseMapper.getValidOrderCount(organizationServiceReport);
    }

    @Override
    public void deleteByDay(LocalDateTime statisticDate) {
        // 大表直接用等于符号删除不能走索引，改为删除大于等于 今天 小于明天的写法
        String endDate = statisticDate.plusDays(1).format(DateTimeFormatter.ofPattern(DateUtil.DATE_FORMAT_YYYYMMDD));
        String date = statisticDate.format(DateTimeFormatter.ofPattern(DateUtil.DATE_FORMAT_YYYYMMDD));
        baseMapper.deleteByDay(date,endDate);
    }

    private Sequence<OrganizationServiceReport> comp = Sequence.create(new Sequence<>(), (v) -> {
        v.initComp(OrganizationServiceReport::getOrganizationCode, Comparator.comparing(OrganizationServiceReport::getOrganizationCode));
        v.initComp(OrganizationServiceReport::getValideOrders, Comparator.comparing(OrganizationServiceReport::getValideOrders));
        v.initCustomComp(OrganizationServiceReport::getAveragePickMins, Comparator.comparing(OrganizationServiceReport::convertAveragePickMinsAsc), Comparator.comparing(OrganizationServiceReport::convertAveragePickMinsDesc));
        v.initComp(OrganizationServiceReport::getPickOvertimeOrders, Comparator.comparing(OrganizationServiceReport::getPickOvertimeOrders));
        v.initCustomComp(OrganizationServiceReport::getAverageDeliveryMins, Comparator.comparing(OrganizationServiceReport::convertAverageDeliveryMinsAsc), Comparator.comparing(OrganizationServiceReport::convertAverageDeliveryMinsDesc));
        v.initComp(OrganizationServiceReport::getDeliveryOvertimeOrders, Comparator.comparing(OrganizationServiceReport::getDeliveryOvertimeOrders));
        v.initComp(OrganizationServiceReport::getInvalideOrders, Comparator.comparing(OrganizationServiceReport::getInvalideOrders));
        v.initComp(OrganizationServiceReport::getLackCommidityOrders, Comparator.comparing(OrganizationServiceReport::getLackCommidityOrders));
        v.initCustomComp(OrganizationServiceReport::getPickOvertimePercent, Comparator.comparing(OrganizationServiceReport::convertPickOvertimePercentAsc), Comparator.comparing(OrganizationServiceReport::convertPickOvertimePercentDesc));
        v.initCustomComp(OrganizationServiceReport::getDeliveryOvertimePercent, Comparator.comparing(OrganizationServiceReport::convertDeliveryOvertimePercentAsc), Comparator.comparing(OrganizationServiceReport::convertDeliveryOvertimePercentDesc));
        v.initCustomComp(OrganizationServiceReport::getInvalidOrdePercent, Comparator.comparing(OrganizationServiceReport::convertInvalidOrdePercentAsc), Comparator.comparing(OrganizationServiceReport::convertInvalidOrdePercentDesc));
        v.initCustomComp(OrganizationServiceReport::getLackCommidityPercent, Comparator.comparing(OrganizationServiceReport::convertLackCommidityPercentAsc), Comparator.comparing(OrganizationServiceReport::convertLackCommidityPercentDesc));
    });

    @DS(DsConstants.DB_ORDER_SLAVE_TWO)
    @Override
    public IPage<OrganizationServiceReport> qryOrganizationServiceReport(QueryOrganizationServiceReportReqDto req) {
        //分页数据
        Page<OrganizationServiceReport> pageData = new Page<>();
        pageData.setSize(req.getPageSize());
        pageData.setCurrent(req.getCurrentPage());
        pageData.setSearchCount(true);

        //汇总数据
        StoreServiceReportAllSnapshotDto snapshotDto = new StoreServiceReportAllSnapshotDto();
        List<OrganizationServiceReport> organizationServiceReports = baseMapper.qryOrganizationServiceReport(req).stream()
                .filter((item) -> StringUtils.isNotEmpty(item.getOrganizationCode())).collect(Collectors.toList());
        List<MerOrganizationCode> merOrgs = dsOnlineStoreRepo.selectDistinctMerOrg(req.getMerCode(), req.getOrganizationCodeList());
        Map<String, String> merOrgMap = merOrgs.stream().filter((item) -> StringUtils.isNotEmpty(item.getOrganizationCode()))
                .map(item -> {
                    if (StringUtils.isEmpty(item.getOrganizationName())) {
                        item.setOrganizationName("");
                    }
                    return item;
                })
                .collect(Collectors.toMap(MerOrganizationCode::getOrganizationCode, MerOrganizationCode::getOrganizationName, (a, b) -> a));
        List<String> orgList = merOrgs.stream().filter(item -> StringUtils.isNotEmpty(item.getOrganizationCode())).map(MerOrganizationCode::getOrganizationCode).distinct().collect(Collectors.toList());
        for (OrganizationServiceReport organizationServiceReport : organizationServiceReports) {
            organizationServiceReport.setOrganizationName(merOrgMap.get(organizationServiceReport.getOrganizationCode()));
            organizationServiceReport.calculate();
            snapshotDto.summaryData(organizationServiceReport);
            orgList.remove(organizationServiceReport.getOrganizationCode());
        }
        //汇总数据计算
        snapshotDto.calculate();
        req.setTotalData(snapshotDto);

        Stream<OrganizationServiceReport> stream = organizationServiceReports.stream();
        if (req.getSortField() != null && req.getSortSequence() != null && comp.getSequence(req.getSortField(), req.getSortSequence()) != null) {
            stream = stream.sorted(comp.getSequence(req.getSortField(), req.getSortSequence()));
        } else {
            stream = stream.sorted(comp.getSequence("valideOrders", "desc"));
        }

        List<OrganizationServiceReport> middleResult = stream.collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(orgList)) {
            for (String organizationCode : orgList) {
                middleResult.add(buildEmptyOrganizationServiceReport(req.getMerCode(), organizationCode, merOrgMap.get(organizationCode)));
            }
        }

        long id = 0;
        for (OrganizationServiceReport record : middleResult) {
            record.setId(id);
            id++;
        }

        pageData.setTotal(middleResult.size());
        List<OrganizationServiceReport> result = middleResult.stream().skip((req.getCurrentPage() - 1) * req.getPageSize()).limit(req.getPageSize()).collect(Collectors.toList());
        pageData.setRecords(result);
        return pageData;
    }

    @Override
    public int countOrganizationServiceReport(QueryOrganizationServiceReportReqDto reqDTO) {
        return baseMapper.countOrganizationServiceReport(reqDTO);
    }

    private OrganizationServiceReport buildEmptyOrganizationServiceReport(String merCode, String organizationCode, String organizationName) {
        OrganizationServiceReport organizationServiceReport = new OrganizationServiceReport();
        organizationServiceReport.setMerCode(merCode);
        organizationServiceReport.setOrganizationCode(organizationCode);
        organizationServiceReport.setOrganizationName(organizationName);
        organizationServiceReport.setAverageDeliveryMins("-");
        organizationServiceReport.setAveragePickMins("-");
        organizationServiceReport.setDeliveryOvertimePercent("-");
        organizationServiceReport.setInvalidOrdePercent("-");
        organizationServiceReport.setLackCommidityPercent("-");
        organizationServiceReport.setPickOvertimePercent("-");
        return organizationServiceReport;
    }

    private StoreServiceReport buildStoreServiceReport(String integrateKey, LocalDateTime statisticDate, String jobId) {
        String[] integrate = integrateKey.split("-");
        StoreServiceReport storeServiceReport = new StoreServiceReport();
        storeServiceReport.setMerCode(integrate[0]);
        storeServiceReport.setOrganizationCode(integrate[1]);
        storeServiceReport.setThirdPlatformCode(integrate[2]);
        storeServiceReport.setOnlineStoreCode(integrate[3]);
        String yesterday = statisticDate.format(DateTimeFormatter.ofPattern(DateUtil.DATE_FORMAT_YYYYMMDD));
        storeServiceReport.setStatisticsDate(yesterday);
        storeServiceReport.setJobId(jobId);
        return storeServiceReport;
    }
}
