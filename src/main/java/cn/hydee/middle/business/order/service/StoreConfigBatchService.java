package cn.hydee.middle.business.order.service;

import cn.hydee.middle.business.order.dto.req.batch.*;
import cn.hydee.middle.business.order.dto.rsp.batch.StoreConfigRspDto;

import java.util.List;

/**
 * cn.hydee.middle.business.order.service
 * 批量更新门店配置信息接口  正常部分
 * <AUTHOR>
 * @version 1.0
 * @date 2020/8/31 9:38
 **/
public interface StoreConfigBatchService {

    /**
     * 批量更新门店订单配置
     * @param storeOrderConfigBatchReqDto
     * @return
     */
    StoreConfigRspDto storeOrderConfigBatch(StoreOrderConfigBatchReqDto storeOrderConfigBatchReqDto);

    /**
     * 批量更新门店声音配置
     * @param storeSoundConfigBatchReqDto
     * @return
     */
    StoreConfigRspDto storeSoundConfigBatch(StoreSoundConfigBatchReqDto storeSoundConfigBatchReqDto);

    /**
     * 批量更新门店配置
     * @param storeConfigBatchReqDto
     * @return
     */
    StoreConfigRspDto storeConfigBatch(StoreConfigBatchReqDto storeConfigBatchReqDto);

    List<String> getAllSelfDeliveryType();
}
