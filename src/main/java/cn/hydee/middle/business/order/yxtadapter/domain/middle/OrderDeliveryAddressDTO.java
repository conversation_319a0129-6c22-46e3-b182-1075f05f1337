package cn.hydee.middle.business.order.yxtadapter.domain.middle;

import cn.hydee.middle.business.order.domain.DomainBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class OrderDeliveryAddressDTO extends DomainBase {
    private Long id;
    @ApiModelProperty(value = "订单id")
    private Long orderId;
    @ApiModelProperty(value = "收货人")
    private String receiver;
    @ApiModelProperty(value = "收货人电话")
    private String receiverTelphone;
    @ApiModelProperty(value = "收货人手机")
    private String receiverMobile;
    @ApiModelProperty(value = "省份")
    private String provinceId;
    @ApiModelProperty(value = "城市")
    private String cityId;
    @ApiModelProperty(value = "区域")
    private String areaId;
    @ApiModelProperty(value = "省份名称")
    private String provinceName;
    @ApiModelProperty(value = "城市名称")
    private String cityName;
    @ApiModelProperty(value = "区域名称")
    private String areaName;
    @ApiModelProperty(value = "邮编")
    private String zipCode;
    @ApiModelProperty(value = "详细地址")
    private String address;
    @ApiModelProperty(value = "完整详细地址")
    private String fullDetaiAddress;
    @ApiModelProperty(value = "收货地址经度")
    private String longitude;
    @ApiModelProperty(value = "收货地址纬度")
    private String latitude;


}