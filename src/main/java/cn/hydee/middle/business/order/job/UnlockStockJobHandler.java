package cn.hydee.middle.business.order.job;
import java.util.Date;


import cn.hutool.core.util.StrUtil;
import cn.hydee.middle.business.order.Enums.LocalStockEnum;
import cn.hydee.middle.business.order.dto.req.CommodityStockReqDto;
import cn.hydee.middle.business.order.dto.req.alarm.AlarmExceptionOrderReq;
import cn.hydee.middle.business.order.entity.CommodityStock;
import cn.hydee.middle.business.order.service.CommodityStockService;
import cn.hydee.middle.business.order.yxtadapter.util.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description  定时任务释放库存占用
 * @Date 15:17 2025/2/12
 **/
@Slf4j
@Component
public class UnlockStockJobHandler {


    @Resource
    private CommodityStockService commodityStockService;


    @XxlJob("unlockStockJobHandler")
    public void unlockStockJobHandler() {

        XxlJobHelper.log("定时任务释放库存占用 unlockStockJobHandler start......");
        try {

            CommodityStockReqDto commodityStockReqDto = new CommodityStockReqDto();
            commodityStockReqDto.setType(LocalStockEnum.SUB_FAIL.getType());
            commodityStockReqDto.setBeginTime(DateUtil.getBeginDayOfYesterday());
            commodityStockReqDto.setEndTime(DateUtil.getEndDayOfYesterDay());
            commodityStockService.unLockStockJob(commodityStockReqDto);
        } catch (Exception e) {
            XxlJobHelper.log(e);
            XxlJobHelper.handleFail( "定时任务释放库存占用 unlockStockJobHandler is failed");
            return;
        }
        XxlJobHelper.log("定时任务释放库存占用 unlockStockJobHandler end......");
        XxlJobHelper.handleSuccess();//设置任务结果
    }
}
