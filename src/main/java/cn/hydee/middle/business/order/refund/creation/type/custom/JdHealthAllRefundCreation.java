package cn.hydee.middle.business.order.refund.creation.type.custom;

import cn.hydee.middle.business.order.dto.message.NetRefundNotifyMessage;
import cn.hydee.middle.business.order.refund.container.BeanContainer;
import cn.hydee.middle.business.order.refund.context.RefundOrderContext;
import cn.hydee.middle.business.order.refund.creation.type.ins.AllRefundCreation;
import cn.hydee.middle.business.order.refund.creation.type.ins.RemainingRefundCreation;

public class JdHealthAllRefundCreation extends AllRefundCreation {

    private final RemainingRefundCreation remainingRefundCreation;

    public JdHealthAllRefundCreation(BeanContainer beanContainer, RemainingRefundCreation remainingRefundCreation) {
        super(beanContainer);
        this.remainingRefundCreation = remainingRefundCreation;
    }

    @Override
    public RefundOrderContext createRefund(NetRefundNotifyMessage message) {
        if (hasRefundOrder(message.getOlorderno())) {
            return remainingRefundCreation.createRefund(message);
        }
        return super.createRefund(message);
    }

}
