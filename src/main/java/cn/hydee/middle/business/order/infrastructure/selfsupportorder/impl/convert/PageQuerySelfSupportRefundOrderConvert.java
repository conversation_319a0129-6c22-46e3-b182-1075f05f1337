package cn.hydee.middle.business.order.infrastructure.selfsupportorder.impl.convert;

import cn.hydee.middle.business.order.dto.rsp.baseinfo.PageQuerySelfSupportRefundOrderResDTO;
import cn.hydee.middle.business.order.entity.RefundOrder;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR> Date:  2024/6/19
 */
public class PageQuerySelfSupportRefundOrderConvert {

  public static List<PageQuerySelfSupportRefundOrderResDTO> convertToSelfSupportRefundOrderList(List<RefundOrder> orderList){
    if(null==orderList){
      return null;
    }
    return orderList.stream()
        .map(PageQuerySelfSupportRefundOrderConvert::convertToSelfSupportRefundOrder).collect(
            Collectors.toList());
  }


  public static PageQuerySelfSupportRefundOrderResDTO convertToSelfSupportRefundOrder(RefundOrder order){
    if(null==order){
      return null;
    }
    PageQuerySelfSupportRefundOrderResDTO selfSupportOrder = new PageQuerySelfSupportRefundOrderResDTO();
    selfSupportOrder.setMerCode(order.getMerCode());
    selfSupportOrder.setPlatformCode(order.getThirdPlatformCode());
    selfSupportOrder.setServiceMode(order.getServiceMode());
    selfSupportOrder.setOrderNo(order.getOrderNo());
    selfSupportOrder.setThirdOrderNo(order.getThirdOrderNo());
    selfSupportOrder.setOrderState(order.getState());
    selfSupportOrder.setTotalActualOrderAmount(order.getConsumerRefund());
   return selfSupportOrder;
  }
}
