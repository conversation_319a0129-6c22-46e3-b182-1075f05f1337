package cn.hydee.middle.business.order.yxtadapter.domain.pos.callback.getorderinfo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by Intellij IDEA.
 * 订单商品明细
 *
 * <AUTHOR>
 * Date:  2023/9/6
 */
public class KcPosOrderDetail implements java.io.Serializable {

    /**
     * 商品编码
     */
    @JsonProperty("GOODSCODE")
    @JSONField(name = "GOODSCODE")
    private String GOODSCODE;

    /**
     * 商品名
     */
    @JsonProperty("GOODSNAME")
    @JSONField(name = "GOODSNAME")
    private String GOODSNAME;


    /**
     * 发货数量
     */
    @JsonProperty("BUYCOUNT")
    @JSONField(name = "BUYCOUNT")
    private BigDecimal BUYCOUNT;


    /**
     * 商品单价（标价）
     */
    @JsonProperty("GOODSPRICE")
    @JSONField(name = "GOODSPRICE")
    private BigDecimal GOODSPRICE;
    /**
     * 折后价（实际价格
     */
    @JsonProperty("DISCOUNTPRICE")
    @JSONField(name = "DISCOUNTPRICE")
    private BigDecimal DISCOUNTPRICE;


    /**
     * 分摊金额
     */
    @JsonProperty("SHARINGPRICE")
    @JSONField(name = "SHARINGPRICE")
    private BigDecimal SHARINGPRICE;
    /**
     * 商品总金额（原价*数量）
     */
    @JsonProperty("GOODSAMOUNT")
    @JSONField(name = "GOODSAMOUNT")
    private BigDecimal GOODSAMOUNT;
    /**
     * 实际净额（小票，可为0）
     */
    @JsonProperty("REALITYAMOUNT")
    @JSONField(name = "REALITYAMOUNT")
    private BigDecimal REALITYAMOUNT;
    /**
     * 折扣（小票）
     */
    @JsonProperty("DISCOUNT")
    @JSONField(name = "DISCOUNT")
    private BigDecimal DISCOUNT;
    /**
     * 分摊金额（销售单，不为0）
     */
    @JsonProperty("SHARINGAMOUNT")
    @JSONField(name = "SHARINGAMOUNT")
    private BigDecimal SHARINGAMOUNT;
    /**
     * 分摊折扣（销售单）
     */
    @JsonProperty("SHARINGDISCOUNT")
    @JSONField(name = "SHARINGDISCOUNT")
    private BigDecimal SHARINGDISCOUNT;
    /**
     * 是否是赠品(0：否，1：是)
     */
    @JsonProperty("ISGIFT")
    @JSONField(name = "ISGIFT")
    private int ISGIFT;
    /**
     * 商品-批次信息
     */
    @JsonProperty("BATCH")
    @JSONField(name = "BATCH")
    private List<KcPosBatchInfo> BATCH;

    public String getGOODSCODE() {
        return GOODSCODE;
    }

    public void setGOODSCODE(String GOODSCODE) {
        this.GOODSCODE = GOODSCODE;
    }

    public String getGOODSNAME() {
        return GOODSNAME;
    }

    public void setGOODSNAME(String GOODSNAME) {
        this.GOODSNAME = GOODSNAME;
    }

    public BigDecimal getBUYCOUNT() {
        return BUYCOUNT;
    }

    public void setBUYCOUNT(BigDecimal BUYCOUNT) {
        this.BUYCOUNT = BUYCOUNT;
    }

    public BigDecimal getGOODSPRICE() {
        return GOODSPRICE;
    }

    public void setGOODSPRICE(BigDecimal GOODSPRICE) {
        this.GOODSPRICE = GOODSPRICE;
    }

    public BigDecimal getDISCOUNTPRICE() {
        return DISCOUNTPRICE;
    }

    public void setDISCOUNTPRICE(BigDecimal DISCOUNTPRICE) {
        this.DISCOUNTPRICE = DISCOUNTPRICE;
    }

    public BigDecimal getSHARINGPRICE() {
        return SHARINGPRICE;
    }

    public void setSHARINGPRICE(BigDecimal SHARINGPRICE) {
        this.SHARINGPRICE = SHARINGPRICE;
    }

    public BigDecimal getGOODSAMOUNT() {
        return GOODSAMOUNT;
    }

    public void setGOODSAMOUNT(BigDecimal GOODSAMOUNT) {
        this.GOODSAMOUNT = GOODSAMOUNT;
    }

    public BigDecimal getREALITYAMOUNT() {
        return REALITYAMOUNT;
    }

    public void setREALITYAMOUNT(BigDecimal REALITYAMOUNT) {
        this.REALITYAMOUNT = REALITYAMOUNT;
    }

    public BigDecimal getDISCOUNT() {
        return DISCOUNT;
    }

    public void setDISCOUNT(BigDecimal DISCOUNT) {
        this.DISCOUNT = DISCOUNT;
    }

    public BigDecimal getSHARINGAMOUNT() {
        return SHARINGAMOUNT;
    }

    public void setSHARINGAMOUNT(BigDecimal SHARINGAMOUNT) {
        this.SHARINGAMOUNT = SHARINGAMOUNT;
    }

    public BigDecimal getSHARINGDISCOUNT() {
        return SHARINGDISCOUNT;
    }

    public void setSHARINGDISCOUNT(BigDecimal SHARINGDISCOUNT) {
        this.SHARINGDISCOUNT = SHARINGDISCOUNT;
    }

    public int getISGIFT() {
        return ISGIFT;
    }

    public void setISGIFT(int ISGIFT) {
        this.ISGIFT = ISGIFT;
    }

    public List<KcPosBatchInfo> getBATCH() {
        return BATCH;
    }

    public void setBATCH(List<KcPosBatchInfo> BATCH) {
        this.BATCH = BATCH;
    }
}
