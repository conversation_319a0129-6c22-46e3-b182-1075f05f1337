package cn.hydee.middle.business.order.Enums;


/**
 * 订单状态统一枚举
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/17
 */

public enum OrderJobTypeEnum {
    //SHOP_EX(1, "门店查询异常，如返回null"),
    //COMMODITY_NOT_FOUND_EX(2, "商品查询接口异常，如返回null"),
    //OUT_STOCK_EX(3, "扣库存异常，如返回null");
    API_ERROR(1, "接口返回异常");

    private Integer code;
    private String msg;

    OrderJobTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

}
