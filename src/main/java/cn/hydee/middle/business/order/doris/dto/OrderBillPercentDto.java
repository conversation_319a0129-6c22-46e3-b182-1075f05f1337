package cn.hydee.middle.business.order.doris.dto;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.annotation.PropertyCompare;
import cn.hydee.middle.business.order.annotation.SpecialPropertyField;
import cn.hydee.middle.business.order.doris.dto.rsp.OrderBillQueryByTimeRspDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/06/21
 */
@Data
public class OrderBillPercentDto extends OrderBillCommonDto {

    private static final long serialVersionUID = -7897665328242918043L;

    @ApiModelProperty(value = "下账笔单价",notes = "下账总金额 / 已下账订单数")
    @SpecialPropertyField(1)
    @PropertyCompare
    private BigDecimal billUnitPrice;

    @ApiModelProperty(value = "商品毛利额",notes = "下账商品金额 - 商品成本（ERP零售下账返回的成本）")
    @SpecialPropertyField(1)
    @PropertyCompare
    private BigDecimal grossProfitAmount;

    @ApiModelProperty(value = "商品毛利率",notes = "商品毛利额 / 下账商品金额")
    @SpecialPropertyField(1)
    @PropertyCompare
    private String grossProfitPercent;

    @ApiModelProperty(value = "综合营收",notes = "下账商品金额 + 门店下账配置中设置为需要计算毛利的下账金额")
    @SpecialPropertyField(1)
    @PropertyCompare
    private BigDecimal totalAmountComprehensive;

    @ApiModelProperty(value = "综合毛利额",notes = "综合营收 - 商品成本")
    @SpecialPropertyField(1)
    @PropertyCompare
    private BigDecimal grossProfitAmountComprehensive;

    @ApiModelProperty(value = "综合毛利率",notes = "综合毛利额 / 综合营收")
    @SpecialPropertyField(1)
    @PropertyCompare
    private String grossProfitPercentComprehensive;


    private void calculateBillUnitPrice() {
        if(DsConstants.INTEGER_ZERO.equals(super.getOrderCount())
                || BigDecimal.ZERO.compareTo(super.getBillTotalAmount()) == 0){
            this.billUnitPrice = BigDecimal.ZERO;
            return;
        }
        // 下账笔单价 = 下账总金额 / 已下账订单数
        this.billUnitPrice = super.getBillTotalAmount().divide(new BigDecimal(super.getOrderCount()),2,BigDecimal.ROUND_HALF_UP);
    }

    private void calculateGrossProfit() {
        if(BigDecimal.ZERO.compareTo(super.getCommodityBillTotal()) == 0) {
            // 如果已下账订单的非【已退款】商品明细的下账总金额为0，则该订单的毛利额和毛利率都为0。
            this.grossProfitAmount = BigDecimal.ZERO;
            this.grossProfitPercent = "0.00%";
            return;
        }
        // 毛利额 = 已下账订单的商品下账总金额 - 商品成本总金额
        this.grossProfitAmount = super.getCommodityBillTotal().subtract(super.getCommodityCostTotal());
        // 毛利率 = 毛利额 / 已下账订单的商品下账总金额
        NumberFormat numberFormat = NumberFormat.getPercentInstance();
        numberFormat.setMaximumFractionDigits(2);
        BigDecimal divide = this.grossProfitAmount.divide(super.getCommodityBillTotal(),4,BigDecimal.ROUND_HALF_UP);
        this.grossProfitPercent = numberFormat.format(divide.doubleValue());
    }

    private void calculateGrossProfitComprehensive(){
        if(BigDecimal.ZERO.compareTo(super.getCommodityBillTotal()) == 0) {
            // 如果已下账订单的非【已退款】商品明细的下账总金额为0，则该订单的综合毛利额和综合毛利率都为0。
            this.grossProfitAmountComprehensive = BigDecimal.ZERO;
            this.grossProfitPercentComprehensive = "0.00%";
            this.totalAmountComprehensive = BigDecimal.ZERO;
            return;
        }
        // 综合营收 = 商品明细的下账金额 + 参与计算综合毛利的下账金额
        this.totalAmountComprehensive = super.getCommodityBillTotal().add(super.getBillAmountComprehensive());
        // 综合毛利额 = 综合营收 - 商品成本总金额
        this.grossProfitAmountComprehensive = this.totalAmountComprehensive.subtract(super.getCommodityCostTotal());
        // 综合毛利率 = 综合毛利额 / 综合营收
        NumberFormat numberFormat = NumberFormat.getPercentInstance();
        numberFormat.setMaximumFractionDigits(2);
        if(BigDecimal.ZERO.compareTo(this.totalAmountComprehensive) == 0) {
            this.grossProfitPercentComprehensive = "0.00%";
            return;
        }
        BigDecimal divide = this.grossProfitAmountComprehensive.divide(this.totalAmountComprehensive,4,BigDecimal.ROUND_HALF_UP);
        this.grossProfitPercentComprehensive = numberFormat.format(divide.doubleValue());
    }

    public void calculateData(){
        calculateBillUnitPrice();
        calculateGrossProfit();
        calculateGrossProfitComprehensive();
    }

    public double convertGrossProfitPercent() {
        String grossProfitPercent = Optional.ofNullable(this.grossProfitPercent).orElse("0.00%");
        String[] grossProfitPercentArr = grossProfitPercent.split("%");
        if(1 != grossProfitPercentArr.length) {
            return 0.0d;
        }
        String grossProfitPercentOneIndex = Optional.ofNullable(grossProfitPercentArr[0]).orElse("0.00");
        grossProfitPercentOneIndex = grossProfitPercentOneIndex.replace(",", "");
        return Double.valueOf(grossProfitPercentOneIndex);
    }

    public double convertGrossProfitPercentComprehensive() {
        String grossProfitPercentComprehensive = Optional.ofNullable(this.grossProfitPercentComprehensive).orElse("0.00%");
        String[] grossProfitPercentComprehensiveArr = grossProfitPercentComprehensive.split("%");
        if(1 != grossProfitPercentComprehensiveArr.length) {
            return 0.0d;
        }
        String grossProfitPercentComprehensiveOneIndex = Optional.ofNullable(grossProfitPercentComprehensiveArr[0]).orElse("0.00");
        grossProfitPercentComprehensiveOneIndex = grossProfitPercentComprehensiveOneIndex.replace(",", "");
        return Double.valueOf(grossProfitPercentComprehensiveOneIndex);
    }

    public void calculateSum(List<OrderBillQueryByTimeRspDto> dataList){
        dataList.forEach(data->{
            this.setOrderCount(this.getOrderCount()+data.getOrderCount());
            this.setBillTotalAmount(this.getBillTotalAmount().add(data.getBillTotalAmount()));
            if(BigDecimal.ZERO.compareTo(data.getCommodityBillTotal()) != 0) {
                // 如果已下账订单的非【已退款】商品明细的下账总金额为0，则该订单的毛利额和毛利率都为0。
                this.setCommodityBillTotal(this.getCommodityBillTotal().add(data.getCommodityBillTotal()));
                this.setCommodityCostTotal(this.getCommodityCostTotal().add(data.getCommodityCostTotal()));
                if(BigDecimal.ZERO.compareTo(data.getBillAmountComprehensive()) != 0) {
                    // 如果已下账订单的非【已退款】商品明细的下账总金额为0，则该订单的综合毛利额和综合毛利率都为0。
                    this.setBillAmountComprehensive(this.getBillAmountComprehensive().add(data.getBillAmountComprehensive()));
                }
            }
            this.setMerchantDeliveryFee(this.getMerchantDeliveryFee().add(data.getMerchantDeliveryFee()));
            this.setPlatformDeliveryFee(this.getPlatformDeliveryFee().add(data.getPlatformDeliveryFee()));
            this.setMerchantPackFee(this.getMerchantPackFee().add(data.getMerchantPackFee()));
            this.setPlatformPackFee(this.getPlatformPackFee().add(data.getPlatformPackFee()));
            this.setPlatBrokerageAmount(this.getPlatBrokerageAmount().add(data.getPlatBrokerageAmount()));
            this.setMerchantDiscount(this.getMerchantDiscount().add(data.getMerchantDiscount()));
            this.setPlatformDiscount(this.getPlatformDiscount().add(data.getPlatformDiscount()));
            this.setDiscountAmount(this.getDiscountAmount().add(data.getDiscountAmount()));
        });
        this.calculateData();
    }
}
