package cn.hydee.middle.business.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * o2o平台配送门店信息
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DsDeliveryStore implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商户编码
     */
    private String merCode;

    /**
     * o2o平台标记
     */
    private String platformCode;

    /**
     * o2o平台名
     */
    private String platformName;

    /**
     * 配送网店编码
     */
    private String deliveryClientCode;

    /**
     * 配送网店名称
     */
    private String deliveryClientName;

    /**
     * 配送门店编码
     */
    private String deliveryStoreCode;

    /**
     * 配送门店名称
     */
    private String deliveryStoreName;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 地址
     */
    private String address;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 末次修改时间
     */
    private Date modifyTime;


    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 门店联系信息(手机号或座机或400)
     */
    private String contactPhone;

    /**
     * 所在城市
     */
    private String city;

    /**
     * 区域名称(如, 浦东新区)
     */
    private String areaName;

    /**
     * 坐标属性（ 1:腾讯地图, 2:百度地图, 3:高德地图）
     */
    private Integer positionSource;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 配送服务代码 美团：（飞速达:4002 快速达:4011 及时达:4012 集中送:4013）
     */
    private String serviceCode;

    /**
     * 默认配送服务code
     */
    private String defaultServiceCode;

    /**
     * 开放配送平台id，只有关联了开放配送平台才有值
     * {@link OpenDeliveryPlatform#id}
     **/
    private Integer openDeliveryPlatformId;

    /**
     * 门店当前可支持的结算方式下的支付方式，支付方式，0、账期支付，1、余额支付； 逗号分隔如1,2 ；空按账期支付处理
     */
    private String payTypeCodes;


    /**
     * 保存配送平台的配送范围，为json数组
     */
    private String scope;
}
