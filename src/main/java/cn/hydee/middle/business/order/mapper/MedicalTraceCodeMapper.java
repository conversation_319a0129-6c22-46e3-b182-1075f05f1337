package cn.hydee.middle.business.order.mapper;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.entity.MedicalTraceCode;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface MedicalTraceCodeMapper extends BaseMapper<MedicalTraceCode> {


  /**
   * 查询正单追溯码
   *
   * @param orderNo
   * @return
   */
  Integer queryOrderTraceCodeCount(@Param("orderNo") Long orderNo);

  /**
   * 查询所有追溯码
   *
   * @param orderNo
   * @return
   */
  List<MedicalTraceCode> queryAllOrderTraceCode(@Param("orderNo") Long orderNo);

  /**
   * 查询正单追溯码
   *
   * @param orderNo
   * @return
   */
  List<MedicalTraceCode> queryOrderTraceCode(@Param("orderNo") Long orderNo);

  List<MedicalTraceCode> queryOrderTraceCodeWithOrderDetailIds(@Param("orderNo") Long orderNo,
      @Param("orderDetailIdList") List<Long> orderDetailIdList);

   /**
   * 查询逆向单追溯码
   *
   * @param orderNo
   * @return
   */
  List<MedicalTraceCode> queryRefundOrderTraceCode(@Param("orderNo") Long orderNo);

  List<MedicalTraceCode> queryRefundOrderTraceCodeWithRefundDetailIds(
      @Param("orderNo") Long orderNo, @Param("refundDetailIdList") List<Long> refundDetailIdList);

  /**
   * 删除追溯码
   *
   * @param medicalTraceCodeRecordReqDto
   * @return
   */
  Boolean deleterByOrderDetailId(@Param("orderNo") Long orderNo,@Param("orderDetailId") Long orderDetailId);

  Boolean deleterByRefundDetailId(@Param("orderNo") Long orderNo,@Param("refundDetailId") Long refundDetailId);
}
