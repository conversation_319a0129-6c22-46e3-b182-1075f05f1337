package cn.hydee.middle.business.order.yxtadapter.domainservice.assignment.impl;

import cn.hydee.middle.business.order.yxtadapter.domain.assignment.Assignment;
import cn.hydee.middle.business.order.yxtadapter.domainservice.assignment.gateway.AssignmentCmdGateway;
import cn.hydee.middle.business.order.yxtadapter.domainservice.assignment.gateway.AssignmentQryGateway;
import cn.hydee.middle.business.order.yxtadapter.domainservice.dubbo.request.AssignmentQry;
import cn.hydee.middle.business.order.yxtadapter.domainservice.dubbo.request.AssignmentSucceededQry;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR>
 * Date:  2023/9/11
 */
@Slf4j
@Component
public class RemoveSucceedAssignmentDefaultTask {

    @Autowired
    private AssignmentQryGateway qryGateway;
    @Autowired
    private AssignmentCmdGateway cmdGateway;


    @Value("${assignment.succeed.interval:5}")
    private int succeedIntervalMinute = 5;
    @Value("${assignment.succeed.limitNum:1000}")
    private int succeedLimitNum = 1000;

    public void doJob(String param) {
        log.info("RemoveSucceedAssignmentDefaultTask  start");
        AssignmentSucceededQry assignmentQry = new AssignmentSucceededQry(succeedIntervalMinute,succeedLimitNum);
        List<Assignment> assignments = qryGateway.qrySucceededList(assignmentQry);
        if (assignments == null || assignments.isEmpty()) {
            log.info("RemoveSucceedAssignmentDefaultTask assignments is empty");
            return;
        }
        List<Long> ids = assignments.stream().map(Assignment::getId).collect(Collectors.toList());
        boolean b = cmdGateway.removeSucceedAssignmentList(ids);
        if (b) {
            log.info("RemoveSucceedAssignmentDefaultTask remove succeed assignment success size = {}", assignments.size());
        } else {
            log.info("RemoveSucceedAssignmentDefaultTask remove succeed assignment fail");
        }

    }




}
