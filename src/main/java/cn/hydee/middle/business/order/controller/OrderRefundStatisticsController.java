package cn.hydee.middle.business.order.controller;


import cn.hydee.middle.business.order.dto.order.refund.statistics.CalculateRefundStatisticsDTO;
import cn.hydee.middle.business.order.service.OrderRefundStatisticsService;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * 订单退款预处理表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-26
 */
@RestController
@RequestMapping("/${api.version}/ds/orderRefundStatistics")
@Api(tags = "订单退款预处理相关接口")
public class OrderRefundStatisticsController extends AbstractController {
    @Autowired
    private OrderRefundStatisticsService orderRefundStatisticsService;

    @ApiOperation(value = "根据时间统计订单退款已下账数据", notes = "一般用于统计历史数据")
    @PostMapping("/calculateHistoryDataByTime")
    public ResponseBase<String> calculateHistoryDataByTime(
            @RequestHeader("userId") String userId,
            @RequestHeader("merCode") String merCode,
            @Valid @RequestBody CalculateRefundStatisticsDTO reqDto,
            BindingResult result){
        checkValid(result);
        reqDto.setMerCode(merCode);
        orderRefundStatisticsService.calculateHistoryDataByTime(reqDto);
        return generateSuccess(null);
    }

    @ApiOperation(value = "根据商户编码、时间统计订单退款已下账数据", notes = "一般用于统计历史数据")
    @PostMapping("/calculateHistoryData")
    public ResponseBase<String> calculateHistoryMerCodeTime(
            @RequestHeader("userId") String userId,
            @RequestHeader("merCode") String merCode,
            @Valid @RequestBody CalculateRefundStatisticsDTO reqDto,
            BindingResult result){
        checkValid(result);
        reqDto.setMerCode(merCode);
        orderRefundStatisticsService.calculateHistoryMerCodeTime(reqDto);
        return generateSuccess(null);
    }

}

