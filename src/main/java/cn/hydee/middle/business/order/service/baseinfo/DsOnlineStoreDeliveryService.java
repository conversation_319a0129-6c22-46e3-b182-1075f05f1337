package cn.hydee.middle.business.order.service.baseinfo;

import cn.hydee.middle.business.order.dto.req.baseinfo.DsBindDeliveryStoreQueryDTO;
import cn.hydee.middle.business.order.dto.req.baseinfo.DsSelfDeliveryQueryDTO;
import cn.hydee.middle.business.order.dto.req.baseinfo.DsStoreDeliveryCreateUpdateDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.DsStoreDeliveryResDTO;
import cn.hydee.middle.business.order.entity.DsDeliveryStore;
import cn.hydee.middle.business.order.entity.DsOnlineStoreDelivery;
import cn.hydee.middle.business.order.storeautosync.infrastructure.mysql.entity.DsOnlineStoreAuthRecord;
import cn.hydee.starter.dto.PageDTO;

import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface DsOnlineStoreDeliveryService extends IService<DsOnlineStoreDelivery> {

     /**
      * 查询线上门店配送信息
      * @param merCode
      * @param dto
      * @return
      */
     List<DsStoreDeliveryResDTO> queryDeliveryConf(String merCode, DsSelfDeliveryQueryDTO dto) throws InstantiationException, IllegalAccessException;

     /**
      * 保存线上门店配送信息
      * @param merCode
      * @param dto
      * @return
      */
     void saveDeliveryConf(String merCode, DsStoreDeliveryCreateUpdateDTO dto, String userId, String userName);

     /**
      * 分页查询配送门店信息
      * @param merCode
      * @param dto
      * @return
      */
     PageDTO<DsStoreDeliveryResDTO> queryDeliveryStoreConf(String merCode, DsBindDeliveryStoreQueryDTO dto) throws InstantiationException, IllegalAccessException;

     /**
      * 设置启用状态
      * @param merCode
      * @param dto
      * @return
      */
     int settingStatus(String merCode, DsStoreDeliveryCreateUpdateDTO dto, String userId, String userName);

     /**
      * 修改自配送配置
      * @param merCode
      * @param dto
      * @return
      */
     Void updateDeliveryConf(String merCode, DsStoreDeliveryCreateUpdateDTO dto);

     /**
      * 创建默认自配送方式
      * @param merCode
      * @return
      */
     public int createDefaultDelivery(String merCode,String platformCode);

     void createDefaultDeliverySingle(String merCode, Long onlineStoreId);

     /**
      * 根据id获取自配送方式
      *
      * @param id
      * @return DsOnlineStoreDelivery
      */
     DsOnlineStoreDelivery get(Long id);

     Long getIdByDeliveryType(Long onlineStoreId,String deliveryType);

     void createOnlineStoreDelivery(List<DsDeliveryStore> dsDeliveryStores, Long onlineStoreId);
}
