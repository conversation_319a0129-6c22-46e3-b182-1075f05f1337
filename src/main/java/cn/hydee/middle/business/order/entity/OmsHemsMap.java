package cn.hydee.middle.business.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
public class OmsHemsMap implements Serializable {

    private static final long serialVersionUID = 1235141902787000307L;
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * oms商户编码
     */
    @ApiModelProperty(value ="oms商户编码")
    private String omsMercode;

    /**
     * oms平台编码
     */
    @ApiModelProperty(value ="oms平台编码")
    private String omsPlatformCode;

    /**
     * oms网店编码
     */
    @ApiModelProperty(value ="oms网店编码")
    private String omsClientCode;

    /**
     * hems商户编码
     */
    @ApiModelProperty(value ="hems商户编码")
    private String hemsMercode;

    /**
     * hems平台编码
     */
    @ApiModelProperty(value ="hems平台编码")
    private String hemsPlatformCode;

    /**
     * hems网店编码
     */
    @ApiModelProperty(value ="hems网店编码")
    private String hemsClientCode;

    /**
     * appsecret
     */
    @ApiModelProperty(value ="appsecret")
    private String appsecret;

}
