package cn.hydee.middle.business.order.dto.req;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.starter.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * cn.hydee.middle.business.order.dto.req
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/6/12 16:17
 **/
@Builder
@Data
public class QueryOrganizationReqDto {

    @ApiModelProperty(value = "商家编码", hidden = true)
    private String merCode;
    @ApiModelProperty(value = "用户id", hidden = true)
    private String userId;

    @ApiModelProperty(value="省市，如：湖南省，全部时传空")
    private List<String> province;
    @ApiModelProperty(value="城市名称，如：长沙市，全部时传空")
    private List<String> city;
    @ApiModelProperty(value="区县，如：[岳麓区,河西区]，全部时传空集合")
    private List<String> area;

    public boolean pcaAll(){
        boolean provinceAll = null == province || province.size() == 0 || province.size() == 1 && province.get(0).equals(DsConstants.ALL);
        boolean cityAll = null == city || city.size() == 0 || city.size() == 1 && city.get(0).equals(DsConstants.ALL);
        boolean areaAll = null == area || area.size() == 0 || area.size() == 1 && area.get(0).equals(DsConstants.ALL);
        return provinceAll && cityAll && areaAll;
    }

    public boolean onlyProvince(){
        boolean provinceNotAll = !(null == province || province.size() == 0 || province.size() == 1 && province.get(0).equals(DsConstants.ALL));
        boolean cityAll = null == city || city.size() == 0 || city.size() == 1 && city.get(0).equals(DsConstants.ALL);
        boolean areaAll = null == area || area.size() == 0 || area.size() == 1 && area.get(0).equals(DsConstants.ALL);
        return provinceNotAll && cityAll && areaAll;
    }

    public boolean onlyAreaNull(){
        boolean provinceNotAll = !(null == province || province.size() == 0 || province.size() == 1 && province.get(0).equals(DsConstants.ALL));
        boolean cityNotAll = !(null == city || city.size() == 0 || city.size() == 1 && city.get(0).equals(DsConstants.ALL));
        boolean areaAll = null == area || area.size() == 0 || area.size() == 1 && area.get(0).equals(DsConstants.ALL);
        return provinceNotAll && cityNotAll && areaAll;
    }

    public boolean areaNotNull(){
        boolean areaAll = !(null == area || area.size() == 0 || area.size() == 1 && area.get(0).equals(DsConstants.ALL));
        return areaAll;
    }

}
