package cn.hydee.middle.business.order.dto.rsp.baseinfo;

import cn.hydee.middle.business.order.entity.OpenDeliveryPlatform;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DsStoreDeliveryInfoResDTO {

    @ApiModelProperty(value = "商户编码")
    private String merCode;

    @ApiModelProperty(value = "o2o平台标记")
    private String platformCode;

    @ApiModelProperty(value = "配送网店编码")
    private String deliveryClientCode;

    @ApiModelProperty(value = "sessionKey")
    private String sessionKey;


    /** 配送门店主键ID **/
    private Long deliveryStoreId;
    /** 配送门店名称 */
    private String deliveryStoreName;
    /** 线上门店对应的配送门店编码 */
    private String deliveryStoreCode;
    /** 配送门店所在城市 */
    private String deliverStoreCity;
    /** 配送门店地址 */
    private String deliverStoreAddress;
    /** 配送门店纬度 */
    private String deliverStoreLatitude;
    /** 配送门店经度 */
    private String deliverStoreLongitude;
    /** 配送门店电话 */
    private String deliverStorePhone;
    /** 配送服务方式 **/
    private String deliveryServiceCode;
    /**默认配送服务code **/
    private String defaultServiceCode;
    /** o2o平台名称 **/
    private String platformName;
    /** 门店当前可支持的结算方式下的支付方式，支付方式，0、账期支付，1、余额支付； 逗号分隔如1,2 ；空按账期支付处理 **/
    private String payTypeCodes;

    /** 配送方式（美团、蜂鸟、达达、员工自送、买家自提）  **/
    private String deliveryType;

    /**
     * 开放配送平台id，只有关联了开放配送平台才有值
     * {@link OpenDeliveryPlatform#id}
     **/
    private Integer openDeliveryPlatformId;

    /**
     * 门店配置的三方骑手是否禁用 0-禁用，1-启用
     */
    private Integer dsStoreDeliveryStatus;

    public boolean isOpenDelivery() {
        return openDeliveryPlatformId != null;
    }

}
