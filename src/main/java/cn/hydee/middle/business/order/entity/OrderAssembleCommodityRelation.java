package cn.hydee.middle.business.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/1/11 下午6:32
 */
@Data
public class OrderAssembleCommodityRelation implements Serializable {
    private static final long serialVersionUID = 4711031614433408553L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 商户编码
     */
    private String merCode;
    /**
     * 平台类型
     */
    private String ectype;
    /**
     * 第三方平台订单号
     */
    private String thirdOrderNo;
    /**
     * 第三方详情ID，如果为空，则为 erpCode_{index}
     */
    private String orderDetailId;
    /**
     * 第三方组合商品skuId
     */
    private String thirdSkuId;
    /**
     * 组合商品erpCode
     */
    private String originalErpCode;
    /**
     * 组合商品数量
     */
    private Integer assembleCommodityCount;
    /**
     * 一个组合商品对应的子商品数量
     */
    private Integer count;
    /**
     * 一个组合商品对应的子商品单价
     */
    private BigDecimal price;
    /**
     * 该商品占组合商品比例
     */
    private BigDecimal rate;
    /**
     * 商品编码
     */
    private String erpCode;
    /**
     * 条形码
     */
    private String barCode;
    /**
     * 商品名称
     */
    private String name;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date modifyTime;
}
