package cn.hydee.middle.business.order.dto.rsp.baseinfo;

import cn.hydee.middle.business.order.point.dto.ClusterBaseDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper=true)
public class QueryStoreByCityRspDto extends ClusterBaseDto{
	
	private static final long serialVersionUID = 2862380118963731263L;

	@ApiModelProperty(value = "门店名称（线下）")
    private String stName;
    
    @ApiModelProperty(value = "门店编码（线下）")
    private String stCode;
    
    @ApiModelProperty(value = "省份")
    private String province;
    
    @ApiModelProperty(value = "城市")
    private String city;
    
    @ApiModelProperty(value = "区域")
    private String area;
    
    @ApiModelProperty(value = "详细地址")
    private String address;
    
    @ApiModelProperty(value = "o2o服务范围(0,设置距离;1,自定义范围,2：全国范围)")
    private Integer o2oServiceScopeType;
    
    @ApiModelProperty(value = "自定义范围坐标（电子围栏）")
    private String o2oServiceScopePaths;

}
