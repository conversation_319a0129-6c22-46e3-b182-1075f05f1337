package cn.hydee.middle.business.order.Enums;

/**
 * cn.hydee.middle.business.order.Enums
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/7/3 17:21
 **/
public enum OrderOperateEnum {
    OrderState(10,"订单状态修改"),
    OrderBillState(20,"订单下账状态修改"),
    RefundState(30,"退款"),
    RefundStateBill(40,"退款下账"),
    UnlockInventory(50,"解锁已锁库存");

    private Integer code;
    private String msg;

    OrderOperateEnum(Integer code,String msg){
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
