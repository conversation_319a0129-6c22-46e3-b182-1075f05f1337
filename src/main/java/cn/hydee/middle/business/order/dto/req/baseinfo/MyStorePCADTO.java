package cn.hydee.middle.business.order.dto.req.baseinfo;

import cn.hydee.starter.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

/**
 * 用户门店请求类
 * <AUTHOR>
 * @version 1.0
 * @date 2019/11/25 14:34
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MyStorePCADTO extends PageBase {
    @ApiModelProperty(value = "商家编码")
    @NotEmpty
    private String merCode;
    @NotBlank(message = "用户ID不可为空")
    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "是否上线门店（0非上线门店，1上线门店）")
    private Integer onlineStatus;

    @ApiModelProperty(value = "状态（0停用，1启用）")
    private Integer status;

    @ApiModelProperty(value = "是否有电商云线上门店")
    private Integer onlineStoreStatus;

    @ApiModelProperty(value = "只查询门店，1-只查门店，其他值查所有")
    private Integer onlyStore;
}
