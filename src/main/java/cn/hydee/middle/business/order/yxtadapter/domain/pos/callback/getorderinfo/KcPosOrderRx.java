package cn.hydee.middle.business.order.yxtadapter.domain.pos.callback.getorderinfo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR>
 * Date:  2023/9/6
 */
@Data
public class KcPosOrderRx implements java.io.Serializable {

    /**
     * 处方单编号
     */
    private String PRESCRIBENO;

    /**
     * 处方单中处方药种类数
     */
    private String SKUCOUNT;


    /**
     * 用药人姓名
     */
    private String PATIENTNAME;


    /**
     * 用药人性别
     */
    private String SEX;
    /**
     * 用药人电话
     */
    private String PHONE;


    /**
     * 用药人身份证号
     */
    private String IDCARD;
    /**
     * 开方医院
     */
    private String HOSPITAL;
    /**
     * 科室
     */
    private String SECTION;
    /**
     * 开方医生
     */
    private String DOCTOR;
    /**
     * 审方药师
     */
    private String APOTHECARY;
    /**
     * 开方日期（yyyy-MM-dd HH:mm:ss）
     */
    private String PRESCRIBEDATE;
    /**
     * 审方日期（yyyy-MM-dd HH:mm:ss）
     */
    private String CHECKINGDATE;
    /**
     * 处方单图片地址链接
     */
    private String PRESCRIBEIMG;
    /**
     * 处方单类型
     */
    private String TYPE;

    /**
     * 用药人年龄
     */
    private String AGE;


    /**
     * 备注
     */
    private String BZ0;
    /**
     * 其他信息1（备用）
     */
    private String BZ1;
    /**
     * 其他信息2（备用）
     */
    private String BZ2;
    /**
     * 其他信息3（备用）
     */
    private String BZ3;
}
