package cn.hydee.middle.business.order.dto.req.log;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * cn.hydee.middle.business.order.dto.req.log
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/7/7 17:40
 **/
@Data
public class LogReqDto {
    @JsonIgnore
    private String userIP;
    @JsonIgnore
    private String userId;
    @JsonIgnore
    private String merCode;
    @ApiModelProperty("孔明锁")
    private Long _ati;


    @ApiModelProperty("订单号列表，用英文分号分隔，每次最多100条记录。如果超过100条，拆分成多条请求。")
    private String  tradeIds;

    @ApiModelProperty("1001\t查询订单列表\t批量操作订单\n" +
            "1002\t打印订单列表\t\n" +
            "1003\t导出订单列表\t\n" +
            "1004\t删除订单列表\t\n" +
            "—\t\t\n" +
            "2001\t查询订单（包括单独查看明文手机号操作）\t单个订单操作\n" +
            "2002\t打印订单\t\n" +
            "2003\t导出订单\t\n" +
            "2004\t删除订单\t\n" +
            "2005\t修改订单")
    private String operation;


    @ApiModelProperty("如果非订单导出操作，此字段填写字符串“NULL”（注意：不是空）；此字段的作用共两点：1、记录导出excel文件的md5值；2、用于关联导出订单日志上传时时被拆分开的日志。")
    private String file_md5;

    @ApiModelProperty("数据库查询语句 查询操作：select   删除操作： delete   更新操作：update  添加操作： insert")
    private String  sql;

    @ApiModelProperty("日志类型 1：订单访问日志   2： 数据库访问日志  3:登录日志")
    @NotNull
    private Integer type;

    @ApiModelProperty("登录成功或失败")
    private String  loginResult;

    @ApiModelProperty("登录信息")
    private String  loginMessage;

}
