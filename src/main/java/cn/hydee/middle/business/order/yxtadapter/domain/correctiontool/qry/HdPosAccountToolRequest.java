package cn.hydee.middle.business.order.yxtadapter.domain.correctiontool.qry;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/9/26
 * @since 1.0
 */
@Data
public class HdPosAccountToolRequest{

    /**
     * 系统订单号(分为销售单和退货单)
     */
    @ApiModelProperty("系统订单号")
    @NotNull(message="系统订单号不为空")
    private String number;

    /**
     * 订单类型(0-销售单 1-退货单)
     */
    @ApiModelProperty("订单类型(0-销售单 1-退款单)")
    @NotNull(message="订单类型不能为空")
    private Integer orderType;
}
