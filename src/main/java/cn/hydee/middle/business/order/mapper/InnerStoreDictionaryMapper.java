
package cn.hydee.middle.business.order.mapper;

import cn.hydee.middle.business.order.yxtadapter.domain.dictionary.InnerStoreDictionary;
import cn.hydee.middle.business.order.yxtadapter.domain.dictionary.InnerStoreOpenAutoBillDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 基本订单信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-16
 */
@Repository
public interface InnerStoreDictionaryMapper extends BaseMapper<InnerStoreDictionary> {

     InnerStoreDictionary selectByOrganizationCode(String organizationCode);
     InnerStoreDictionary selectByInnerAppId(String innerAppId);

    int updateTokenByInnerAppId(@Param("token")String token, @Param("expirationTime")Long expirationTime, @Param("innerAppId")String innerAppId);

    InnerStoreDictionary selectExpirationTimeByToken(String token);

    List<InnerStoreOpenAutoBillDto> selectOpenAndTimeByOrganizationCode(@Param("organizationCodeList")List<String> organizationCodeList);

    List<InnerStoreDictionary> selectByOrganizationCodes(@Param("organizationCodeList")List<String> organizationCodeList);
}
