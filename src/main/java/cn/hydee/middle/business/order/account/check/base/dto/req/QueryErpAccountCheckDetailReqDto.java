
package cn.hydee.middle.business.order.account.check.base.dto.req;

import cn.hydee.starter.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper=true)
public class QueryErpAccountCheckDetailReqDto extends PageBase {
    @ApiModelProperty(value = "商家编码", hidden = true)
    private String merCode;
    @ApiModelProperty(value = "数据筛选，默认值为全部；可选值：0 全部、1 是-仅展示有差异的数据、2 否-展示无差异的数据;")
    private int dataType;
    @ApiModelProperty(value = "下单开始时间")
    private Date orderTimeStart;
    @ApiModelProperty(value = "下单结束时间")
    private Date orderTimeEnd;
    @ApiModelProperty(value = "下账开始时间")
    private Date billTimeStart;
    @ApiModelProperty(value = "下账结束时间")
    private Date billTimeEnd;
    @ApiModelProperty(value = "平台编码")
    @NotNull
    private String platformCode;
    @ApiModelProperty(value = "门店编码")
    @NotNull
    private String onlineStoreCode;
    @ApiModelProperty(value = "机构编码")
    @NotNull
    private String organizationCode;
    @ApiModelProperty(value = "机构编码List", hidden = true)
    private List<String> organizationCodeList;
}
  
