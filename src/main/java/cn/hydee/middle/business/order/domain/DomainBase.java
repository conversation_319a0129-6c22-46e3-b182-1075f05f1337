package cn.hydee.middle.business.order.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

import java.util.Date;

/**
 * 数据层基类
 */
@Data
public class DomainBase {
    @TableLogic(value="1",delval="0")
    private Integer isvalid;
    @TableField("create_name")
    private String createName;
    @TableField("create_time")
    private Date createTime;
    @TableField("modify_name")
    private String modifyName;
    @TableField("modify_time")
    private Date modifyTime;
}
