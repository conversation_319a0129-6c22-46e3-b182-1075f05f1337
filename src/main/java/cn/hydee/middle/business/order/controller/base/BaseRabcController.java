package cn.hydee.middle.business.order.controller.base;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

/**
 *获取当前登录的用户上下文
 * <AUTHOR>
 * @version 1.0
 * @date 2020/7/19 13:57
 **/
@Slf4j
public class BaseRabcController {

    public static HttpServletRequest getRequest() {
        return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    }

    private BaseRabcController(){

    }

    /**
     * 获得response
     *
     * @return
     */
    public static HttpServletResponse getResponse() {
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        try {
            response.setContentType("text/html;charset=utf-8");
            response.setCharacterEncoding("UTF-8");
        } catch (Exception e1) {
            // TODO Auto-generated catch block
            log.error("context", e1);
        }
        return response;
    }

    public static HttpServletResponse getUpload(String fileName){
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        try {
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setHeader("Content-disposition","attachment; "+fileName);
            response.setCharacterEncoding("UTF-8");
        } catch (Exception e1) {
            // TODO Auto-generated catch block
            log.error("context", e1);
        }
        return response;
    }

    /**
     * 获得session
     *
     * @return
     */
    public static HttpSession getSession() {
        return getRequest().getSession();
    }

    /**
     * 获得servlet上下文
     *
     * @return
     */
    public static ServletContext getServletContext() {
        return getRequest().getServletContext();
    }

    /**
     * 获得绝对路径
     * @param path
     * @return
     */
    public static String getRealPath(String path) {
        return getServletContext().getRealPath(path);
    }
}
