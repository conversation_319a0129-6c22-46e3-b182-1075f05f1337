package cn.hydee.middle.business.order.Enums.baseinfo;


public enum BedeptEnum {

    KA("ka", "KA"),
    CENTRAL_SOUTH("central-south", "中南大区"),
    NORTH("north", "北方大区"),
    EAST_CHINA("east-china", "华东大区"),
    NORTHWEST("northwest", "西北大区"),
    GM("GM", "总经办"),
    SOUTHWEST("southwest", "西南大区");

    private String code;
    private String msg;

    BedeptEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static String getDepeTypeName(String code) {
        for (BedeptEnum item : BedeptEnum.values()) {
            if (item.getCode().equals(code)) {
                return item.getMsg();
            }
        }
        return "";
    }
}
