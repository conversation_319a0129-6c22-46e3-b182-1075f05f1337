package cn.hydee.middle.business.order.v2.handlers;

/**
 * @Description: 处理器接口类
 * @Author: chufeng
 * @since: 2020/8/18 11:46
 **/
public abstract class AbstractHandler {

    protected AbstractHandler nextHandler = null;

    public void setNextHandler(AbstractHandler nextHandler){
        this.nextHandler = nextHandler;
    }

    protected Object handlerNext(Object request) {
        if (null == nextHandler) {
            return null;
        }
        return nextHandler.handlerRequest(request);
    }

    /**
     * 交由各自的Handler实现
     * 
     * @param request
     *            请求参数，实现类需要强转相应参数，注释写清楚
     * @return 返回参数，实现类需要注释写清楚
     */
    public abstract Object handlerRequest(Object request);
}
