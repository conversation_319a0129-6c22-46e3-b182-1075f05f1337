package cn.hydee.middle.business.order.yxtadapter.domain.oms.transform;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Date: 2023/9/21
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class YnOmsOrderDeliveryDto {


    private YnOmsOrderInfoDto ynOmsOrderInfoDto; //订单信息
    private List<YnOmsOrderDetailDto> ynOmsOrderDetailDtoList; //订单商品详细信息
    private List<YnOmsGiftDto> ynOmsOrderGiftList; //订单赠品集合
    private List<OrderInvoice> ynOmsOrderInvoiceDtoList; //订单发票信息
    private OrderRxReviewDto ynOmsOrderRxReviewDto; //订单处方信息
    private Boolean isFinishedState; //是否是最终状态

    @Data
    @Builder
    public static class YnOmsOrderInfoDto {
        private String olorderno; //三方平台订单号
        private String orderid; //三方平台订单号 消息通知时使用
        private String ectype; //电商平台 notnull
        private String groupid; //企业编码  商户编码
        private String clientid; //网店编码  notnull
        private String status; //交易状态
        private String discount_fee; //系统优惠金额
        private BigDecimal total_fee; //商品金额
        private BigDecimal amount; //订单总额，原价总额+运费等
        private String adjust_fee; //卖家手工调整金额
        private String buyer_nick; //买家昵称
        private String receiver_name; //收货人姓名
        private String receiver_state; //收货人所在省份
        private String receiver_city; //收货人所在城市
        private String receiver_district; //收货人所在地区
        private String receiver_town; //收货人所在镇
        private String receiver_address; //收货人详细地址
        private String receiver_zip; //收货人邮编
        private String receiver_mobile; //收货人手机号码
        private String receiver_phone; //收货人电话号码
        private String buyer_message; //买家留言
        private BigDecimal post_fee=BigDecimal.ZERO; //原始配送费
        private BigDecimal realpostfee=BigDecimal.ZERO; //实际配送费
        private String buyer_email; //买家邮件地址
        private String trade_from; //交易内部来源
        private String expcmpname; //快递名称
        private String omsstatus; //（订单状态: 31：处方单待审核 32：待接单 33：处方单驳回 34：处方单审核通过 35：已接单 37：已拣货 40：呼叫骑手 45：骑手到店 54：骑手取货 58：配送完成 110：订单取消)
        private String omsdeliverytype; //到店自提 即时达 当日定时达 快递配送  达达专送  门店配送
        private Integer isselfdelivery; // 是否是门店配送（0：否，1：是）
        private String modified; //交易修改时间
        private String confirm_time; //确认时间
        private String created; //交易创建时间
        private String end_time; //交易结束时间
        private String picking_time;
        private String buyer_memo; //买家备注
        private String seller_memo; //卖家备注
        private String o2o_shop_id; //导购员门店 线上门店
        private Integer buyer_flag; //买家备注标志
        private Integer seller_flag; //卖家备注标志
        private String daynum; //每日号
        private Integer applyCancel; //申请取消
        private Integer o2o_auto_confirm; //o2o自动确认
        private String vatTaxpayerNumber; //增值税纳税人识别号
        private String receiver_lat; //收货地址维度
        private String receiver_lng; //收货地址经度
        private String invoice_contect; //发票内容
        private String invoice_name; //发票抬头
        private String invoice_type; //发票类型
        private String taxerId;  //纳税人识别号
        private String rx_audit_status; //处方审核状态
        private String havecfy; //是否处方
        private Integer cfycheck; //处方药检验
        private String type; //交付类型列表
        private BigDecimal seller_cod_fee = BigDecimal.ZERO; //卖家货到付款服务费
        private BigDecimal buyer_cod_fee = BigDecimal.ZERO; //买家到付服务费
        private BigDecimal payment = BigDecimal.ZERO; //商家实收
        private BigDecimal discount_fee_dtl = BigDecimal.ZERO; //商家明细优惠合计
        private BigDecimal yfx_fee = BigDecimal.ZERO; //订单运费险
        private BigDecimal discount_fee_eccode = BigDecimal.ZERO; //平台优惠
        private BigDecimal poitranscoupon=BigDecimal.ZERO;// 商家承担运费优惠
        private BigDecimal plattranscoupon=BigDecimal.ZERO;// 平台承担运费优惠
        private BigDecimal discount_fee_sum = BigDecimal.ZERO; //商家整单优惠
        private BigDecimal cod_payment = BigDecimal.ZERO; //到付金额
        private BigDecimal settlement_amount = BigDecimal.ZERO; //结算金额
        private BigDecimal commission_fee = BigDecimal.ZERO; //交易佣金
        private BigDecimal package_fee = BigDecimal.ZERO; //打包费
        private BigDecimal sumdiscount = BigDecimal.ZERO; //商品优惠  暂时没使用
        private BigDecimal postfee_dis = BigDecimal.ZERO; //运费优惠
        private BigDecimal customerpayment = BigDecimal.ZERO; //客户实付 （商品小计+运费+包装费-商品优惠-运费优惠）
        private String pay_time; //付款时间
        private Integer delivery_time_type; //配送时间类型
        private String delivery_time; //配送时间描述
        private String selfverifycode; //骑手取货码
        private String membercard; //会员卡号
        private Integer isadjust; //是否调整订单 1 是，0否
        private String ordertype; //订单类型 N正常订单 V虚拟商品订单 I积分订单 A预约订单 C云仓订单
        private String isfirstorder; //是否是当前用户首单 1.是 0.否
        private String extrainfo; //额外的附加信息，参看 ExtraInfo 或 ExtJson
        private String omni_package; //B2C 天猫同城购
        private String b2cClientId; //b2cClient的id
        private String top_hold; //TOP拦截标识，0不拦截，1拦截 null=平台无此标识
        private String delivery_type; ////到店自提 即时达 当日定时达 快递配送  达达专送  门店配送
        private BigDecimal postfee_nodis = BigDecimal.ZERO; //京东到家原始配送费
        private String payCode;//	支付编码-对应ERP支付编码
        private String payName;//	支付类型
        private String paytype; //支付方式
        private BigDecimal payPrice; ////支付类型对应的金额
        private String channelSource; ////支付来源
        private String sourceFrom; //订单来源：OC00006(京东健康)
        private Integer needInvoice; // 是否需要发票 0：否 1：是
        private String billTime;// 下账时间
        private String erpState;// 是否下账（0：待下账，1：可下帐，2：已下账,3进行返下账）

    }

    @Data
    @Builder
    public static class YnOmsOrderDetailDto {
        private String olorderno; //三方平台订单号
        private String clientid; //网店编码  notnull
        private String oid; //子订单编号
        private BigDecimal price; //商品价格
        private String num_iid; //商品数字id 三方平台商品编码
        private String item_meal_id; //套餐id
        private Integer num; //购买数量
        private BigDecimal total_fee; //商品应付金额
        private BigDecimal payment; //商品实付金额
        private BigDecimal discount_fee; //子订单级订单优惠金额
        private BigDecimal adjust_fee; //手工调整金额
        private BigDecimal billPrice;// 下账价格
        private Date modified; //修改时间
        private String title; //商品标题
        private String sku_properties_name; //sku的值
        private String sku_id; //商品最小库存单位sku的id upc
        private String outer_iid; //商家外部编码  erp编码
        private String upc; //upc
        private Integer isdelete; //是否删除
        private String orderdetailid; //订单详情ID
        private String isgift; //是否赠品 true false
        private String oldErpCode;// 换货前的erp_code
    }

    @Data
    @Builder
    public static class YnOmsGiftDto {
        private String gift_outer_iid;    //赠品外部商品编码（赠品的ERP商品编码）
        private String main_outer_iid;    //主商品外部商品编码（赠品关联的主商品的ERP商品编码）
        private Integer gift_num;    //赠品数量
        private String gift_name;    //赠品名称
        private String main_sku_id;    //主商品 skuid
        private String gift_sku_id;  //	赠品 skuid
    }



    @Data
    public static class OrderRxReviewDto {
        private String status;// 审核状态
        private Date reviewtime; // 审核时间
        private String rx_img;// 处方图片
    }




}
