package cn.hydee.middle.business.order.service;

import cn.hydee.middle.business.order.entity.OrderRefundBillStatistics;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 订单退款下账预处理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-24
 */
public interface OrderRefundBillStatisticsService extends IService<OrderRefundBillStatistics> {

    int calculateBillRefundOrderRefundAmountByOrderNo(List<Long> orderNoList);
}
