package cn.hydee.middle.business.order.refund.creation.strategy;

import cn.hydee.middle.business.order.Enums.PlatformCodeEnum;
import cn.hydee.middle.business.order.dto.message.NetRefundNotifyMessage;
import cn.hydee.middle.business.order.refund.context.RefundOrderContext;

/**
* @Description: 京东到家创建退款单工厂策略
* @Author: syuson
* @Date: 2021-7-27
*/
public class JddjRefundCreateFactoryStrategy extends AbstractPlatRefundCreateFactoryStrategy {

    public JddjRefundCreateFactoryStrategy() {
        super(PlatformCodeEnum.JD_DAOJIA.getCode());
    }

    @Override
    public RefundOrderContext createPartRefund(NetRefundNotifyMessage message) {
        return super.createPartRefund(message);
    }

    @Override
    public RefundOrderContext createAllRefund(NetRefundNotifyMessage message) {
        return super.createAllRefund(message);
    }

}
