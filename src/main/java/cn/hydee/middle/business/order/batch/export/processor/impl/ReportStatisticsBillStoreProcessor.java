package cn.hydee.middle.business.order.batch.export.processor.impl;

import cn.hydee.middle.business.order.Enums.DsErrorType;
import cn.hydee.middle.business.order.batch.export.constant.ExportTypeConstant;
import cn.hydee.middle.business.order.batch.export.dto.ReportStatisticsBillStoreDTO;
import cn.hydee.middle.business.order.doris.dto.req.OrderReportStatisticsPageQueryReqDto;
import cn.hydee.middle.business.order.doris.dto.rsp.OrderBillQueryByStoreRspDto;
import cn.hydee.middle.business.order.doris.handler.OrderReportStatisticsHandler;
import cn.hydee.middle.business.order.doris.service.OrderReportStatisticsService;
import cn.hydee.middle.business.order.entity.ExportTask;
import cn.hydee.middle.business.order.util.JsonUtil;
import cn.hydee.middle.business.order.v2.manager.ParentStoreManager;
import cn.hydee.starter.exception.WarnException;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
* @Author: syuson
* @Date: 2021-6-24
*/
@Service(ExportTypeConstant.SERVICE_PREFIX + ExportTypeConstant.REPORT_STATISTICS_BILL_STORE)
@Slf4j
public class ReportStatisticsBillStoreProcessor extends AbstractExportTaskProcessor<ReportStatisticsBillStoreDTO> {

    private final OrderReportStatisticsService orderReportStatisticsService;
    private final OrderReportStatisticsHandler orderReportStatisticsHandler;

    public ReportStatisticsBillStoreProcessor(OrderReportStatisticsService orderReportStatisticsService, OrderReportStatisticsHandler orderReportStatisticsHandler) {
        this.orderReportStatisticsService = orderReportStatisticsService;
        this.orderReportStatisticsHandler = orderReportStatisticsHandler;
    }

    @Override
    public Class getExportClass() {
        return ReportStatisticsBillStoreDTO.class;
    }

    @Override
    public List getExportDateList(ExportTask task) {
        OrderReportStatisticsPageQueryReqDto reqDto = JsonUtil.json2Object(task.getRequestJson(),OrderReportStatisticsPageQueryReqDto.class);
        if(!orderReportStatisticsHandler.platCodeOrganizationParamValid(task.getCreateUserId(),task.getMerCode(),reqDto, Boolean.TRUE)){
            return Collections.emptyList();
        }
        List<OrderBillQueryByStoreRspDto> dataList = orderReportStatisticsService.queryOrderBillDataByHistoryStore(reqDto);
        if (CollectionUtils.isEmpty(dataList)) {
            throw WarnException.builder().code(DsErrorType.EXCEL_ROW_NULL.getCode()).
                    tipMessage(DsErrorType.EXCEL_ROW_NULL.getMsg()).build();
        }
        List<ReportStatisticsBillStoreDTO> exportList = dataList.stream().map(OrderBillQueryByStoreRspDto::convert2ExportData).collect(Collectors.toList());
        // 【【上海国药】【订单查询】：订单明细报表增加门店“上级机构”字段】 https://www.tapd.cn/61969829/prong/stories/view/1161969829001033404
        ParentStoreManager.convertReportStatisticsBillStoreExport(reqDto.getMerCode(),exportList);
        return exportList;
    }

    @Override
    public String getFileNamePrefix() {
        return "经营分析-下账报表-导出线上门店数据";
    }

    @Override
    public CellWriteHandler getCellWriteHandler(Integer dataSize) {
        return new SimpleColumnWidthStyleStrategy(20);
    }

}
