package cn.hydee.middle.business.order.yxtadapter.adapterparent.apiimpl.controlller;

import cn.hydee.middle.business.order.yxtadapter.domain.posconfig.OrganizationPosConfigDTO;
import cn.hydee.middle.business.order.yxtadapter.domain.posconfig.OrganizationPosConfigListRequest;
import cn.hydee.middle.business.order.yxtadapter.domain.posconfig.OrganizationPosConfigRequest;
import cn.hydee.middle.business.order.yxtadapter.domain.posconfig.OrganizationPosConfigTreeDTO;
import cn.hydee.middle.business.order.yxtadapter.domain.posconfig.StoreInfoChangeMessage;
import cn.hydee.middle.business.order.yxtadapter.domainservice.posconfig.OrganizationPosConfigGateway;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import io.swagger.annotations.Api;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ds/organization/pos/config")
@Api(tags = "组织机构设置POS类型")
public class OrganizationPosConfigController extends AbstractController {

  @Autowired
  private OrganizationPosConfigGateway organizationPosConfigGateway;

  @PostMapping
  public ResponseBase<Void> config(
      @RequestBody @Validated OrganizationPosConfigRequest request
  ) {
    organizationPosConfigGateway.config(request);
    return ResponseBase.success();
  }

  @PostMapping("/list")
  public ResponseBase<List<OrganizationPosConfigTreeDTO>> configList(@RequestBody @Validated OrganizationPosConfigListRequest request) {
    return generateSuccess(organizationPosConfigGateway.configList(request));
  }

  @GetMapping("/info")
  public ResponseBase<OrganizationPosConfigDTO> configInfo(@RequestParam String organizationCode, @RequestParam Integer organizationType) {
    return generateSuccess(organizationPosConfigGateway.configInfo(organizationCode, organizationType));
  }

  @PostMapping("/store/change/mq")
  public ResponseBase<Void> configInfo(@RequestBody StoreInfoChangeMessage message,@RequestHeader String msgId) {
    organizationPosConfigGateway.storeInfoChangeMQConsume(message, msgId);
    return ResponseBase.success();
  }
}
