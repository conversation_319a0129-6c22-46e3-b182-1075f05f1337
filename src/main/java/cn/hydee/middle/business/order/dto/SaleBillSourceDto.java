package cn.hydee.middle.business.order.dto;

import cn.hydee.middle.business.order.Enums.salebill.SaleBillSourceTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/02/18
 */
@Data
public class SaleBillSourceDto implements Serializable {

    private static final long serialVersionUID = -7095920911376496436L;

    private Integer sourceType;

    public SaleBillSourceDto(Integer sourceType) {
        this.sourceType = sourceType;
    }

    public boolean needSendOrderBillFailSound(){
        if(SaleBillSourceTypeEnum.AUTO_BILL_ORDER_CONSUMER.getCode().equals(this.sourceType)
            || SaleBillSourceTypeEnum.AUTO_BILL_ORDER_RIDER_CONSUMER.getCode().equals(this.sourceType)){
            return true;
        }
        return false;
    }

    public boolean needSendRefundOrderBillFailSound(){
        if(SaleBillSourceTypeEnum.AUTO_BILL_REFUND_ORDER_CONSUMER.getCode().equals(this.sourceType)){
            return true;
        }
        return false;
    }

    public static SaleBillSourceDto buildDefaultBean(){
        return new SaleBillSourceDto(SaleBillSourceTypeEnum.UNKNOWN.getCode());
    }

    public static SaleBillSourceDto buildBean(SaleBillSourceTypeEnum typeEnum){
        return new SaleBillSourceDto(typeEnum.getCode());
    }

}
