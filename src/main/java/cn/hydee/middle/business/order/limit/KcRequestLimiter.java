package cn.hydee.middle.business.order.limit;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 科传请求限流
 *
 * <AUTHOR> (moatkon)
 * @date 2024年01月17日 9:26
 * @email: <EMAIL>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(value = {ElementType.METHOD})
public @interface KcRequestLimiter {

    /**
     * 唯一键
     *
     * @return
     */
    String key();

    /**
     * 限流数
     *
     * @return
     */
    int limitCount() default 1;

    /**
     * 描述内
     *
     * @return
     */
    int seconds() default 1;

    /**
     * 标记来源,如果是人工则不限流
     *
     * @return
     */
    String source() default "";

}
