package cn.hydee.middle.business.order.autopick.controller;


import cn.hydee.middle.business.order.Enums.DsErrorType;
import cn.hydee.middle.business.order.account.check.batch.list.BatchListHandler;
import cn.hydee.middle.business.order.autopick.dto.AutoPickInfo;
import cn.hydee.middle.business.order.autopick.dto.AutoPickInfoReqDto;
import cn.hydee.middle.business.order.service.rocket.MessageProducerService;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
* @Author: syu
* @Date: 2022-7-11
*/
@RestController
@RequestMapping("/${api.version}/ds/pickInfo")
@Api(tags = "机器自动拣货相关业务")
public class OrderAutoPickController extends AbstractController {

    @Autowired
    private MessageProducerService messageProducerService;
	
    @ApiOperation(value = "同步自动拣货信息接口", notes = "ERP通过统一接口平台调用OMS的回传拣货信息接口传输信息给OMS")
    @PostMapping("/sync")
    public ResponseBase<Boolean> sync(@RequestBody AutoPickInfoReqDto req){
        //推送到rocket消息中进行消费
        if(CollectionUtils.isEmpty(req.getData())){
            return ResponseBase.error(DsErrorType.ERP_PICK_INFO_IS_NULL.getCode(),
                    DsErrorType.ERP_PICK_INFO_IS_NULL.getMsg());
        }
        // 限制发送mq的数据大小
        new BatchListHandler<AutoPickInfo>().batchResolve(req.getData(), 50, (items) -> messageProducerService.erpPickInfoMessage(items));
        return generateSuccess(true);
    }
}

