
package cn.hydee.middle.business.order.account.check.strategy.cleandata;

import cn.hydee.middle.business.order.Enums.PlatformCodeEnum;
import cn.hydee.middle.business.order.account.check.base.dto.AccountCheckOrderInfoDto;
import cn.hydee.middle.business.order.account.check.base.dto.OrderRefundInfoDto;
import cn.hydee.middle.business.order.account.check.base.dto.req.CleanDataReqDto;
import cn.hydee.middle.business.order.account.check.base.model.AccountCheckClean;
import cn.hydee.middle.business.order.account.check.base.model.AccountCheckOrigin;
import cn.hydee.middle.business.order.account.check.base.utils.CompareUtil;
import cn.hydee.middle.business.order.account.check.batch.list.BatchListHandler;
import cn.hydee.middle.business.order.account.check.strategy.AbstractDataCleanStrategy;
import cn.hydee.middle.business.order.entity.AccountCheckOmsTemp;
import cn.hydee.middle.business.order.entity.OrderPayInfo;
import cn.hydee.middle.business.order.entity.StoreBillConfig;
import cn.hydee.middle.business.order.mapper.OrderInfoMapper;
import cn.hydee.middle.business.order.service.AccountCheckOmsTempService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**  
 * ClassName:OmsDataClean <br/>  
 * 
 * Date:     2020年11月5日 下午3:56:38 <br/>  
 * <AUTHOR>  
 */
@Component("omsDataClean")
public class OmsDataClean extends AbstractDataCleanStrategy {
	
	@Autowired
	private OrderInfoMapper orderInfoMapper; 
	@Autowired
	private AccountCheckOmsTempService accountCheckOmsTempService;
	
	@Override
	public List<AccountCheckOrigin> getOriginData(CleanDataReqDto reqDto) {
		List<AccountCheckOrderInfoDto> dataList = orderInfoMapper.selectAccountCheckData(reqDto);
		if(CollectionUtils.isEmpty(dataList)) {
			return Collections.emptyList();
		}
		return convetOrigin(dataList);
	}
	
	private List<AccountCheckOrigin> convetOrigin(List<AccountCheckOrderInfoDto> dataList) {
		List<AccountCheckOrigin> originList = new ArrayList<>();
		dataList.forEach(data -> {{
			originList.addAll(convetOrigin(data));
		}});
		return originList;
	}
	
	private List<AccountCheckOrigin> convetOrigin(AccountCheckOrderInfoDto data) {
		if(!data.needHandel()) {
			return Collections.emptyList();
		}
		// 财务数据
		OrderPayInfo orderPayInfo = data.getOrderPayInfo();
		// 下账配置数据
		StoreBillConfig storeBillConfig = data.getStoreBillConfig();
		AccountCheckOrigin originData = AccountCheckOrigin.builder()
				.merCode(data.getMerCode())
				.thirdPlatformCode(data.getThirdPlatformCode())
				.thirdPlatformName(getPlatformname(data))
				.onlineStoreCode(data.getOnlineStoreCode())
				.onlineStoreName(data.getOnlineStoreName())
				.organizationCode(data.getOrganizationCode())
				.organizationName(data.getOrganizationName())
				.orderNo(data.getOrderNo())
				.thirdOrderNo(data.getThirdOrderNo())
				.merchantActualAmount(orderPayInfo.getMerchantActualAmount())// 商家实收
				.totalAmount(orderPayInfo.getTotalAmount())//商品总金额
				.platformDiscount(orderPayInfo.getPlatformDiscount())//平台优惠
				.merchantDiscount(orderPayInfo.getMerchantDiscountSum())//商家优惠
				.brokerageAmount(orderPayInfo.getBrokerageAmount())//交易佣金
				.platformDeliveryFee(orderPayInfo.getPlatformDeliveryFee())//平台配送费
				.merchantDeliveryFee(orderPayInfo.getMerchantDeliveryFee())//商家配送费
				.platformPackFee(orderPayInfo.getPlatformPackFee())//平台打包费
				.merchantPackFee(orderPayInfo.getMerchantPackFee())//商家打包费
				.freightFeeFetch(storeBillConfig.getFreightFeeFetch())//配送费 1-平台收取 2-商家收取
				.packageFeeFetch(storeBillConfig.getPackageFeeFetch())//包装费 1-平台收取 2-商家收取
				.orderTime(data.getCreated())
				.billTime(data.getBillTime())
				.build();
		// 退款单	
		List<OrderRefundInfoDto> orderRefundList = data.getOrderRefundList();
		// 订单没有退款单
		if(CollectionUtils.isEmpty(orderRefundList)) {
			return Collections.singletonList(originData);
		}
		List<AccountCheckOrigin> originList = new ArrayList<>();
		originList.add(originData);
		orderRefundList.forEach(refund -> {{
			originList.add(AccountCheckOrigin.builder()
					.merCode(data.getMerCode())
					.thirdPlatformCode(data.getThirdPlatformCode())
					.thirdPlatformName(getPlatformname(data))
					.onlineStoreCode(data.getOnlineStoreCode())
					.onlineStoreName(data.getOnlineStoreName())
					.organizationCode(data.getOrganizationCode())
					.organizationName(data.getOrganizationName())
					.orderNo(data.getOrderNo())
					.thirdOrderNo(data.getThirdOrderNo())
					.merchantActualAmount(refund.getShopRefund().negate())// 商家实收(退款单： 商家退款总金额)
					.totalAmount(refund.getTotalFoodAmount().negate())//商品总金额(退款单：退款商品总金额)
					.platformDiscount(refund.getPlatformDiscountRefund().negate())//平台优惠(退款单：退平台优惠)
					.merchantDiscount(refund.getShopDiscountRefund().negate())//商家优惠(退款单：退还商家优惠)
					.brokerageAmount(refund.getFeeRefund().negate())//交易佣金(退款单:退还佣金)
					.platformDeliveryFee(refund.getPlatformRefundDeliveryFee().negate())//平台配送费(退款单：退平台配送费)
					.merchantDeliveryFee(refund.getMerchantRefundPostFee().negate())//商家配送费(退款单：退商家配送费)
					.platformPackFee(refund.getPlatformRefundPackFee().negate())//平台打包费(退款单：退平台包装费)
					.merchantPackFee(refund.getMerchantRefundPackFee().negate())//商家打包费(退款单：退商家包装费)
					.build());
		}});
		return originList;
	}
	
	private String getPlatformname(AccountCheckOrderInfoDto data) {
		PlatformCodeEnum platformCodeEnum = PlatformCodeEnum.getByCode(data.getThirdPlatformCode());
		if(null == platformCodeEnum) {
			return "未知";
		}
		return platformCodeEnum.getType();
	}
	
	@Override
	public List<String> saveDataReturnCompareStr(List<AccountCheckClean> cleanData) {

		List<AccountCheckOmsTemp> dataList = cleanData.stream().map(source -> {{
			AccountCheckOmsTemp temp = new AccountCheckOmsTemp();
			BeanUtils.copyProperties(source, temp);
			// TODO 解析比较对象
			String compareStr = CompareUtil.compareStr(source);
			temp.setCompareStr(compareStr);
			return temp;
		}}).collect(Collectors.toList());
		
		// 分批处理
		new BatchListHandler<AccountCheckOmsTemp>().batchResolve(dataList, 1000, (items) -> {
			accountCheckOmsTempService.saveUpdateBatch(items);
		});

		return dataList.stream().map(AccountCheckOmsTemp::getCompareStr).collect(Collectors.toList());
	}

}
  
