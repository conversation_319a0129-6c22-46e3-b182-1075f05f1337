package cn.hydee.middle.business.order.refund.container;

import cn.hydee.middle.business.order.feign.MiddleIdClient;
import cn.hydee.middle.business.order.mapper.*;
import cn.hydee.middle.business.order.refund.calc.ICalculateRefundSinkAmountService;
import cn.hydee.middle.business.order.service.refund.IPartRefundService;
import cn.hydee.middle.business.order.service.refund.RefundMessageDetailService;
import cn.hydee.middle.business.order.service.suport.OrderBasicService;
import lombok.Data;
import org.springframework.data.redis.core.StringRedisTemplate;

/**
 * SpringBean容器
 * <AUTHOR> zhiye
 * @version *******
 * @date 2020/09/15
 */
@Data
public class BeanContainer {

    public BeanContainer(RefundOrderMapper refundOrderMapper, RefundDetailMapper refundDetailMapper,
                         RefundPictureMapper refundPictureMapper, OrderInfoMapper orderInfoMapper,
                         OrderPayInfoMapper orderPayInfoMapper, OrderDetailMapper orderDetailMapper,
                         MiddleIdClient middleIdClient, StringRedisTemplate stringRedisTemplate,
                         IPartRefundService partRefundService, OrderBasicService orderBasicService,
                         ICalculateRefundSinkAmountService calculateRefundSinkAmountService,
                         RefundMessageDetailService refundMessageDetailService,
                         InnerStoreDictionaryMapper innerStoreDictionaryMapper) {
        this.refundOrderMapper = refundOrderMapper;
        this.refundDetailMapper = refundDetailMapper;
        this.refundPictureMapper = refundPictureMapper;
        this.orderInfoMapper = orderInfoMapper;
        this.orderPayInfoMapper = orderPayInfoMapper;
        this.orderDetailMapper = orderDetailMapper;
        this.middleIdClient = middleIdClient;
        this.stringRedisTemplate = stringRedisTemplate;
        this.partRefundService = partRefundService;
        this.orderBasicService = orderBasicService;
        this.calculateRefundSinkAmountService = calculateRefundSinkAmountService;
        this.refundMessageDetailService = refundMessageDetailService;
        this.innerStoreDictionaryMapper = innerStoreDictionaryMapper;
    }

    private RefundOrderMapper refundOrderMapper;

    private RefundDetailMapper refundDetailMapper;

    private RefundPictureMapper refundPictureMapper;

    private OrderInfoMapper orderInfoMapper;

    private OrderPayInfoMapper orderPayInfoMapper;

    private OrderDetailMapper orderDetailMapper;

    private MiddleIdClient middleIdClient;

    private StringRedisTemplate stringRedisTemplate;

    private IPartRefundService partRefundService;

    private OrderBasicService orderBasicService;

    private ICalculateRefundSinkAmountService calculateRefundSinkAmountService;

    private RefundMessageDetailService refundMessageDetailService;

    private InnerStoreDictionaryMapper innerStoreDictionaryMapper;
}
