package cn.hydee.middle.business.order.dto.req.tracecode;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class MedicalTraceCodeBatchCodeReqDto {
    @ApiModelProperty(value = "订单编号")
    @NotNull(message = "orderNo:订单编号不能为空")
    private Long orderNo;
    @ApiModelProperty(value = "追溯码")
    @NotNull(message = "ecode:追溯码不能为空")
    private String ecode;
    @ApiModelProperty(value = "商品编码")
    @NotNull(message = "erpCode:商品编码不能为空")
    private String erpCode;
}
