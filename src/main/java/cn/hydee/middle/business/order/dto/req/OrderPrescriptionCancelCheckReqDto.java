package cn.hydee.middle.business.order.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class OrderPrescriptionCancelCheckReqDto {

    @ApiModelProperty(value="商户编码",notes="商户编码")
    private String merCode;

    @ApiModelProperty(value="电商云平台订单号",notes="电商云平台订单号")
    @NotNull(message = "订单号不能为空")
    private Long  orderNo;

}
