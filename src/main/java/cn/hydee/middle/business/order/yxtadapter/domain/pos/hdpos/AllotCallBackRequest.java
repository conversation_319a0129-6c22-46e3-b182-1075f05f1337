package cn.hydee.middle.business.order.yxtadapter.domain.pos.hdpos;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/6/6
 * @since 1.0
 */
@Data
public class AllotCallBackRequest implements Serializable {

    @ApiModelProperty("pos调拨单号")
    private String posAllotNo;

    @ApiModelProperty("心云调拨单号")
    @NotNull(message="心云调拨单号不能为空")
    private String omsAllotNo;

    @ApiModelProperty("调拨状态")
    @NotNull(message="调拨状态不能为空")
    private String allotStatus;

    @ApiModelProperty("失败原因")
    private String failMessage;
}
