package cn.hydee.middle.business.order.proxy;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.dto.rsp.OnlineStoreInfoRspDto;
import cn.hydee.middle.business.order.entity.OrderDetail;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.entity.StoreBillConfig;
import cn.hydee.middle.business.order.service.ErpBillService;
import cn.hydee.middle.business.order.service.OrderHandlerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * cn.hydee.middle.business.order.proxy
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/9/3 10:56
 **/
@Component
public class LockStockProxy {

    @Autowired
    private OrderHandlerService orderHandlerService;
    @Autowired
    private ErpBillService erpBillService;

    /**
     * 切换门店锁库存
     * tapd需求地址：  https://www.tapd.cn/39022996/prong/stories/view/1139022996001004137
     * 新门店是否配置创建订单锁库存，如果不锁库存，则换门店后不锁ERP库存，且不判断库存是否充足，即不需要判断库存不足异常；
     * @param onlineStoreInfoRspDto 门店配置信息 不可为null
     * @param storeBillConfig 门店下账配置信息  可以为null
     * @param orderInfo 订单信息
     * @param orderDetailList 订单商品列表
     */
    public void switchStoreLockStock(OnlineStoreInfoRspDto onlineStoreInfoRspDto, StoreBillConfig storeBillConfig, OrderInfo orderInfo, List<OrderDetail> orderDetailList){
        //配置创建订单锁库存，如果不锁库存，则换门店后不锁ERP库存，且不判断库存是否充足，即不需要判断库存不足异常；
        if(DsConstants.INTEGER_ZERO.equals(onlineStoreInfoRspDto.getWhetherInventoryLocked())){
            return;
        }
        orderHandlerService.commodityStockDeductOther(orderInfo,orderDetailList, DsConstants.NEED_RETRY,"切换门店锁库存");
    }

    /**
     * 调用接单接口
     * tapdbug地址：  https://www.tapd.cn/60939819/bugtrace/bugs/view/1160939819001004643
     * 如果配置了创建新订单 不锁库存 则无需锁库存
     * @param onlineStoreInfoRspDto
     * @param orderInfo
     * @param orderDetailList
     */
    public void  callOrderReceiveLockStock(OnlineStoreInfoRspDto onlineStoreInfoRspDto,OrderInfo orderInfo, List<OrderDetail> orderDetailList){
        if(DsConstants.INTEGER_ZERO.equals(onlineStoreInfoRspDto.getWhetherInventoryLocked())){
            return;
        }
        orderHandlerService.commodityStockDeductOther(orderInfo,orderDetailList,DsConstants.NEED_RETRY,"暂未使用");
    }
    
    /**
     * 
     * switchStoreLockStockObOrder:ob预约单，锁库存. <br/>  
     *  
     * @param onlineStoreInfoRspDto
     * @param storeBillConfig
     * @param orderInfo
     * @param orderDetailList  
     * @return 是否需要锁库存 
     * <AUTHOR>  
     * @date 2020年9月29日 下午4:02:40
     */
    public boolean switchStoreLockStockObOrder(OnlineStoreInfoRspDto onlineStoreInfoRspDto, StoreBillConfig storeBillConfig, OrderInfo orderInfo, List<OrderDetail> orderDetailList){
        //配置创建订单锁库存，如果不锁库存，则换门店后不锁ERP库存，且不判断库存是否充足，即不需要判断库存不足异常；
        if(DsConstants.INTEGER_ZERO.equals(onlineStoreInfoRspDto.getWhetherInventoryLocked())){
            return Boolean.FALSE;
        }
        if (DsConstants.CALLERPFLAG_CALL.equals(orderInfo.getCallErpFlag())){
            erpBillService.erpTakeWithEx(orderInfo, orderDetailList,DsConstants.NO_NEED_RETRY);
        }
        return Boolean.TRUE;
    }
}
