package cn.hydee.middle.business.order.module.orderdiff.handler.config;

import cn.hydee.middle.business.order.module.orderdiff.handler.OrderDiffHandler;
import cn.hydee.middle.business.order.module.orderdiff.handler.constant.OrderDiffConstant;
import cn.hydee.middle.business.order.module.orderdiff.handler.event.EventHandlerFactory;
import cn.hydee.middle.business.order.util.SpringBeanUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2021/12/09
 */
@Configuration
@DependsOn("orderSpringBeanUtils")
public class OrderDiffHandlerConfig {

    @Bean("orderDiffEventHandlerFactory")
    public EventHandlerFactory register(){
        EventHandlerFactory factory = new EventHandlerFactory();
        Stream.of(OrderDiffConstant.OrderDiffType.values()).forEach(task -> {
            factory.setHandler(task.getType(), SpringBeanUtils.getBean(task.getServiceName(), OrderDiffHandler.class));
        });
        return factory;
    }
}
