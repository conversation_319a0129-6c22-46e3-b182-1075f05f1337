package cn.hydee.middle.business.order.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/18 15:46
 */
@Data
public class StockDeductReqDto {
    /** true增加库存 false 减少库存 */
    private Boolean add;
    /** 商家编码 */
    private String merCode;
    /** 订单号 */
    private Long orderNo;
    /** 操作人 */
    private String userName;
    /** 线上门店编码 */
    private String onlineStoreCode;
    /** 平台编码 */
    private String platformCode;
    private String note;
    private List<StockCommodity> stockList;

    @ApiModelProperty("线下门店编码")
    private String storeCode;
    @ApiModelProperty("线下门店id")
    private String storeId;

    @Data
    public static class StockCommodity{
        /** 流水号 */
        private Long serialNumber;

        /** erp编码 */
        private String erpCode;
        /** 操作的库存数量 */
        private Integer stock;

        public StockCommodity(String erpCode, Integer stock,Long serialNumber){
            this.erpCode = erpCode;
            this.stock = stock;
            this.serialNumber = serialNumber;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            };
            StockCommodity that = (StockCommodity) o;
            return Objects.equals(erpCode, that.erpCode) &&
                    Objects.equals(stock, that.stock);
        }

        @Override
        public int hashCode() {
            return Objects.hash(erpCode, stock);
        }
    }
}
