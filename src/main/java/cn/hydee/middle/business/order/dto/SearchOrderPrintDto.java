package cn.hydee.middle.business.order.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/06/07
 */
@Data
public class SearchOrderPrintDto {

    @ApiModelProperty(value = "打印类型，1-全部结果打印 2-部分打印")
    private Integer printType;
    @ApiModelProperty(value = "待打印系统订单号集合，2-部分打印时，必须入参")
    private List<Long> orderNoList;
    @ApiModelProperty(value = "待打印系统订单号集合，1-全部结果打印时，必须入参")
    private OrderQuerySearchReqDto searchOrderPageReqDto;
    @ApiModelProperty(value = "打印模板类型  1-接单小票模板 2-拣货小票模板")
    private Integer templateType;
}
