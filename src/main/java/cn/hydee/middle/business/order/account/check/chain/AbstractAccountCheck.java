
package cn.hydee.middle.business.order.account.check.chain;

public abstract class AbstractAccountCheck<T> {
	protected static String jobId ;

	// 对账结果, 0：正常，1：金额差异，2：已下账 oms单边，3：OMS未下账
	public static final Integer EQUAL = 0;
	public static final Integer AMOUNT_DIFF = 1;
	public static final Integer OMS_MORE = 2;
	public static final Integer NO_BILL = 3;

	// 对账处理类型, 0：正常 1 平账  99：未知
	public static final Integer NORMAL = 0;
	public static final Integer BALANCE = 1;
	public static final Integer UNKNOWN = 99;

	// 对账结果处置,0：未处置 1：已处置
	public static final Integer WAIT_HANDLE = 0;
	public static final Integer HANDLED = 1;
	public static final Integer NO_NEED_HANDLE = 2;

}
