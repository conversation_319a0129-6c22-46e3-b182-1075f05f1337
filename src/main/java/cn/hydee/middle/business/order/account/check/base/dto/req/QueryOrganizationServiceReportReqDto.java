
package cn.hydee.middle.business.order.account.check.base.dto.req;

import cn.hydee.middle.business.order.dto.StoreServiceReportAllSnapshotDto;
import cn.hydee.middle.business.order.entity.OrganizationServiceReport;
import cn.hydee.starter.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class QueryOrganizationServiceReportReqDto extends PageBase {
	
	@ApiModelProperty(value = "商家编码", hidden = true)
    private String merCode;
    @ApiModelProperty(value = "下单开始时间,格式 yyyymmdd",required = true)
    @NotNull
    private String orderTimeStart;
    @ApiModelProperty(value = "下单结束时间,格式 yyyymmdd",required = true)
    @NotNull
    private String orderTimeEnd;
    @ApiModelProperty(value = "排序字段，默认有效订单数;organizationCode,pickOvertimeOrders,averagePickMins,pickOvertimePercent,deliveryOvertimeOrders,deliveryOvertimePercent,averageDeliveryMins,\n" +
            "valideOrders,invalideOrders,invalidOrdePercent,lackCommidityOrders,lackCommidityPercent")
    private String sortField;
    @ApiModelProperty(value = "排序顺序：升序 asc 降序 desc")
    private String sortSequence;
    @ApiModelProperty(value = "机构编码LIST，全部就传空")
    private List<String> organizationCodeList;

    @ApiModelProperty(value = "总数（后端业务使用）")
    private StoreServiceReportAllSnapshotDto totalData;

}
  
