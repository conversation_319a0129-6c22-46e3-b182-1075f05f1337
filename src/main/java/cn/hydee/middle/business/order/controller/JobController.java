package cn.hydee.middle.business.order.controller;

import cn.hydee.middle.business.order.service.JobService;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/1/14 13:43
 */
@RestController
@RequestMapping("/${api.version}/ds/job")
@Api(tags = "定时任务接口")
public class JobController extends AbstractController {

    @Autowired
    private JobService jobService;

    @ApiOperation(value = "处理未成功通知", notes = "处理未成功通知")
    @GetMapping("/notify/net")
    public ResponseBase<Void> notifyNet(){
        jobService.notifyNet();
        return generateSuccess(null);
    }

    @ApiOperation(value = "处理出问题的订单", notes = "处理出问题的订单")
    @GetMapping("/order/problem")
    public ResponseBase<Void> processProblemOrder(){
        jobService.processProblemOrder();
        return generateSuccess(null);
    }
}
