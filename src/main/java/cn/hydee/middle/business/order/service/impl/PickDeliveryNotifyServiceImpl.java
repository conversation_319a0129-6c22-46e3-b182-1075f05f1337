package cn.hydee.middle.business.order.service.impl;

import cn.hydee.middle.business.order.Enums.OrderStateEnum;
import cn.hydee.middle.business.order.Enums.SoundTypeEnum;
import cn.hydee.middle.business.order.dto.message.NotifyMessage;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.service.OrderLogService;
import cn.hydee.middle.business.order.service.PickDeliveryNotifyService;
import cn.hydee.middle.business.order.service.rocket.MessageProducerService;
import cn.hydee.middle.business.order.service.suport.OrderBasicService;
import cn.hydee.middle.business.order.util.Const;
import cn.hydee.middle.business.order.util.RedisConstUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Set;

/**
 * @desc
 * @date 2020-12-21 20:57:13
 **/
@Slf4j
@Service("pickDeliveryNotifyService")
public class PickDeliveryNotifyServiceImpl implements PickDeliveryNotifyService {
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private OrderBasicService orderBasicService;
    @Autowired
    private MessageProducerService messageProducerService;
    @Autowired
    private OrderLogService orderLogService;

    @Override
    public void pickDeliveryNotify() {
        try {
            Set<String> keys = stringRedisTemplate.keys(RedisConstUtil.NOTIFY_KEY_PREFIX + "*");
            for (String key : keys) {
                long handleTime = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
                Set<String> notifies = stringRedisTemplate.opsForZSet().rangeByScore(key, 0, handleTime);
                if (notifies.size() > 2000) {
                    log.info("pick-delivery job notify got records more than 2000 for key {} , size {} ", key, notifies.size());
                }

                notifies.forEach(notify -> {
                    final NotifyMessage notifyMessage = JSON.parseObject(notify, NotifyMessage.class);
                    OrderInfo orderInfo = orderBasicService.getOrderBaseWithCheck(Long.parseLong(notifyMessage.getOrderNo()));
                    if (notifyMessage.getHandleType().equals(Const.PICK_NOTIFY)) {
                        if (orderInfo.getOrderState() <= OrderStateEnum.UN_PICK.getCode()) {
                            // 发送提醒,只发送一次
                            messageProducerService.producePickDeliveryNotifyMessage(orderInfo, notifyMessage, SoundTypeEnum.PICK_TIME_EXCEED);
                            orderLogService.savePickNotify(notifyMessage);
                        }
                    } else if (notifyMessage.getHandleType().equals(Const.DELIVERY_NOTIFY)) {
                        if (orderInfo.getOrderState() < OrderStateEnum.POSTING.getCode()) {
                            // 发送提醒,只发送一次
                            messageProducerService.producePickDeliveryNotifyMessage(orderInfo, notifyMessage, SoundTypeEnum.DELIVERY_TIME_EXCEED);
                            orderLogService.saveDeliveryNotify(notifyMessage);
                        }
                    }
                });
                // 批量移除处理过的记录
                stringRedisTemplate.opsForZSet().removeRangeByScore(key,0,handleTime);
            }
        } catch (Exception e) {
            log.info("pick-delivery job notify handle error,", e);
        }
    }

    @Override
    public void removeAllZSetNotify() {
        try {
            Set<String> keys = stringRedisTemplate.keys(RedisConstUtil.NOTIFY_KEY_PREFIX + "*");
            stringRedisTemplate.delete(keys);
        }catch (Exception e){
            log.info("removeAllZSetNotify job error,", e);
        }
    }

}
