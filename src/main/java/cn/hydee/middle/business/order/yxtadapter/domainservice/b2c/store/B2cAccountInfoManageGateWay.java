package cn.hydee.middle.business.order.yxtadapter.domainservice.b2c.store;

import cn.hydee.middle.business.order.entity.ErpBillInfo;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.entity.OrderPayInfo;
import cn.hydee.middle.business.order.entity.RefundOrder;
import cn.hydee.middle.business.order.yxtadapter.domain.pos.hdpos.OrderBillResponse;
import cn.hydee.starter.dto.ResponseBase;

public interface B2cAccountInfoManageGateWay {

    /**
     * 获取B2C下账信息
     * */
    ErpBillInfo obtainErpBillInfo(Long orderNo);
    /**
     * 获取B2C订单支付信息
     * */
    OrderPayInfo obtainOrderPayInfo(Long orderNo);


    /**
     * B2C订单下帐成功后操作
     * 修改oms_order_info
     * 处理B2C库存
     * */
    void afterOrderAccountSuccess(OrderInfo orderInfo);

    /**
     * B2C订单下帐失败后操作
     * 修改oms_order_info
     * */
    void afterOrderAccountFail(OrderInfo orderInfo, ResponseBase<OrderBillResponse> responseBase);

    /**
     * 退款单下账成功后处理
     * */
    void afterRefundAccountSuccess(String thirdRefundNo,String posNo, String serviceMode);

    /**
     * 退款单下账失败后处理
     * */
    void afterRefundAccountFail(RefundOrder refundOrder, String errMsg, OrderInfo orderInfo);
}
