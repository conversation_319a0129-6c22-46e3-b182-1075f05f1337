package cn.hydee.middle.business.order.Enums;

import lombok.Getter;

@Getter
public enum YNRefundStatus {

    //120--退单申请 110--订单取消 122--同意退款 124--驳回退单 125--退单取消 126--确认收货 128--退单完成
    WAIT_REFUND(120, "退单申请"),
    ORDER_CANCEL(110, "订单取消"),
    AGREE_REFUND(122, "同意退款"),
    REJECT_REFUND(124, "驳回退单"),
    REFUND_CANCEL(125, "退单取消"),
    CONFIRM_RECEIPT(126, "确认收货"),
    REFUND_COMPLETE(128, "退单完成"),
    UNKNOWN(-1, "未知");

    private final int code;
    private final String desc;

    YNRefundStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static YNRefundStatus getByCode(int code) {
        for (YNRefundStatus status : YNRefundStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return UNKNOWN;
    }
}
