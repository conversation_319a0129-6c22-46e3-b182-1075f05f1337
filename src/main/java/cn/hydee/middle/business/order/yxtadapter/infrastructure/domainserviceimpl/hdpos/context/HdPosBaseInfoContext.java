package cn.hydee.middle.business.order.yxtadapter.infrastructure.domainserviceimpl.hdpos.context;

import cn.hydee.middle.business.order.entity.*;
import cn.hydee.middle.business.order.yxtadapter.domain.dictionary.InnerStoreDictionary;
import lombok.Data;

import java.util.List;

@Data
public class HdPosBaseInfoContext {

    public HdPosBaseInfoContext(){}
    public HdPosBaseInfoContext(OrderInfo orderInfo, OrderPayInfo orderPayInfo, List<OrderDetail> orderDetailList) {
        this.orderInfo = orderInfo;
        this.orderPayInfo = orderPayInfo;
        this.orderDetailList = orderDetailList;
    }

    public HdPosBaseInfoContext(OrderInfo orderInfo, OrderPayInfo orderPayInfo,List<OrderDetail> orderDetailList, RefundOrder refundOrder) {
        this.orderInfo = orderInfo;
        this.orderPayInfo = orderPayInfo;
        this.orderDetailList = orderDetailList;
        this.refundOrder = refundOrder;
    }
    /**
     * 正单信息
     * */
    private OrderInfo orderInfo;

    /**
     * 订单支付信息
     * */
    private OrderPayInfo orderPayInfo;

    /**
     * 订单明细
     * */
    private List<OrderDetail> orderDetailList;

    /**
     * 退款单信息
     * */
    private RefundOrder refundOrder;

    /**
     * 退款明细
     * */
    private List<RefundDetail> refundDetailList;

    /**
     * 拣货信息
     * */
    private List<OrderPickInfo> orderPickInfoList;

    /**
     * 店铺下账配置
     * */
    private StoreBillConfig storeBillConfig;

    /**
     * ERP退款下账金额信息表
     * */
    private ErpRefundInfo erpRefundInfo;

    /**
     * 海典 pos 枚举 H1 H2
     * @see cn.hydee.middle.business.order.Enums.PosModeEnum
     * */
    private Integer posMode;

    private InnerStoreDictionary innerStoreDictionary;

}
