package cn.hydee.middle.business.order.controller.open;

import cn.hydee.middle.business.order.dto.req.customer.CustomerPlatRefundProcessingRequest;
import cn.hydee.middle.business.order.dto.rsp.SimpleOrderNoResDTO;
import cn.hydee.middle.business.order.dto.rsp.customer.AreaResDTO;
import cn.hydee.middle.business.order.dto.req.customer.ReceiverInfoRequest;
import cn.hydee.middle.business.order.dto.req.customer.SaveAddressRequest;
import cn.hydee.middle.business.order.dto.req.customer.CustomerLogisticRequest;
import cn.hydee.middle.business.order.dto.req.customer.CustomerOrderNoRequest;
import cn.hydee.middle.business.order.dto.req.customer.CustomerYxdjOrderPageRequest;
import cn.hydee.middle.business.order.dto.req.customer.CustomerRefundRequest;
import cn.hydee.middle.business.order.dto.req.customer.CustomerYxdjRefundOrderPageRequest;
import cn.hydee.middle.business.order.dto.req.customer.SellerRemarkRequest;
import cn.hydee.middle.business.order.dto.rsp.ReceiverInfoRsp;
import cn.hydee.middle.business.order.dto.rsp.customer.CustomerLogisticResponse;
import cn.hydee.middle.business.order.dto.rsp.customer.CustomerOrderResponse;
import cn.hydee.middle.business.order.dto.rsp.customer.CustomerRefundOrderResponse;
import cn.hydee.middle.business.order.service.ZhiNengKeFuService;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/7/22 10:50
 */
@RestController
@RequestMapping("/order/customer/service")
@Api(tags = "智能客服对外接口")
@Slf4j
public class ZhiNengKeFuController extends AbstractController {
    @Autowired
    private ZhiNengKeFuService zhiNengKeFuService;
    //ok
    @ApiOperation(value = "一心到家订单查询-（线上线下单）", notes = "客服订单查询")
    @PostMapping("/yxdj/orderPage")
    public ResponseBase<IPage<CustomerOrderResponse>> yxdjOrderPage(@Validated @RequestBody CustomerYxdjOrderPageRequest request, BindingResult result) {
        this.checkValid(result);
        log.info("/order/customer/service/orderPage req:{}", JSONObject.toJSONString(request));
        IPage<CustomerOrderResponse> page = zhiNengKeFuService.yxdjOrderPage(request);
        log.info("/order/customer/service/orderPage res:{}", JSONObject.toJSONString(page));
        return generateSuccess(page);
    }
    //ok
    @ApiOperation(value = "客服一心到家退单查询（线上线下单）", notes = "客服一心到家退单查询")
    @PostMapping("/yxdj/refundOrderPage")
    public ResponseBase<IPage<CustomerRefundOrderResponse>> yxdjOrderRefundPage(@Validated @RequestBody CustomerYxdjRefundOrderPageRequest request, BindingResult result) {
        this.checkValid(result);
        log.info("/order/customer/service/orderPage req:{}", JSONObject.toJSONString(request));
        IPage<CustomerRefundOrderResponse> page = zhiNengKeFuService.yxdjRefundOrderPage(request);
        log.info("/order/customer/service/orderPage res:{}", JSONObject.toJSONString(page));
        return generateSuccess(page);
    }

    @ApiOperation(value = "一心到家订单审核", notes = "一心到家B2C订单退款审核")
    @PostMapping("/yxdj/audit")
    public ResponseBase<Boolean> refundAudit(@Validated @RequestBody CustomerPlatRefundProcessingRequest request, BindingResult result) {
        this.checkValid(result);
        log.info("/order/customer/service/audit req:{}", JSONObject.toJSONString(request));
        return zhiNengKeFuService.refundAudit(request);
    }

    //ok
    @ApiOperation(value = "根据订单号查询订单(公域)", notes = "根据订单号查询订单")
    @PostMapping("/orderNo/order")
    public ResponseBase<CustomerOrderResponse> order(@Validated @RequestBody CustomerOrderNoRequest request, BindingResult result) {
        this.checkValid(result);
        log.info("/order/customer/service/logistic req:{}", JSONObject.toJSONString(request));
        CustomerOrderResponse customerOrderResponse = zhiNengKeFuService.order(request);
        log.info("/order/customer/service/logistic res:{}", JSONObject.toJSONString(customerOrderResponse));
        return generateSuccess(customerOrderResponse);
    }

    //ok
    @ApiOperation(value = "客服订单备注", notes = "客服订单备注")
    @PostMapping("/remark")
    public ResponseBase<Boolean> remark(@Validated @RequestBody SellerRemarkRequest request, BindingResult result) {
        this.checkValid(result);
        log.info("/order/customer/service/remark req:{}", JSONObject.toJSONString(request));
        Boolean remark = zhiNengKeFuService.remark(request);
        log.info("/order/customer/service/remark res:{}",remark);
        return generateSuccess(remark);
    }
    //ok
    @ApiOperation(value = "订单部分款申请（仅一心到家商城订单）", notes = "订单部分款申请（仅一心到家商城订单）")
    @PostMapping("/refund")
    public ResponseBase<Boolean> refund(@Validated @RequestBody CustomerRefundRequest request, BindingResult result) {
        this.checkValid(result);
        log.info("/order/customer/service/refund req:{}", JSONObject.toJSONString(request));
        ResponseBase<Boolean> refund = zhiNengKeFuService.refund(request);
        log.info("/order/customer/service/refund res:{}", refund);
        return refund;
    }

    @ApiOperation(value = "订单物流查询", notes = "订单物流查询")
    @PostMapping("/logistic")
    public ResponseBase<List<CustomerLogisticResponse>> logistic(@Validated @RequestBody CustomerLogisticRequest request, BindingResult result) {
        this.checkValid(result);
        log.info("/order/customer/service/logistic req:{}", JSONObject.toJSONString(request));
        List<CustomerLogisticResponse> logistic = zhiNengKeFuService.logistic(request);
//        if(CollectionUtils.isEmpty(logistic)){
//            logistic = mock();
//        }
        log.info("/order/customer/service/logistic res:{}", JSONObject.toJSONString(logistic));
        return generateSuccess(logistic);
    }

    private List<CustomerLogisticResponse> mock(){
        String s= "[{\"data\":[{\"areaCode\":\"CN440300000000\",\"areaName\":\"广东,深圳市\",\"context\":\"B2C订单未对接,测试数据,已签收：【浦老师】签收网点：【广东深圳海吉星公司油甘浦分部】\",\"ftime\":\"2023-02-25 17:38:58\",\"status\":\"签收\",\"time\":\"2023-02-25 17:38:58\"},{\"areaCode\":\"CN440300000000\",\"areaName\":\"广东,深圳市\",\"context\":\"派送中：【广东深圳海吉星公司油甘浦分部】，查单电话：13650067241;派件员是：李艳雨(15112841996)\",\"ftime\":\"2023-02-25 17:37:26\",\"status\":\"派件\",\"time\":\"2023-02-25 17:37:26\"},{\"context\":\"到达：【广东深圳海吉星公司油甘浦分部】，查单电话：13650067241;上级网点：【广东深圳分拨中心】，查单电话：0755-36551219\",\"ftime\":\"2023-02-25 12:32:08\",\"status\":\"在途\",\"time\":\"2023-02-25 12:32:08\"},{\"areaCode\":\"CN440303000000\",\"areaName\":\"广东,深圳市,罗湖区\",\"context\":\"运送中：【广东深圳分拨中心】，查单电话：0755-36551219;正发往：【广东深圳海吉星公司】，查单电话：/\",\"ftime\":\"2023-02-25 08:54:49\",\"status\":\"在途\",\"time\":\"2023-02-25 08:54:49\"},{\"context\":\"到达：【广东深圳分拨中心】，查单电话：0755-36551219;上级网点：【广东佛山分拨中心】，查单电话：0757-82323247\",\"ftime\":\"2023-02-25 07:22:09\",\"status\":\"在途\",\"time\":\"2023-02-25 07:22:09\"},{\"areaCode\":\"CN440600000000\",\"areaName\":\"广东,佛山市\",\"context\":\"运送中：【广东佛山分拨中心】，查单电话：0757-82323247;正发往：【广东深圳分拨中心】，查单电话：0755-36551219\",\"ftime\":\"2023-02-25 01:25:46\",\"status\":\"在途\",\"time\":\"2023-02-25 01:25:46\"},{\"context\":\"到达：【广东佛山分拨中心】，查单电话：0757-82323247;上级网点：【广东广州茶滘公司】，查单电话：020-39464267\",\"ftime\":\"2023-02-25 01:09:48\",\"status\":\"在途\",\"time\":\"2023-02-25 01:09:48\"},{\"areaCode\":\"CN440103018000\",\"areaName\":\"广东,广州市,荔湾区,茶滘\",\"context\":\"运送中：【广东广州茶滘公司】，查单电话：020-39464267;正发往：【广东佛山分拨中心】，查单电话：0757-82323247\",\"ftime\":\"2023-02-24 23:11:49\",\"status\":\"在途\",\"time\":\"2023-02-24 23:11:49\"},{\"context\":\"已开单：【广东广州茶滘公司】，查单电话：020-39464267;开单员是：左威亮(16620043445)\",\"ftime\":\"2023-02-24 19:27:39\",\"status\":\"揽收\",\"time\":\"2023-02-24 19:27:39\"}],\"companyName\":\"韵达快运\",\"companyCode\":\"yundakuaiyun\",\"number\":\"*********\"}]";
        return JSONObject.parseArray(s,CustomerLogisticResponse.class);
    }

    //OK
    @ApiOperation(value = "查询商户维护的行政区域", notes = "查询商户维护的行政区域")
    @PostMapping("/area/merchant")
    public ResponseBase<List<AreaResDTO>> queryAdministrationAreaByMerCode() {
        return generateSuccess(zhiNengKeFuService.queryAllArea());
    }

    //ok
    @ApiOperation(value = "修改收货人信息", notes = "修改收货人信息")
    @PostMapping("/changeAddressDetail")
    public ResponseBase<String> changeAddressDetail(@Validated @RequestBody SaveAddressRequest dto) {
        return  zhiNengKeFuService.changeAddressDetail(dto);
    }
    //ok
    @ApiOperation(value = "获取收货人信息", notes = "获取收货人信息")
    @PostMapping("/getReceiverInfo")
    public ResponseBase<ReceiverInfoRsp> getReceiverInfo(@RequestBody ReceiverInfoRequest receiverInfoRequest) {
        return generateSuccess(zhiNengKeFuService.getReceiverInfo(receiverInfoRequest));
    }

    @ApiOperation(value = "根据平台订单号获取系统订单号", notes = "根据平台订单号获取系统订单号")
    @GetMapping("/getSimpleOrderNo")
    public ResponseBase<SimpleOrderNoResDTO> getSimpleOrderNo(@RequestParam("thirdPlatformCode") String thirdPlatformCode, @RequestParam("thirdOrderNo") String thirdOrderNo) {
        return generateSuccess(zhiNengKeFuService.getSimpleOrderNo(thirdPlatformCode,thirdOrderNo));
    }

}
