package cn.hydee.middle.business.order.util.local.page;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * 排序工具
 * 
 * <AUTHOR> 2020年2月5日 下午3:34:45 
 * @version V1.0
 * @param <T>
 */
public class ListSortTool<T> {
    private final static String PERCENT = "%";
    private final static String CROSS_LINE = "--";
    private final static String MAX_NEGATIVE_PERCENT = "-100000000%";

    public List<T> sort(List<T> list, final String  sortField, final Boolean Ascending){
 
        Collections.sort(list, new Comparator<T>() {
            @Override
            public int compare(T o1, T o2) {
                int retVal=0;
                // 首字母转大写
                String newStr=sortField.substring(0, 1).toUpperCase()+sortField.replaceFirst("\\w","");
                String methodStr="get"+newStr;
                try {
                    Method method1 = ((T) o1).getClass().getMethod(methodStr, null);
                    Method method2 = ((T) o2).getClass().getMethod(methodStr, null);
                    // 获取返回值类型
                    Type t1 = method1.getAnnotatedReturnType().getType();
                    Type t2 = method2.getAnnotatedReturnType().getType();
                    // 返回值为数字和字符串使用不同的比较方法
                    if(t1.equals(Integer.class) && t2.equals(Integer.class)){
                        retVal = Integer.parseInt(method2.invoke(((T) o2), null).toString())-Integer.parseInt(method1.invoke(((T) o1), null).toString());
                    } else if(t1.equals(BigDecimal.class) && t2.equals(BigDecimal.class)){
                        BigDecimal value2 = new BigDecimal(method2.invoke(((T) o2), null).toString());
                        BigDecimal value1 = new BigDecimal(method1.invoke(((T) o1), null).toString());
                        retVal = value2.compareTo(value1);
                    } else{
                        String str1 = method1.invoke(((T) o1), null).toString();
                        String str2 = method2.invoke(((T) o2), null).toString();
                        if(CROSS_LINE.equalsIgnoreCase(str1)){
                            str1 = MAX_NEGATIVE_PERCENT;
                        }
                        if(CROSS_LINE.equalsIgnoreCase(str2)){
                            str2 = MAX_NEGATIVE_PERCENT;
                        }
                        if(str1.contains(PERCENT) && str2.contains(PERCENT)){
                            retVal = convertPercent(str2) > convertPercent(str1) ? 1 : -1;
                        }else {
                            retVal = str2.compareTo(str1);
                        }
                    }
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                } catch (InvocationTargetException e) {
                    e.printStackTrace();
                } catch (NoSuchMethodException e) {
                    e.printStackTrace();
                }
                if (Ascending) {
                    return 0-retVal;
                } else {
                    return retVal;
                }
            }
        });
        return list;
    }
 
 
    /**
     * 查看一个字符串是否可以转换为数字
     * @param str 字符串
     * @return true 可以; false 不可以
     *
     */
    public  boolean isStr2Num(String str) {
        try {
            Integer.parseInt(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    private double convertPercent(String percent) {
        String percentStr = Optional.ofNullable(percent).orElse("0.00%");
        String[] percentArr = percentStr.split("%");
        if(1 != percentArr.length) {
            return 0.0d;
        }
        String percentOneIndex = Optional.ofNullable(percentArr[0]).orElse("0.00");
        percentOneIndex = percentOneIndex.replace(",", "");
        return Double.valueOf(percentOneIndex);
    }
 
}
