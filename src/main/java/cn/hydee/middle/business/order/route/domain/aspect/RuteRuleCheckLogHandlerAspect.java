package cn.hydee.middle.business.order.route.domain.aspect;

import cn.hydee.middle.business.order.route.domain.model.RuleCheckContext;
import cn.hydee.middle.business.order.route.domain.model.StoreValueObject;
import cn.hydee.middle.business.order.yxtadapter.annotation.KCLogRecord;
import cn.hydee.middle.business.order.yxtadapter.domain.mongo.KCReqLogRecord;
import com.alibaba.fastjson.JSONObject;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/03/29 18:02
 **/
@Slf4j
@Aspect
@Configuration
public class RuteRuleCheckLogHandlerAspect {


    @Pointcut("@annotation(cn.hydee.middle.business.order.route.domain.annotation.RouteCheckLog)")
    public void checkLogHandler() {
    }

    @After("checkLogHandler()")
    public void recordKCReqAndResParam(JoinPoint joinPoint) {
        RuleCheckContext ruleCheckContext =  (RuleCheckContext)joinPoint.getArgs()[0];
        log.info(" 平台订单 {} 符合的发货门店集合 {}",ruleCheckContext.getOrderInfoAllDomainCheckValueObject().getThirdOrderNo(),JSONObject.toJSONString(ruleCheckContext.getAccordStoreValues().stream().map(
            StoreValueObject::getReStoreCode).collect(Collectors.toList())));
    }

    @Before("checkLogHandler()")
    public void recordKCReqParam(JoinPoint joinPoint) {
        RuleCheckContext ruleCheckContext =  (RuleCheckContext)joinPoint.getArgs()[0];
        log.info("规则校验请求参数 {}",JSONObject.toJSONString(ruleCheckContext));
    }
}
