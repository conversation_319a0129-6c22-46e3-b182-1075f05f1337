package cn.hydee.middle.business.order.storeautosync.controller;

import cn.hydee.middle.business.order.storeautosync.facade.storeauth.entity.StoreAuthRecordRequest;
import cn.hydee.middle.business.order.storeautosync.facade.storeauth.entity.StoreAuthRecordResponse;
import cn.hydee.middle.business.order.storeautosync.facade.storeauth.entity.StoreAuthRequest;
import cn.hydee.middle.business.order.storeautosync.facade.storeauth.service.StoreAutoAuthService;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/${api.version}/ds/store/auto")
@Api(tags = "网店自动授权")
public class StoreAutoAuthController extends AbstractController {

    @Autowired
    private StoreAutoAuthService storeAutoAuthService;

    @PostMapping("/auth/by_excel")
    @ApiOperation("网店自动授权、解绑、更换三方网店id入库")
    public ResponseBase<Void> storeAuth(
            @RequestParam MultipartFile file,
            @RequestParam(required = false, defaultValue = "27") @ApiParam("平台编码，目前只有美团：27") String platformCode,
            @RequestParam(required = false, defaultValue = "O2O") @ApiParam("服务模式，目前只有O2O") String serviceMode,
            @RequestHeader("userName") String userName
    ) {
        storeAutoAuthService.storeAuth(file, platformCode, serviceMode, userName);
        return ResponseBase.success();
    }

    @PostMapping("/auth/process")
    @ApiOperation("xxl-job调用，网店自动授权、解绑、更换三方网店id处理")
    public ResponseBase<Void> storeAuthProcess(
            @RequestBody String request
    ) {
        storeAutoAuthService.storeAuthProcess(JSON.parseObject(request, StoreAuthRequest.class));
        return ResponseBase.success();
    }

    @PostMapping("/auth/record")
    @ApiOperation("网店自动授权记录分页列表")
    public ResponseBase<PageDTO<StoreAuthRecordResponse>> storeAuthRecordPage(
            @RequestBody StoreAuthRecordRequest request
    ) {
        return generateSuccess(storeAutoAuthService.getStoreAuthRecordPage(request));
    }
}
