package cn.hydee.middle.business.order.account.check.base.dto.req;

import cn.hydee.starter.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/3/25
 * @since 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AccountCheckPageReqDto extends PageBase {

    @ApiModelProperty(value = "开始账单日期")
    private String startAccountTime;

    @ApiModelProperty(value = "结束账单日期")
    private String endAccountTime;

    @ApiModelProperty(value = "三方平台编码")
    private String thirdPlatformCode;

    @ApiModelProperty(value = "商户编码")
    private String merCode;

    @ApiModelProperty(value = "机构编码")
    private String organizationCode;

    @ApiModelProperty(value = "平台订单号")
    private String thirdOrderNo;
}
