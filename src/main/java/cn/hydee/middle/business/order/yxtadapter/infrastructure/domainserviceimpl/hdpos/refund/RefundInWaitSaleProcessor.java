package cn.hydee.middle.business.order.yxtadapter.infrastructure.domainserviceimpl.hdpos.refund;

import cn.hydee.middle.business.order.Enums.ErpStateEnum;
import cn.hydee.middle.business.order.domain.OrderDetailDomain;
import cn.hydee.middle.business.order.entity.*;
import cn.hydee.middle.business.order.mapper.OrderDetailMapper;
import cn.hydee.middle.business.order.service.suport.RefundBasicService;
import cn.hydee.middle.business.order.yxtadapter.domainservice.hdpos.RefundProcessor;
import cn.hydee.middle.business.order.yxtadapter.domainservice.refund.RefundHandleService;
import cn.hydee.middle.business.order.yxtadapter.infrastructure.domainserviceimpl.hdpos.context.HdPosBaseInfoContext;
import cn.hydee.middle.business.order.yxtadapter.infrastructure.domainserviceimpl.hdpos.context.RefundProcessorFactory;
import cn.hydee.starter.util.BeanUtil;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * 订单下账状态已拣货[待下账]退款下账处理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RefundInWaitSaleProcessor implements RefundProcessor {

    private final RefundBasicService refundBasicService;
    private final RefundHandleService refundHandleService;

    @SneakyThrows
    @Override
    public boolean handleRefund(HdPosBaseInfoContext context, Integer businessFlag) {
        OrderInfo orderInfo = context.getOrderInfo();
        RefundOrder refundOrder = context.getRefundOrder();
        List<RefundDetail> refundDetailList = context.getRefundDetailList();
        List<OrderDetail> orderDetailList = context.getOrderDetailList();
        List<OrderPickInfo> orderPickInfoList = context.getOrderPickInfoList();
        refundHandleService.refundAfterPick(orderInfo,orderDetailList,refundOrder,refundDetailList,orderPickInfoList);
        return true;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        RefundProcessorFactory.register(ErpStateEnum.WAIT_SALE.getCode(),this);
    }
}
