package cn.hydee.middle.business.order.Enums;

/**
 * 线上门店营业状态
 * <AUTHOR>
 * @date 2020/9/14
 */
public enum StoreOpenStatusEnum {

    // 营业状态（0：歇业 1：营业中 2：休息）
    DEFAULT("-1", "默认状态"),
    CLOSED("0", "歇业"),
    IN_OPERATION("1", "营业"),
    REST("2", "休息");

    private String code;
    private String msg;

    StoreOpenStatusEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static String getMsgByCode(String code){
        for(StoreOpenStatusEnum statusEnum : values()){
            if(statusEnum.getCode().equals(code)){
                return statusEnum.getMsg();
            }
        }
        return "";
    }
}
