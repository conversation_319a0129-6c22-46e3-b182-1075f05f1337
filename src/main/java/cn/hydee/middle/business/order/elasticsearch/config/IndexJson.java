package cn.hydee.middle.business.order.elasticsearch.config;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.Charset;

@Component
@Slf4j
@Getter
public class IndexJson {

    private String erporderes;

    private String orderinfoes;

    @Value("classpath:index/erporderes.json")
    public void setErporderes(Resource erporderes) {
        try {
            this.erporderes = IOUtils.toString(erporderes.getInputStream(), String.valueOf(Charset.forName("UTF-8")));
        } catch (IOException e) {
            log.error("读取索引json文件失败", e);
        }
    }

    /**
     * 线上订单创index的mappings
     * @param orderinfoes
     */
    @Value("classpath:index/orderinfoes.json")
    public void setOrderInfoes(Resource orderinfoes) {
        try {
            this.orderinfoes = IOUtils.toString(orderinfoes.getInputStream(), String.valueOf(Charset.forName("UTF-8")));
        } catch (IOException e) {
            log.error("读取索引json文件失败", e);
        }
    }

}
