package cn.hydee.middle.business.order.route.application.representation;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
@ApiModel(description = "策略信息")
public class StrategyRepresentationDto {

  @JsonSerialize(using = ToStringSerializer.class)
  @ApiModelProperty(value = "策略Id")
  private Long id;

  @ApiModelProperty(value = "策略名称")
  private String strategyName;

  @ApiModelProperty(value = "场景Id")
  private Long sceneId;

  @ApiModelProperty(value = "场景名称")
  private String sceneName;

  @ApiModelProperty(value = "门店编码")
  private String storeCode;

  @ApiModelProperty(value = "门店名称")
  private String storeName;

  @ApiModelProperty(value = "店铺id字符串")
  private String onlineStoreIds;

  @ApiModelProperty(value = "店铺名称集合")
  private List<String> onlineStoreNames;

  @ApiModelProperty(value = "创建人id")
  private String createdBy;

  @ApiModelProperty(value = "创建人")
  private String createdName;

  @ApiModelProperty(value = "创建时间")
  private Date createdTime;

  @ApiModelProperty(value = "状态")
  private Boolean state;

  @ApiModelProperty(value = "版本号")
  private Long version;

  /**
   * 更新时间
   */
  private Date updatedTime;
}
