package cn.hydee.middle.business.order.service.impl;

import cn.hydee.middle.business.order.entity.AccountCheckChannelTemp;
import cn.hydee.middle.business.order.mapper.AccountCheckChannelTempMapper;
import cn.hydee.middle.business.order.service.AccountCheckChannelTempService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 渠道数据清洗表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-06
 */
@Service
@Slf4j
public class AccountCheckChannelTempServiceImpl extends ServiceImpl<AccountCheckChannelTempMapper, AccountCheckChannelTemp> implements AccountCheckChannelTempService {

    @Override
    public void saveUpdateBatch(List<AccountCheckChannelTemp> dataList){
        try {
            List<String> queryList = dataList.stream().map(AccountCheckChannelTemp::getThirdOrderNo).collect(Collectors.toList());
            queryList = queryList.stream().distinct().collect(Collectors.toList());
            QueryWrapper<AccountCheckChannelTemp> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(AccountCheckChannelTemp::getThirdOrderNo, queryList);
            List<AccountCheckChannelTemp> existDataList = baseMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(existDataList)) {
                existDataList = Collections.emptyList();
            }
            Map<String, AccountCheckChannelTemp> dataMap = existDataList.stream().collect(Collectors.toMap(AccountCheckChannelTemp::getUnionKeyStr, a -> a, (v1, v2) -> v1));
            List<AccountCheckChannelTemp> updateDataList = dataList.stream().filter(data -> dataMap.containsKey(data.getUnionKeyStr())).collect(Collectors.toList());
            List<AccountCheckChannelTemp> insertDataList = dataList.stream().filter(data -> !dataMap.containsKey(data.getUnionKeyStr())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(updateDataList)) {
                updateDataList.forEach(data -> {
                    data.setId(dataMap.get(data.getUnionKeyStr()).getId());
                    data.setModifyTime(new Date());
                });
                updateBatchById(updateDataList);
            }
            if (!CollectionUtils.isEmpty(insertDataList)) {
                insertDataList.forEach(data -> {
                    data.setCreateTime(new Date());
                });
                saveBatch(insertDataList);
            }
        }catch (Exception e) {
            log.info("AccountCheckChannelTempServiceImpl Batch error,dataList:{},cause:{}",dataList,e);
        }
    }
}
