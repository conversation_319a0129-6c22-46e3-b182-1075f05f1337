package cn.hydee.middle.business.order.dto;

import cn.hydee.middle.business.order.Enums.DsConstants;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/12/07
 */
@Data
public class CheckAppointCallRiderRspDto {

    @ApiModelProperty(value = "系统订单号")
    private Long orderNo;

    @ApiModelProperty(value = "0-符合预约单呼叫逻辑 1- 不符合")
    private int appointCallRiderFlag;

    @ApiModelProperty(value = "预约时间描述")
    private String appointTimeDesc;

    @ApiModelProperty(value = "预计呼叫时间描述")
    private String callTimeDesc;

    public static CheckAppointCallRiderRspDto buildNoData(Long orderNo){
        CheckAppointCallRiderRspDto dto = new CheckAppointCallRiderRspDto();
        dto.setOrderNo(orderNo);
        dto.setAppointCallRiderFlag(DsConstants.INTEGER_ONE);
        return dto;
    }

    public static CheckAppointCallRiderRspDto buildYesData(Long orderNo,String appointTimeDesc,String callTimeDesc){
        CheckAppointCallRiderRspDto dto = new CheckAppointCallRiderRspDto();
        dto.setOrderNo(orderNo);
        dto.setAppointCallRiderFlag(DsConstants.INTEGER_ZERO);
        dto.setAppointTimeDesc(appointTimeDesc);
        dto.setCallTimeDesc(callTimeDesc);
        return dto;
    }

}
