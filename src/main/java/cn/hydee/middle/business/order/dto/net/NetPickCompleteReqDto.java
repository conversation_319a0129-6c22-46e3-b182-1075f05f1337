package cn.hydee.middle.business.order.dto.net;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/3/7 11:54
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
public class NetPickCompleteReqDto {
    private String olorderno;
    /** 配送类型 */
    private String deliverytype;

    /** 平台门店ID */
    private String olshop_id;

    public NetPickCompleteReqDto(String orderNo, String deliveryType,String olShopId) {
        this.olorderno = orderNo;
        this.deliverytype = deliveryType;
        this.olshop_id = olShopId;
    }
}
