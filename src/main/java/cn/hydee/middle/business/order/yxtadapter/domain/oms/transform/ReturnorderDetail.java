package cn.hydee.middle.business.order.yxtadapter.domain.oms.transform;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * returnorder_detail
 */
@Data
public class ReturnorderDetail implements Serializable {
    private String guid;

    /**
     * 手动插入退单时的当前时间
     */
    private String orderguid;

    /**
     * 退单编号
     */
    private String returncode;

    /**
     * 商品guid
     */
    private String goodsguid;

    /**
     * 商品编码
     */
    private String goodscode;

    /**
     * 商品标识（code|价格）
     */
    private String goodsflag;

    /**
     * 商品名
     */
    private String goodsname;

    /**
     * 退货数量
     */
    private Integer returncount;

    /**
     * 商品价格
     */
    private BigDecimal goodsprice;

    /**
     * 退款商品金额
     */
    private BigDecimal goodsamount;

    /**
     * 是否是赠品
     */
    private Integer isgift;

    /**
     * 买家备注
     */
    private String buyernotes;

    /**
     * 卖家备注
     */
    private String sellernotes;

    /**
     * 折后价（实际价格）
     */
    private BigDecimal discountprice;

    /**
     * 是否部分退（0：否，1：是）
     */
    private Integer ispart;

    /**
     * 均摊价格（未使用）
     */
    private BigDecimal sharingprice;

    /**
     * 积分操作（无用）
     */
    private String addintegral;

    /**
     * 商品退款状态
     */
    private String goodsstatus;

    /**
     * 规格1
     */
    private String spec1;

    /**
     * 规格2
     */
    private String spec2;

    /**
     * 规格3
     */
    private String spec3;

    /**
     * 关联显示的skuid
     */
    private String skuid;

    private String batchnumber;

    private Integer innerstatus;

    private String reservefield0;

    private String reservefield1;

    private String reservefield2;

    private String reservefield3;

    private String reservefield4;

    private String reservefield5;

    private static final long serialVersionUID = 1L;
}