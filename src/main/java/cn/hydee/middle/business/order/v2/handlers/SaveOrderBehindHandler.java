package cn.hydee.middle.business.order.v2.handlers;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hydee.middle.business.order.Enums.*;
import cn.hydee.middle.business.order.Enums.oborder.ObOrderTypeEnum;
import cn.hydee.middle.business.order.configuration.MerCodesConfig;
import cn.hydee.middle.business.order.domain.OrderInfoAllDomain;
import cn.hydee.middle.business.order.dto.commodity.CommodityStockInfoDto;
import cn.hydee.middle.business.order.dto.req.AddOrderDetailReqDto;
import cn.hydee.middle.business.order.dto.req.AddOrderInfoReqDto;
import cn.hydee.middle.business.order.dto.rsp.OnlineStoreInfoRspDto;
import cn.hydee.middle.business.order.entity.*;
import cn.hydee.middle.business.order.feign.MiddleMerchandiseClient;
import cn.hydee.middle.business.order.mapper.OrderInfoMapper;
import cn.hydee.middle.business.order.mapper.OrderLockInfoMapper;
import cn.hydee.middle.business.order.mapper.OrderPickInfoMapper;
import cn.hydee.middle.business.order.service.OrderPrescriptionService;
import cn.hydee.middle.business.order.service.async.HydeeEsSyncClientAsync;
import cn.hydee.middle.business.order.service.rabbit.delayConsumer.base.BaseDelayMqMsg;
import cn.hydee.middle.business.order.service.rabbit.delayConsumer.base.DelayProducer;
import cn.hydee.middle.business.order.service.rabbit.delayConsumer.base.MsgTypeServiceEnum;
import cn.hydee.middle.business.order.service.rocket.MessageProducerService;
import cn.hydee.middle.business.order.util.NotifyRedisHelper;
import cn.hydee.middle.business.order.v2.feign.hems.HemsCommonClient;
import cn.hydee.middle.business.order.v2.manager.OrderBusinessToConsumerHandlerManager;
import cn.hydee.middle.business.order.v2.manager.OrderSaveHandlerManager;
import cn.hydee.middle.business.order.v2.manager.base.OrderHandlerManager;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.unified.model.HemsBaseData;
import cn.hydee.unified.model.order.OrderConformReq;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Scope;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description: 保存订单处理器
 * @Author: chufeng
 * @since: 2020/8/18 11:42
 **/
@Slf4j
@Component
@Scope(name = "prototype", description = "prototype")
public class SaveOrderBehindHandler extends AbstractHandler {

    @Autowired
    private OrderHandlerManager orderHandlerManager;
    @Autowired
    private MessageProducerService messageProducerService;
    @Autowired
    private HemsCommonClient hemsCommonClient;
    @Autowired
    private OrderInfoMapper orderInfoMapper;
    @Autowired
    private OrderSaveHandlerManager orderSaveHandlerManager;
    @Autowired
    private DelayProducer delayProducer;
    @Autowired
    private OrderLockInfoMapper orderLockInfoMapper;
    @Autowired
    private MerCodesConfig merCodesConfig;
    @Autowired
    private MiddleMerchandiseClient middleMerchandiseClient;
    @Value("${prescriptionPlatList}")
    private String prescriptionPlatList;
    @Autowired
    private OrderPrescriptionService orderPrescriptionService;
    // 是否推送到药师云
    @Value("${approvePrescriptionPlatform:11,24}")
    private String approvePrescriptionPlatform;

    @Value("${print-sound-delay:true}")
    private boolean printSoundDelay;

    @Autowired
    private OrderPickInfoMapper orderPickInfoMapper;

    @Value("${vip.pickerId:1072801}")
    private String pickerId;

    /**
     * @param request
     * @return
     */
    @SuppressWarnings("unchecked")
    @Override
    public Object handlerRequest(Object request) {
        // 拿参数
        Map<String, Object> params = (Map<String, Object>) request;
        AddOrderInfoReqDto addOrderInfoReqDto = (AddOrderInfoReqDto) params.get(
            "addOrderInfoReqDto");
        OnlineStoreInfoRspDto onlineStoreInfoRspDto = (OnlineStoreInfoRspDto) params.get(
            "onlineStoreInfoRspDto");
        OrderInfoAllDomain orderInfoAllDomain = (OrderInfoAllDomain) params.get(
            "orderInfoAllDomain");
        // VIP 订单不走打印小票和声音通知
        if (DsConstants.VIP_ORDER.equalsIgnoreCase(addOrderInfoReqDto.getOrdertype())) {
            // 修改订单状态
            OrderInfo upOrder = new OrderInfo();
            upOrder.setOrderNo(orderInfoAllDomain.getOrderNo());
            upOrder.setOrderState(OrderStateEnum.COMPLETED.getCode());
            upOrder.setErpState(ErpStateEnum.WAIT_SALE.getCode());
            upOrder.setPickerId(pickerId);
            upOrder.setPickOperatorId(pickerId);
            upOrder.setCompleteTime(new Date());
            upOrder.setLockFlag(0);
            upOrder.setLockMsg("");
            orderInfoMapper.updateOrder(upOrder);
            // 新增拣货信息
            List<OrderDetail> orderDetailList = orderInfoAllDomain.getOrderDetailList();
            List<OrderPickInfo> orderPickInfos = new ArrayList<>(orderDetailList.size());
            orderDetailList.forEach(a -> orderPickInfos.add(getOrderPickInfo(a)));
            orderPickInfoMapper.insertBatch(orderPickInfos);
            // 更新配送方式
            return handlerNext(params);
        }
        Long orderNo = (Long) params.get("orderNo");
        //京东健康特殊业务场景 如果是处方订单，接单即处方审核通过，不需要再单独调用处方审核接口了
        if (PlatformCodeEnum.JD_HEALTH.getCode().equals(addOrderInfoReqDto.getEctype())) {
            OrderInfo upOrder = new OrderInfo();
            upOrder.setOrderNo(orderInfoAllDomain.getOrderNo());
            upOrder.setPrescriptionStatus(DsConstants.INTEGER_ONE);
            orderInfoMapper.updateOrder(upOrder);
        } else {
            log.info("SaveOrderBehindHandler handlerRequest params:{}",
                JSONObject.toJSONString(orderInfoAllDomain.getOrderPrescriptionList()));
            //  目前只判断 是否是处方单就可以了 美团平台暂时推送消息
            if (!ObjectUtil.isEmpty(orderInfoAllDomain.getOrderPrescriptionList())
                && approvePrescriptionPlatform.contains(
                orderInfoAllDomain.getThirdPlatformCode())) {
                //推送三方审方
                boolean res = orderPrescriptionService.pushPrescriptionCheck(orderInfoAllDomain);
                if (res) {
                    //更新order推送
                    OrderInfo upOrder = new OrderInfo();
                    upOrder.setOrderNo(orderInfoAllDomain.getOrderNo());
                    upOrder.setIsPushCheck(DsConstants.INTEGER_ONE);
                    orderInfoMapper.updateOrder(upOrder);
                }
            }
        }

        List<OrderDetail> orderDetailList = orderInfoAllDomain.getOrderDetailList();
        // 按照配置进行处理 如果配置了创建新订单自动锁库存 则进行扣减库存操作 1:创建新单自动锁库存  0：不锁库存
        // 预约单不锁库存; 转仓到 HEMS的不锁库存
        boolean isCheckErp = false;
        if (!DsConstants.APPOINTMENT.equalsIgnoreCase(addOrderInfoReqDto.getOrdertype())) {
            if (DsConstants.INTEGER_ONE.equals(onlineStoreInfoRspDto.getWhetherInventoryLocked())) {
                //返回是否扣商品库存成功  扣失败的不通知
                isCheckErp = orderHandlerManager.commodityStockDeductV2(orderInfoAllDomain,
                    orderDetailList);
            }
        }

        // 创建新订单成功,审方通过后才打印小票和声音通知，目前除京东到家和饿了么除外其他不需要走审方流程。
        if (isNeedSendPrintMessage(orderInfoAllDomain, onlineStoreInfoRspDto)) {
            // 保存打印信息
            List<OrderLockInfo> lockList = orderLockInfoMapper.selectByOrderNo(
                orderInfoAllDomain.getOrderNo());
            orderSaveHandlerManager.producePrintMessageContent(orderInfoAllDomain, lockList,
                onlineStoreInfoRspDto, DsConstants.INTEGER_ONE);
        }
        // 需要审方的订单 延时发送来单语音消息
        if(isNeedSendSoundMessage(orderInfoAllDomain)){
            // client新订单声音通知，websocket
            messageProducerService.produceSoundBroadCastMessage(orderInfoAllDomain,
                getSoundTypeOnSaveOrder(orderInfoAllDomain));
        }

        // net新订单通知，呼叫骑手
        // 流程优化：方法命名上没有呼叫骑手，理论上应该接单后才会呼叫骑手
        messageProducerService.produceNewOrderMessage(orderInfoAllDomain.getOrderNo());
        if (OrderThirdStateEnum.WAIT_TAKE.getCode().equals(addOrderInfoReqDto.getOmsstatus())
            && !DsConstants.INTEGER_ONE.equals(onlineStoreInfoRspDto.getAutoAcceptFlag())) {
            orderInfoMapper.updateAcceptTimeNull(orderNo);
            orderInfoAllDomain.setAcceptTime(null);
        }

        // 1015205 拣货、发货提醒与时效管控
        NotifyRedisHelper.addPickNotify(orderInfoAllDomain, onlineStoreInfoRspDto);

        // 判断订单状态是否为待接单且设置为自动接单，如果是待接单，则自动调用接单接口接单
        if (OrderThirdStateEnum.WAIT_TAKE.getCode().equals(addOrderInfoReqDto.getOmsstatus())
            && DsConstants.INTEGER_ONE.equals(onlineStoreInfoRspDto.getAutoAcceptFlag())) {
            //4.8.1 非 （处方单 & 配置了当前平台）  不自动接单
            if (!(DsConstants.INTEGER_ONE.equals(orderInfoAllDomain.getPrescriptionFlag())
                && isContainsPlatform(orderInfoAllDomain.getThirdPlatformCode()))) {
                HemsBaseData baseData = hemsCommonClient.constructHemsBaseData(
                    addOrderInfoReqDto.getGroupid(), addOrderInfoReqDto.getEctype(),
                    addOrderInfoReqDto.getClientid(), onlineStoreInfoRspDto.getSessionKey());
                OrderConformReq orderConfirmReq = new OrderConformReq();
                orderConfirmReq.setConfirmType(DsConstants.STRING_THREE);
                orderConfirmReq.setDeliveryId("");
                orderConfirmReq.setOlOrderNo(addOrderInfoReqDto.getOlorderno());
                orderConfirmReq.setOlShopId(addOrderInfoReqDto.getO2o_shop_id());
                orderConfirmReq.setShopId(onlineStoreInfoRspDto.getOutShopId());
                //操作人 【京东健康必填】
                orderConfirmReq.setOperateMan("system");
                try {
                    hemsCommonClient.orderConfirm(orderConfirmReq, baseData);
                    OrderInfo updateOrder = new OrderInfo();
                    updateOrder.setOrderNo(orderNo);
                    updateOrder.setOrderState(OrderStateEnum.UN_PICK.getCode());
                    orderInfoMapper.updateOrder(updateOrder);
                    HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE,
                        orderInfoAllDomain.getMerCode(), orderNo,
                        orderInfoAllDomain.getOrderState(), orderInfoAllDomain.getErpState(),
                        OrderLogEnum.RECEIVE_ORDER.getAction(),
                        OrderLogEnum.getReceveOrderInfo(orderInfoAllDomain.getOrderNo(),
                            orderInfoAllDomain.getOrderState()), null);
                } catch (Exception e) {
                    log.error(
                        "SaveOrderBehindHandler:saveOrder-订单确认异常 - addOrderInfoReqDto:{}",
                        addOrderInfoReqDto);
                }
            }
        }

        try {
            //3分钟后通知自动拣货  硬编码万和521220   美团饿百  会进入队列
            if (merCodesConfig.needPick2platformFlag(orderInfoAllDomain.getMerCode())) {
                //美团平台订单且配送方式为美团平台配送，或饿百平台订单
                OrderDeliveryRecord orderDeliveryRecord = orderInfoAllDomain.getOrderDeliveryRecord();
                if (PlatformCodeEnum.E_BAI.getCode()
                    .equals(orderInfoAllDomain.getThirdPlatformCode()) ||
                    (PlatformCodeEnum.MEITUAN.getCode()
                        .equals(orderInfoAllDomain.getThirdPlatformCode())
                        && DeliveryTypeEnum.checkPlatformRider(
                        orderDeliveryRecord.getDeliveryType()))) {
                    JSONObject mqParam = new JSONObject();
                    mqParam.put("orderNo", orderInfoAllDomain.getOrderNo());
                    BaseDelayMqMsg message = new BaseDelayMqMsg(
                        MsgTypeServiceEnum.ORDER_PICK.getType(), mqParam);
                    delayProducer.sendMessage2RedayFirst(message);
                }
            }
        } catch (Exception e) {
            log.error("pick2platform error,orderNo:{},e:{}", orderInfoAllDomain.getOrderNo(), e);
        }
        //重新获取订单信息， 商品不存在 & 库存不足 ， 则通知商品中台   校验了erp库存的才返回这个
        if (isCheckErp) {
            notifyComm(addOrderInfoReqDto, orderNo);
        }

        if (DsConstants.INTEGER_ONE.equals(onlineStoreInfoRspDto.getAutoToHems())) {
            orderHandlerManager.pushToHEMS(addOrderInfoReqDto);
        }

        // 供应商自动转单
        OrderBusinessToConsumerHandlerManager.srmToConsumerForNewOrder(orderInfoAllDomain,
            addOrderInfoReqDto);

        return handlerNext(params);
    }


    /**
     * 是否发送打印来单小票消息
     *
     * @param orderInfo 订单信息
     * @return 是否需要及时打印和推送声音信息
     */
    private boolean isNeedSendPrintMessage(OrderInfoAllDomain orderInfo,
        OnlineStoreInfoRspDto onlineStoreInfoRspDto) {
        // 配置的需要二次审方的平台 如果是处方单待审核状态就延迟打印小票
        if (DsConstants.INTEGER_ONE.equals(onlineStoreInfoRspDto.getAutoPrintReceipt())
            && CollUtil.isNotEmpty(orderInfo.getOrderPrescriptionList())
            && printSoundDelay
            && approvePrescriptionPlatform.contains(orderInfo.getThirdPlatformCode())
            && orderInfo.getOrderState().equals(OrderStateEnum.UN_CHECK.getCode())) {
            return Boolean.FALSE;
        }
        return true;
    }

    /**
     * 是否发送声音来单消息
     * @param orderInfo 订单消息
     * @return false 不发送  true 发送
     */
    private boolean isNeedSendSoundMessage(OrderInfoAllDomain orderInfo ) {
        // 如果是 需要药师云审方的处方单并且配置了延迟发送 则不能在新建订单时发送新订单语音消息
        SoundTypeEnum soundTypeEnum = getSoundTypeOnSaveOrder(orderInfo);
        if(soundTypeEnum!= SoundTypeEnum.NEW_ORDER){
            // 如果不是新订单的语音播报，还是需要发送声音消息
            return Boolean.TRUE;
        }
        if (printSoundDelay
            && CollUtil.isNotEmpty(orderInfo.getOrderPrescriptionList())
            && approvePrescriptionPlatform.contains(orderInfo.getThirdPlatformCode())
            && orderInfo.getOrderState().equals(OrderStateEnum.UN_CHECK.getCode())) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }



    @NotNull
    private static OrderPickInfo getOrderPickInfo(OrderDetail orderDetail) {
        OrderPickInfo orderPickInfo = new OrderPickInfo();
        orderPickInfo.setOrderDetailId(orderDetail.getId());
        orderPickInfo.setErpCode(orderDetail.getErpCode());
        orderPickInfo.setCommodityBatchNo("NONE");
        orderPickInfo.setCount(1);
        orderPickInfo.setPurchasePrice(orderDetail.getPrice());
        orderPickInfo.setCreateTime(new Date());
        orderPickInfo.setModifyTime(new Date());
        orderPickInfo.setIsValid(1);
        return orderPickInfo;
    }

    //通知商户中台  如果商品中台返回库存不足，不会走到此流程
    private void notifyComm(AddOrderInfoReqDto addOrderInfoReqDto,Long orderNo){
        try{
            OrderInfo orderInfo = orderInfoMapper.selectOrderInfo(orderNo);
            if(OrderLockFlagEnum.LOCK_LACK_STOCK.getCode().equals(orderInfo.getLockFlag())
                || OrderLockFlagEnum.LOCK_COMMODITY_NOT_EXIST.getCode().equals(orderInfo.getLockFlag())
                    || OrderLockFlagEnum.LOCK_COMMODITY_NOT_SALL.getCode().equals(orderInfo.getLockFlag())
                    //商品不存在异常分类
                    || OrderLockFlagEnum.LOCK_ERP_CODE_NULL.getCode().equals(orderInfo.getLockFlag())
                    || OrderLockFlagEnum.LOCK_THIRD_COMMODITY_UNMATCH.getCode().equals(orderInfo.getLockFlag())
                    || OrderLockFlagEnum.LOCK_OFFLINE_COMMODITY_NOT_EXSIST.getCode().equals(orderInfo.getLockFlag())){
                //erp无法判断具体某个商品，传全量商品给商品中台
                List<AddOrderDetailReqDto> orderdetaillist = addOrderInfoReqDto.getOrderdetaillist();
                if(!CollectionUtils.isEmpty(orderdetaillist)){
                    String[] erpCodes = new String[orderdetaillist.size()];
                    for(int i = 0;i< orderdetaillist.size();i++){
                        erpCodes[i] = orderdetaillist.get(i).getOuter_iid();
                    }
                    ResponseBase<PageDTO<CommodityStockInfoDto>> result =  middleMerchandiseClient.notifyStockEx(erpCodes,orderInfo.getMerCode(),orderInfo.getOrganizationCode());
                    log.info("saveOrderBehindnotifyComm orderNo:{},erpCodes:{},result:{}",orderNo,String.valueOf(erpCodes),JSONObject.toJSONString(result));
                }
            }
        }catch (Exception e){
            log.error("saveOrderBehindnotifyComm error: orderNo:{},",orderNo,e);
        }
    }

    public boolean isContainsPlatform(String thirdPlatformCode) {
        //配置空  则默认全部
        if(StringUtils.isEmpty(prescriptionPlatList)){
            return true;
        }
        String[] codeList = prescriptionPlatList.split(",");
        for(int i=0; i<codeList.length; i++){
            if(codeList[i].equals(thirdPlatformCode)){
                return true;
            }
        }
        return false;
    }

    public SoundTypeEnum getSoundTypeOnSaveOrder(OrderInfo orderInfo) {
        SoundTypeEnum soundTypeEnum = SoundTypeEnum.NEW_ORDER;

        // 待审方订单
        if (DsConstants.INTEGER_ONE.equals(orderInfo.getIsPrescription()) && DsConstants.INTEGER_ZERO.equals(orderInfo.getPrescriptionStatus())) {
            // 设置订单语音消息播报类型为："待审方订单"
            soundTypeEnum = SoundTypeEnum.UN_CHECK_ORDER;
        }

        // 预约单
        if (ObOrderTypeEnum.APPOINTMENT_ORDER.getCode().equals(orderInfo.getAppointment())) {
            soundTypeEnum = SoundTypeEnum.APPOINTMENT_ORDER;
        }

        if (orderInfo.getLockFlag() > OrderLockFlagEnum.LOCK_EXCEPTION_MIN_CODE.getCode() &&
                orderInfo.getLockFlag() < OrderLockFlagEnum.LOCK_EXCEPTION_MAX_CODE.getCode()) {
            soundTypeEnum = SoundTypeEnum.EXCEPTION_ORDER;
        }

        return soundTypeEnum;
    }

}
