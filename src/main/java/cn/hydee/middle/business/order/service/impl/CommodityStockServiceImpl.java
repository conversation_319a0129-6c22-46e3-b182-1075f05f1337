package cn.hydee.middle.business.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hydee.middle.business.order.Enums.*;
import cn.hydee.middle.business.order.dto.req.CommodityStockReqDto;
import cn.hydee.middle.business.order.dto.req.StockDeductReqDto;
import cn.hydee.middle.business.order.dto.rsp.StockDeductRspDto;
import cn.hydee.middle.business.order.entity.CommodityExceptionOrder;
import cn.hydee.middle.business.order.entity.CommodityStock;
import cn.hydee.middle.business.order.entity.OrderDetail;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.entity.b2c.OmsOrderInfo;
import cn.hydee.middle.business.order.feign.MiddleIdClient;
import cn.hydee.middle.business.order.feign.MiddleMerchandiseClient;
import cn.hydee.middle.business.order.mapper.CommodityStockMapper;
import cn.hydee.middle.business.order.mapper.OrderInfoMapper;
import cn.hydee.middle.business.order.mapper.b2c.OmsOrderInfoMapper;
import cn.hydee.middle.business.order.service.CommodityExceptionOrderService;
import cn.hydee.middle.business.order.service.CommodityStockService;
import cn.hydee.middle.business.order.service.cache.BaseInfoCache;
import cn.hydee.middle.business.order.service.suport.OrderBasicService;
import cn.hydee.middle.business.order.util.ExceptionUtil;
import cn.hydee.middle.business.order.util.RedisKeyUtil;
import cn.hydee.middle.business.order.util.redis.RedisLockUtil;
import cn.hydee.middle.business.order.yxtadapter.domain.oms.transform.OrderStd;
import cn.hydee.middle.business.order.yxtadapter.infrastructure.domainserviceimpl.util.ThreadUtils;
import cn.hydee.middle.business.order.yxtadapter.util.DateUtil;
import cn.hydee.middle.business.order.yxtadapter.util.ReadWriteLock;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author: xin.tu
 * @date: 2023/1/6 10:10
 * @menu:
 */
@Service
@Slf4j
public class CommodityStockServiceImpl extends ServiceImpl<CommodityStockMapper, CommodityStock> implements CommodityStockService {


    @Resource
    private ReadWriteLock readWriteLock;

    @Autowired
    private BaseInfoCache baseInfoCache;

    @Resource
    private OrderBasicService orderBasicService;
    @Autowired
    private MiddleMerchandiseClient middleMerchandiseClient;

    @Autowired
    private CommodityExceptionOrderService commodityExceptionOrderService;

    @Autowired
    private MiddleIdClient middleIdClient;

    @Autowired
    private OmsOrderInfoMapper omsOrderInfoMapper;

    @Autowired
    private OrderInfoMapper orderInfoMapper;

    @Autowired
    private CommodityStockMapper commodityStockMapper;



    @Override
    public void lockStock(OrderInfo orderInfo, List<OrderDetail> orderDetailList, ResponseBase<StockDeductRspDto> resp) {
        if (orderInfo == null || CollUtil.isEmpty(orderDetailList)) {
            return;
        }
        if (resp == null || resp.getCode() == null || !resp.checkSuccess()) {
            return;
        }
        log.info("commodityLockStock param orderNo:{}", orderInfo.getOrderNo());
        List<String> exceptionErpCode = CollUtil.list(false);
        if (!DsConstants.INTEGER_ZERO.equals(resp.getData().getStatus())) {
            List<String> notFoundItem = resp.getData().getNotFoundItem();
            List<String> outOfStock = resp.getData().getOutOfStock();
            if (CollUtil.isNotEmpty(notFoundItem)) {
                exceptionErpCode.addAll(notFoundItem);
            }
            if (CollUtil.isNotEmpty(outOfStock)) {
                exceptionErpCode.addAll(outOfStock);
            }
        }
        orderDetailList = orderDetailList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(
                Comparator.comparing(item -> item.getId() + item.getOrderNo())
        )), ArrayList::new));

        //去除异常商品
        orderDetailList.removeIf(next -> exceptionErpCode.contains(next.getErpCode()));
        if (CollUtil.isEmpty(orderDetailList)) {
            return;
        }
        String lockKey = RedisKeyUtil.getCommodityStockLock(orderInfo.getOrderNo().toString());
        if (RedisLockUtil.getLockKey(lockKey, String.valueOf(System.currentTimeMillis()))) {
            try {
                //查询已锁库存
                List<CommodityStock> existList = getCommodityStockList(orderInfo, LocalStockEnum.ADD);
                Map<String, CommodityStock> existMap = existList.stream().collect(Collectors.toMap(it -> it.getOrderNo() + "_" + it.getOrderDetailId() + "_" + it.getOrganizationCode(), a -> a));

                List<CommodityStock> saveCommodityStocks = new ArrayList<>();

                for (OrderDetail orderDetail : orderDetailList) {
                    String key = orderDetail.getOrderNo() + "_" + orderDetail.getId() + "_" + orderInfo.getOrganizationCode();
                    if (existMap.containsKey(key)) {
                        // 已锁库存过滤
                        log.info("commodityStock exist this key,key:{}", key);
                        continue;
                    }
                    if (orderDetail.getPickCount() <= 0) {
                        continue;
                    }
                    CommodityStock commodityStock = new CommodityStock();
                    commodityStock.setMerCode(orderInfo.getMerCode());
                    commodityStock.setErpCode(orderDetail.getErpCode());
                    commodityStock.setOrderNo(orderDetail.getOrderNo().toString());
                    commodityStock.setOrderDetailId(orderDetail.getId());
                    commodityStock.setOnlineStoreCode(orderInfo.getOnlineStoreCode());
                    commodityStock.setOrganizationCode(orderInfo.getOrganizationCode());
                    commodityStock.setStockQty(orderDetail.getPickCount());
                    commodityStock.setType(LocalStockEnum.ADD.getType());
                    commodityStock.setCreateTime(new Date());
                    saveCommodityStocks.add(commodityStock);
                }
                //批量保持库存
                this.saveBatch(saveCommodityStocks);
            } catch (Exception e) {
                String errorMsg = StrUtil.format("commodityLockStock error! orderNo:{}", orderInfo.getOrderNo());
                log.error(errorMsg, e);
            } finally {
                //释放锁
                RedisLockUtil.releaseLockKey(lockKey);
            }
        } else {
            log.warn("commodityLockStock failed to get lock! orderNo:{}", orderInfo.getOrderNo());
        }

    }

    @DS(DsConstants.DB_ORDER_MASTER)
    private List<CommodityStock> getCommodityStockList(OrderInfo orderInfo, LocalStockEnum add) {
        LambdaQueryWrapper<CommodityStock> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CommodityStock::getOrderNo, orderInfo.getOrderNo());
        queryWrapper.eq(CommodityStock::getType, add.getType());
        List<CommodityStock> existList = this.list(queryWrapper);
        return existList;
    }

    /**
     * 解锁商品库存. 由原来cn.hydee.middle.business.order.v2.feign.erp.ErpCommonClient#lockStockNoBatchNo(java.lang.String, java.lang.String, cn.hydee.middle.business.order.entity.OrderInfo, java.util.List, java.lang.Integer, java.lang.Integer)
     * 方法改造.注释原来stockDeduction方法改为取消订单退款 等入口调用
     *
     * @param orderInfo
     * @param orderDetailList
     */
    @Override
    public void unlockStock(OrderInfo orderInfo, List<OrderDetail> orderDetailList) {
        if (orderInfo == null) {
            return;
        }
        // https://www.tapd.cn/61969829/prong/stories/view/1161969829001056185
//        if (PlatformCodeEnum.YD_JIA.getCode().equals(orderInfo.getThirdPlatformCode())) {
//            return;
//        }
        log.info("commodityUnlockStock param orderNo:{},thirdOrderNo:{} ", orderInfo.getOrderNo(), orderInfo.getThirdOrderNo());
        String lockKey = RedisKeyUtil.getCommodityStockLock(orderInfo.getOrderNo().toString());
        if (RedisLockUtil.getLockKey(lockKey, String.valueOf(System.currentTimeMillis()))) {
            try {
                //查询已锁库存
                List<CommodityStock> lockExistList = getCommodityStockList(orderInfo, LocalStockEnum.ADD);
                if (CollUtil.isEmpty(lockExistList)) {
                    return;
                }
                Map<String, CommodityStock> lockExistMap = lockExistList.stream().collect(Collectors.toMap(it -> it.getOrderNo() + "_" + it.getOrderDetailId() + "_" + it.getOrganizationCode(), a -> a));

                //查询已解锁库存
                List<CommodityStock> unlockExistList = getCommodityStockList(orderInfo, LocalStockEnum.SUB);
                log.info("查询已解锁库存 unlockExistList {}", unlockExistList);
                //已解锁库存数量 key 订单号+订单详情ID , value 数量
                Map<String, Integer> unLockQtyMap = new HashMap<>(8);
                for (CommodityStock commodityStock : unlockExistList) {
                    String key = commodityStock.getOrderNo() + "_" + commodityStock.getOrderDetailId() + "_" + commodityStock.getOrganizationCode();
                    int qty = unLockQtyMap.getOrDefault(key, 0);
                    qty = qty + commodityStock.getStockQty();
                    unLockQtyMap.put(key, qty);
                }
                log.info("unLockQtyMap, {}", JSONObject.toJSONString(unLockQtyMap));
                log.info("orderDetailList, {}", JSONObject.toJSONString(orderDetailList));
                if (CollUtil.isEmpty(orderDetailList)) {
                    orderDetailList = new ArrayList<>();
                    for (CommodityStock commodityStock : lockExistList) {
                        String key = commodityStock.getOrderNo() + "_" + commodityStock.getOrderDetailId() + "_" + commodityStock.getOrganizationCode();
                        Integer unLockQty = unLockQtyMap.getOrDefault(key, 0);
                        // 剩余解锁库存 = 锁定库存-已解锁库存
                        int surplusStockQty = commodityStock.getStockQty() - unLockQty;
                        if (surplusStockQty <= 0) {
                            continue;
                        }
                        OrderDetail orderDetail = new OrderDetail();
                        orderDetail.setErpCode(commodityStock.getErpCode());
                        orderDetail.setOrderNo(Long.valueOf(commodityStock.getOrderNo()));
                        orderDetail.setId(commodityStock.getOrderDetailId());
                        orderDetail.setCommodityCount(surplusStockQty);
                        orderDetail.setRefundCount(0);
                        orderDetailList.add(orderDetail);
                    }
                }
                //查下erp库存异常数据
                List<CommodityExceptionOrder> commodityExceptionOrders = commodityExceptionOrderService.getListByOrderNo(orderInfo.getOrderNo(), CollUtil.list(false, DsConstants.INTEGER_TWO, DsConstants.INTEGER_THREE));
                List<String> exceptionErpCode = CollUtil.emptyIfNull(commodityExceptionOrders).stream()
                        .filter(it -> OrderDetailStatusEnum.OUT_OF_STOCK.getCode().equals(it.getStatus()))
                        .map(CommodityExceptionOrder::getErpCode)
                        .collect(Collectors.toList());
                log.info("commodityExceptionOrders", JSONObject.toJSONString(commodityExceptionOrders));

                Iterator<OrderDetail> iterator = orderDetailList.iterator();
                while (iterator.hasNext()) {
                    OrderDetail next = iterator.next();
                    String key = next.getOrderNo() + "_" + next.getId() + "_" + orderInfo.getOrganizationCode();
                    if (!lockExistMap.containsKey(key)) {
                        iterator.remove();
                        continue;
                    }
                    if (next.getPickCount() <= 0) {
                        continue;
                    }
                    CommodityStock commodityLockStock = lockExistMap.get(key);
                    Integer qty = unLockQtyMap.getOrDefault(key, 0);
                    unLockQtyMap.put(key, qty + next.getPickCount());
                    qty = unLockQtyMap.get(key);
                    // 当前解锁数量+本次解锁数量>已锁定数量  continue
                    if (qty > commodityLockStock.getStockQty()) {
                        iterator.remove();
                    }
                }
                log.info("iterator orderDetailList {}", JSONObject.toJSONString(orderDetailList));
                if (CollUtil.isEmpty(orderDetailList)) {
                    return;
                }
                List<StockDeductReqDto.StockCommodity> stockCommodityList = buildStockCommodityData(orderDetailList, Boolean.TRUE);
                StockDeductReqDto stockDeductReqDto = new StockDeductReqDto();
                stockDeductReqDto.setStockList(stockCommodityList);
                stockDeductReqDto.setAdd(Boolean.TRUE);
                stockDeductReqDto.setUserName("系统自动");
                stockDeductReqDto.setOrderNo(orderInfo.getOrderNo());
                stockDeductReqDto.setMerCode(orderInfo.getMerCode());
                stockDeductReqDto.setOnlineStoreCode(orderInfo.getOnlineStoreCode());
                stockDeductReqDto.setPlatformCode(orderInfo.getThirdPlatformCode());
                stockDeductReqDto.setNote("解锁库存");
                stockDeductReqDto.setStoreCode(orderInfo.getOrganizationCode());
                stockDeductReqDto.setStoreId(baseInfoCache.getStoreIdByMerCodeOrgCode(orderInfo.getMerCode(), orderInfo.getOrganizationCode()));
                //商品中台解锁库存
                ResponseBase<StockDeductRspDto> base = middleMerchandiseClient.stockOccupyStockV2(stockDeductReqDto);

                log.info("解锁商品中台库存===> requestParam:{}, result:{}",
                        JSON.toJSONString(stockDeductReqDto), JSON.toJSONString(base));
//                Arrays.stream(Thread.currentThread().getStackTrace()).forEach(it -> log.info("解锁商品中台库存===> {}", it));
                if (base.checkSuccess() && DsConstants.INTEGER_ZERO.equals(base.getData().getStatus())) {
                    List<CommodityStock> saveCommodityStocks = new ArrayList<>();
                    for (OrderDetail orderDetail : orderDetailList) {
                        CommodityStock commodityStock = new CommodityStock();
                        commodityStock.setMerCode(orderInfo.getMerCode());
                        commodityStock.setErpCode(orderDetail.getErpCode());
                        commodityStock.setOrderNo(orderDetail.getOrderNo().toString());
                        commodityStock.setOrderDetailId(orderDetail.getId());
                        commodityStock.setOnlineStoreCode(orderInfo.getOnlineStoreCode());
                        commodityStock.setOrganizationCode(orderInfo.getOrganizationCode());
                        commodityStock.setStockQty(orderDetail.getPickCount());
                        commodityStock.setCreateTime(new Date());
                        commodityStock.setType(LocalStockEnum.SUB.getType());
                        saveCommodityStocks.add(commodityStock);
                    }
                    this.saveBatch(saveCommodityStocks);
                }
            } catch (Exception e) {
                String errorMsg = StrUtil.format("commodityUnlockStock error! orderNo:{}", orderInfo.getOrderNo());
                log.error(errorMsg, e);
            } finally {
                RedisLockUtil.releaseLockKey(lockKey);
            }
        } else {
            log.warn("commodityUnlockStock failed to get lock! orderNo:{}", orderInfo.getOrderNo());
        }
    }


    /**
     * ERP 下账成功后 通知商品中台 释放库存 并且添加记录到 commodityStock表
     *
     * <AUTHOR>
     * @Description
     * @date 2023/12/15 11:31
     */
    @Override
    public void erpSucceedUnlockStock(OrderInfo orderInfo) {
        List<OrderDetail> orderDetails = orderBasicService.getOrderDetailListWithCheck(orderInfo.getOrderNo());
        this.unLockStockNew(orderInfo, orderDetails, "ERP下账成功后通知商品中台释放库存",UnLockStockTypeEnum.PART);
    }


    /**
     * 异常订单换货锁定新库存
     *
     * <AUTHOR>
     * @Description
     * @date 2023/12/19 15:13
     */
    @Override
    @Transactional(propagation=Propagation.NOT_SUPPORTED)
    public void abnormalExchange(OrderInfo orderInfo, List<OrderDetail> orderDetailList, String note) {
        // 获取订单锁定库存
        List<OrderDetail> lockStockListOrderDetail = getLockStockListOrderDetail(orderInfo, orderDetailList);
        if(CollUtil.isEmpty(lockStockListOrderDetail)){return;}
        List<StockDeductReqDto.StockCommodity> stockCommodityList = new ArrayList<>();
        List<CommodityStock> saveCommodityStocks = new ArrayList<>();
        lockStockListOrderDetail.forEach(p -> {
            Long l = middleIdClient.getId(1).get(0);
            StockDeductReqDto.StockCommodity stockCommodity = new StockDeductReqDto.StockCommodity(p.getErpCode(), p.getPickCount(),l );
            stockCommodityList.add(stockCommodity);
            CommodityStock commodityStock = new CommodityStock();
            commodityStock.setMerCode(orderInfo.getMerCode());
            commodityStock.setErpCode(p.getErpCode());
            commodityStock.setOrderNo(p.getOrderNo().toString());
            commodityStock.setOrderDetailId(p.getId());
            commodityStock.setOnlineStoreCode(orderInfo.getOnlineStoreCode());
            commodityStock.setOrganizationCode(orderInfo.getOrganizationCode());
            commodityStock.setStockQty(p.getPickCount());
            commodityStock.setCreateTime(new Date());
            commodityStock.setSerialNumber(l);
            commodityStock.setType(LocalStockEnum.ADD_ENQUEUE.getType());
            saveCommodityStocks.add(commodityStock);
        });
        this.saveBatch(saveCommodityStocks);
        try {
            // 扣减库存
            StockDeductReqDto stockDeductReqDto = new StockDeductReqDto();
            stockDeductReqDto.setStockList(stockCommodityList);
            stockDeductReqDto.setAdd(false);
            stockDeductReqDto.setUserName("系统自动");
            stockDeductReqDto.setOrderNo(orderInfo.getOrderNo());
            stockDeductReqDto.setMerCode(orderInfo.getMerCode());
            stockDeductReqDto.setOnlineStoreCode(orderInfo.getOnlineStoreCode());
            stockDeductReqDto.setPlatformCode(orderInfo.getThirdPlatformCode());
            stockDeductReqDto.setNote(note);
            stockDeductReqDto.setStoreCode(orderInfo.getOrganizationCode());
            stockDeductReqDto.setStoreId(baseInfoCache.getStoreIdByMerCodeOrgCode(orderInfo.getMerCode(), orderInfo.getOrganizationCode()));
            //商品中台扣库存，只能返回成功
            ResponseBase<StockDeductRspDto> base = middleMerchandiseClient.stockOccupyStockV2(stockDeductReqDto);
            log.info("异常订单换货调用商品中台扣库存requestParam:{}:base{}", JSON.toJSONString(stockDeductReqDto), JSON.toJSONString(base));
            if (!base.checkSuccess()) {
                throw ExceptionUtil.getWarnException(DsErrorType.MERCHANDISE_STOCK_DEDUCT_ERROR);
            }
            List<String> exceptionErpCode = CollUtil.list(false);
            if (!DsConstants.INTEGER_ZERO.equals(base.getData().getStatus())) {
                List<String> notFoundItem = base.getData().getNotFoundItem();
                List<String> outOfStock = base.getData().getOutOfStock();
                if (CollUtil.isNotEmpty(notFoundItem)) {
                    exceptionErpCode.addAll(notFoundItem);
                }
                if (CollUtil.isNotEmpty(outOfStock)) {
                    exceptionErpCode.addAll(outOfStock);
                }
            }
            saveCommodityStocks.forEach(a -> {
                a.setType(LocalStockEnum.ADD.getType());
                if (CollUtil.isNotEmpty(exceptionErpCode) && exceptionErpCode.contains(a.getErpCode())) {
                    a.setType(LocalStockEnum.ADD_FAIL.getType());
                }
            });
            this.updateBatchById(saveCommodityStocks);
            StockDeductRspDto stockDeductRspDto = base.getData();
            if (!DsConstants.INTEGER_ZERO.equals(stockDeductRspDto.getStatus())) {
                if (stockDeductRspDto.getNotFoundItem() != null && !stockDeductRspDto.getNotFoundItem().isEmpty()) {
                    String msg = String.format("商品中台商品不存在，erp编码:%s", stockDeductRspDto.getNotFoundItem().get(0));
                    log.info(""+msg);
                    throw ExceptionUtil.getWarnException(DsErrorType.ERP_MERCHANDISE_NOT_EXISTS_ERROR.getCode(), DsErrorType.ERP_MERCHANDISE_NOT_EXISTS_ERROR.getMsg());
                }
                if (stockDeductRspDto.getOutOfStock() != null && !stockDeductRspDto.getOutOfStock().isEmpty()) {
                    String msg = String.format("商品中台库存不足，erp编码:%s", stockDeductRspDto.getOutOfStock().get(0));
                    log.info(""+msg);
                    throw ExceptionUtil.getWarnException(DsErrorType.ERP_MERCHANDISE_NO_STOCK.getCode(), DsErrorType.ERP_MERCHANDISE_NO_STOCK.getMsg());
                }
            }
        } catch (WarnException e) {
            throw ExceptionUtil.getWarnException(e.getCode(), e.getTipMessage());
        } catch (Exception e) {
            log.error("通知解锁商品中台锁定失败Exception===> {}", e);
            saveCommodityStocks.forEach(a -> a.setType(LocalStockEnum.EXCEPTION.getType()));
            this.updateBatchById(saveCommodityStocks);
            throw e;
        }

    }


    /**
     * @Description: 区分组合商品
     * @Param: [orderDetailList, pickCountFlag]
     * @return: java.util.List<cn.hydee.middle.business.order.dto.req.StockDeductReqDto.StockCommodity>
     * @Author: syuson
     * @Date: 2021-6-3
     */
    public List<StockDeductReqDto.StockCommodity> buildStockCommodityData(List<OrderDetail> orderDetailList, boolean pickCountFlag) {
        List<StockDeductReqDto.StockCommodity> stockCommodityList = new ArrayList<>();

        List<StockDeductReqDto.StockCommodity> stockChaiLingCommodityList = new ArrayList<>();
        List<StockDeductReqDto.StockCommodity> finalStockChaiLingCommodityList = stockChaiLingCommodityList;
        orderDetailList.forEach(orderDetail -> {
            Long l = middleIdClient.getId(1).get(0);
            orderDetail.setSerialNumber(l);
            if (DsConstants.INTEGER_TWO.equals(orderDetail.getChailing())
                    && !StringUtils.isEmpty(orderDetail.getChaiLingOriginalErpCode())) {
                // 拆零商品
                StockDeductReqDto.StockCommodity stockComposeCommodity = null;
                if (pickCountFlag) {
                    stockComposeCommodity = new StockDeductReqDto.StockCommodity(orderDetail.getChaiLingOriginalErpCode(), orderDetail.getPickCount(), l);
                } else {
                    stockComposeCommodity = new StockDeductReqDto.StockCommodity(orderDetail.getChaiLingOriginalErpCode(), orderDetail.getCommodityCount(), l);
                }
                finalStockChaiLingCommodityList.add(stockComposeCommodity);
            } else {
                StockDeductReqDto.StockCommodity stockCommodity = null;
                if (pickCountFlag) {
                    stockCommodity = new StockDeductReqDto.StockCommodity(orderDetail.getErpCode(), orderDetail.getPickCount(),l);
                } else {
                    stockCommodity = new StockDeductReqDto.StockCommodity(orderDetail.getErpCode(), orderDetail.getCommodityCount(), l);
                }
                stockCommodityList.add(stockCommodity);
            }
        });
        if (!CollectionUtils.isEmpty(stockChaiLingCommodityList)) {
            stockChaiLingCommodityList = stockChaiLingCommodityList.stream().distinct().collect(Collectors.toList());
            stockCommodityList.addAll(stockChaiLingCommodityList);
        }
        return stockCommodityList;
    }


    /**
     * 锁定库存
     *
     * <AUTHOR>
     * @Description
     * @date 2023/12/19 18:18
     */
    @Override
    @Transactional(propagation=Propagation.NOT_SUPPORTED)
    public void lockStockNew(OrderInfo orderInfo, List<OrderDetail> orderDetailList, String note) {
        readWriteLock.writeLockExecutor(ReadWriteLock.WAIT_SECOND, ReadWriteLock.OVERDUE_SECOND, TimeUnit.SECONDS, String.valueOf(orderInfo.getOrderNo()), key -> {
            List<OrderDetail> lockStockListOrderDetail = getLockStockListOrderDetail(orderInfo, orderDetailList);
            if (CollUtil.isEmpty(lockStockListOrderDetail)) {
                return;
            }
            lockStockAfter(orderInfo, lockStockListOrderDetail, note);
        });
    }
    /**
     * 传入订单主表信息 和需要锁定的商品明细
     *
     * <AUTHOR>
     * @Description
     * @date 2023/12/19 16:42
     */
    public List<OrderDetail> getLockStockListOrderDetail(OrderInfo orderInfo, List<OrderDetail> orderDetailList) {
        List<CommodityStock> allCommodityStock = getCommodityStockList(orderInfo.getOrderNo());
        if (CollUtil.isEmpty(allCommodityStock)) {
            return orderDetailList;
        }
        // 锁定的
        List<CommodityStock> lockExistList = allCommodityStock.stream().filter(c -> LocalStockEnum.ADD.getType() == c.getType()  && Objects.nonNull(c.getSerialNumber())).collect(Collectors.toList());
        if (CollUtil.isEmpty(lockExistList)) {
            return orderDetailList;
        }
        // 筛选已经释放的商品
        List<CommodityStock> unlockExistList = allCommodityStock.stream().filter(c -> LocalStockEnum.SUB.getType() == c.getType()  && Objects.nonNull(c.getSerialNumber())).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(unlockExistList)){
            // 已解锁库存数量 key 订单号+订单详情ID , value 数量
            Map<Long, Integer> unLockQtyMap = new HashMap<>(8);
            for (CommodityStock commodityStock : unlockExistList) {
                Long key = commodityStock.getSerialNumber();
                int qty = unLockQtyMap.getOrDefault(key, 0);
                qty = qty + commodityStock.getStockQty();
                unLockQtyMap.put(key, qty);
            }
            // 迭代器删除不需要释放
            Iterator<CommodityStock> commodityStockIterator = lockExistList.iterator();
            while (commodityStockIterator.hasNext()) {
                CommodityStock next = commodityStockIterator.next();
                Long key = next.getSerialNumber();
                Integer unLockQty = unLockQtyMap.getOrDefault(key, 0);
                int surplusStockQty = next.getStockQty() - unLockQty;
                if (surplusStockQty <= 0) {
                    commodityStockIterator.remove();
                } else {
                    // 特殊处理需要释放的库存
                    next.setStockQty(surplusStockQty);
                }
            }
            if (CollUtil.isEmpty(lockExistList)) {
                return orderDetailList;
            }
        }
        Map<String, CommodityStock> newStockMap = lockExistList.stream().collect(Collectors.toMap(it -> it.getOrderNo() + "_" + it.getOrderDetailId() + "_" + it.getErpCode(), a -> a));
        Iterator<OrderDetail> iterator = orderDetailList.iterator();
        while (iterator.hasNext()) {
            OrderDetail next = iterator.next();
            String key = next.getOrderNo() + "_" + next.getId() + "_" + next.getErpCode();
            if (newStockMap.containsKey(key)) {
                iterator.remove();
            }
        }
        return orderDetailList;
    }


    /**
     * 释放库存
     *
     * <AUTHOR>
     * @Description
     * @date 2023/12/19 18:20
     */
    @Override
    @Transactional(propagation=Propagation.NOT_SUPPORTED)
    public void unLockStockNew(OrderInfo orderInfo, List<OrderDetail> orderDetailList, String note, UnLockStockTypeEnum unLockStockTypeEnum) {
        readWriteLock.writeLockExecutor(ReadWriteLock.WAIT_SECOND, ReadWriteLock.OVERDUE_SECOND, TimeUnit.SECONDS, String.valueOf(orderInfo.getOrderNo()), key -> {
            List<OrderDetail> unLockStockListOrderDetail = getUnLockStockListOrderDetail(orderInfo, orderDetailList, unLockStockTypeEnum);
            if (CollUtil.isEmpty(unLockStockListOrderDetail)) {
                return;
            }
            unlockStockAfter(orderInfo, unLockStockListOrderDetail, note);
        });
    }





    /**
     * 传入订单主表信息 和需要解锁的商品明细
     * 返回需要解锁的商品明细
     *
     * <AUTHOR>
     * @Description
     * @date 2023/12/19 16:42
     */
    public List<OrderDetail> getUnLockStockListOrderDetail(OrderInfo orderInfo, List<OrderDetail> orderDetailList,UnLockStockTypeEnum unLockStockTypeEnum) {
        LambdaQueryWrapper<CommodityStock> commodityStockLambdaQueryWrapper = new LambdaQueryWrapper<>();
        commodityStockLambdaQueryWrapper.eq(CommodityStock::getOrderNo, orderInfo.getOrderNo());
        List<CommodityStock> allCommodityStock = getCommodityStockList(orderInfo.getOrderNo());
        if (CollUtil.isEmpty(allCommodityStock)) {
            return Lists.newArrayList();
        }
        // 锁定的
        List<CommodityStock> lockExistList = allCommodityStock.stream().filter(c -> LocalStockEnum.ADD.getType() == c.getType() && Objects.nonNull(c.getSerialNumber())).collect(Collectors.toList());
        if (CollUtil.isEmpty(lockExistList)) {
            return Lists.newArrayList();
        }
        // 筛选已经释放的商品
        List<CommodityStock> unlockExistList = allCommodityStock.stream().filter(c -> LocalStockEnum.SUB.getType() == c.getType()  && Objects.nonNull(c.getSerialNumber())).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(unlockExistList)){
            // 已解锁库存数量 key 订单号+订单详情ID , value 数量
            Map<Long, Integer> unLockQtyMap = new HashMap<>(8);
            for (CommodityStock commodityStock : unlockExistList) {
                Long key = commodityStock.getSerialNumber();
                int qty = unLockQtyMap.getOrDefault(key, 0);
                qty = qty + commodityStock.getStockQty();
                unLockQtyMap.put(key, qty);
            }
            // 迭代器删除不需要释放
            Iterator<CommodityStock> commodityStockIterator = lockExistList.iterator();
            while (commodityStockIterator.hasNext()) {
                CommodityStock next = commodityStockIterator.next();
                Long key = next.getSerialNumber();
                Integer unLockQty = unLockQtyMap.getOrDefault(key, 0);
                int surplusStockQty = next.getStockQty() - unLockQty;
                if (surplusStockQty <= 0) {
                    commodityStockIterator.remove();
                } else {
                    // 特殊处理需要释放的库存
                    next.setStockQty(surplusStockQty);
                }
            }
            if (CollUtil.isEmpty(lockExistList)) {
                return Lists.newArrayList();
            }
        }
        // 如果传入得商品明细为null，将占用库存记录放入明细  没有释放过
        if (CollUtil.isEmpty(orderDetailList)) {
            orderDetailList = new ArrayList<>();
            for (CommodityStock commodityStock : lockExistList) {
                OrderDetail orderDetail = new OrderDetail();
                orderDetail.setErpCode(commodityStock.getErpCode());
                orderDetail.setOrderNo(Long.valueOf(commodityStock.getOrderNo()));
                orderDetail.setId(commodityStock.getOrderDetailId());
                orderDetail.setCommodityCount(commodityStock.getStockQty());
                orderDetail.setRefundCount(0);
                orderDetail.setSerialNumber(commodityStock.getSerialNumber());
                orderDetailList.add(orderDetail);
            }
            return orderDetailList;
        }
        Map<String, CommodityStock> newStockMap = lockExistList.stream().collect(Collectors.toMap(it -> it.getOrderNo() + "_" + it.getOrderDetailId() + "_" + it.getErpCode(), a -> a));
        Iterator<OrderDetail> iterator = orderDetailList.iterator();
        while (iterator.hasNext()) {
            OrderDetail next = iterator.next();
            String key = next.getOrderNo() + "_" + next.getId() + "_" + next.getErpCode();
            if (!newStockMap.containsKey(key)) {
                iterator.remove();
            } else {
                CommodityStock commodityStock = newStockMap.get(key);
                // 设置可以释放的数量 全部释放放入占用库存
                if(UnLockStockTypeEnum.ALL.equals(unLockStockTypeEnum)){
                    next.setCommodityCount(commodityStock.getStockQty());
                }
                // 占用的流水号
                next.setSerialNumber(commodityStock.getSerialNumber());
            }
        }
        return orderDetailList;
    }


    /**
     * 锁定库存之后的操作
     *
     * <AUTHOR>
     * @Description
     * @date 2024/2/22 16:53
     */
    private void lockStockAfter(OrderInfo orderInfo, List<OrderDetail> orderDetailList, String note) {
        List<StockDeductReqDto.StockCommodity> stockCommodityList = buildStockCommodityData(orderDetailList, Boolean.TRUE);
        if (CollUtil.isEmpty(stockCommodityList)) {
            return;
        }
        List<CommodityStock> saveCommodityStocks = new ArrayList<>();
        for (OrderDetail orderDetail : orderDetailList) {
            CommodityStock commodityStock = new CommodityStock();
            commodityStock.setMerCode(orderInfo.getMerCode());
            commodityStock.setErpCode(orderDetail.getErpCode());
            commodityStock.setOrderNo(orderDetail.getOrderNo().toString());
            commodityStock.setOrderDetailId(orderDetail.getId());
            commodityStock.setOnlineStoreCode(orderInfo.getOnlineStoreCode());
            commodityStock.setOrganizationCode(orderInfo.getOrganizationCode());
            commodityStock.setStockQty(orderDetail.getCommodityCount());
            commodityStock.setCreateTime(new Date());
            commodityStock.setType(LocalStockEnum.ADD_ENQUEUE.getType());
            commodityStock.setSerialNumber(orderDetail.getSerialNumber());
            saveCommodityStocks.add(commodityStock);
        }
        this.saveBatch(saveCommodityStocks);
        try {
            StockDeductReqDto stockDeductReqDto = new StockDeductReqDto();
            stockDeductReqDto.setStockList(stockCommodityList);
            stockDeductReqDto.setAdd(Boolean.FALSE);
            stockDeductReqDto.setUserName("系统自动");
            stockDeductReqDto.setOrderNo(orderInfo.getOrderNo());
            stockDeductReqDto.setMerCode(orderInfo.getMerCode());
            stockDeductReqDto.setOnlineStoreCode(orderInfo.getOnlineStoreCode());
            stockDeductReqDto.setPlatformCode(orderInfo.getThirdPlatformCode());
            stockDeductReqDto.setNote(note);
            stockDeductReqDto.setStoreCode(orderInfo.getOrganizationCode());
            stockDeductReqDto.setStoreId(baseInfoCache.getStoreIdByMerCodeOrgCode(orderInfo.getMerCode(), orderInfo.getOrganizationCode()));
            // 商品中台解锁库存
            ResponseBase<StockDeductRspDto> base = middleMerchandiseClient.stockOccupyStockV2(stockDeductReqDto);
            log.info("调用商品中台锁定库存===> requestParam:{}, result:{}", JSON.toJSONString(stockDeductReqDto), JSON.toJSONString(base));
            if (!base.checkSuccess()) {
                throw ExceptionUtil.getWarnException(DsErrorType.MERCHANDISE_STOCK_DEDUCT_ERROR);
            }
            List<String> exceptionErpCode = CollUtil.list(false);
            if (!DsConstants.INTEGER_ZERO.equals(base.getData().getStatus())) {
                List<String> notFoundItem = base.getData().getNotFoundItem();
                List<String> outOfStock = base.getData().getOutOfStock();
                if (CollUtil.isNotEmpty(notFoundItem)) {
                    exceptionErpCode.addAll(notFoundItem);
                }
                if (CollUtil.isNotEmpty(outOfStock)) {
                    exceptionErpCode.addAll(outOfStock);
                }
            }
            saveCommodityStocks.forEach(a -> {
                a.setType(LocalStockEnum.ADD.getType());
                if (CollUtil.isNotEmpty(exceptionErpCode) && exceptionErpCode.contains(a.getErpCode())) {
                    a.setType(LocalStockEnum.ADD_FAIL.getType());
                }
            });
            this.updateBatchById(saveCommodityStocks);
        } catch (Exception e) {
            log.error("通知解锁商品中台锁定失败===> {}", e);
            saveCommodityStocks.forEach(a -> a.setType(LocalStockEnum.EXCEPTION.getType()));
            this.updateBatchById(saveCommodityStocks);
        }
    }


    /**
     * 释放库存之后的操作
     *
     * <AUTHOR>
     * @Description
     * @date 2024/2/21 15:55
     */
    private void unlockStockAfter(OrderInfo orderInfo, List<OrderDetail> orderDetailList, String note) {
        List<StockDeductReqDto.StockCommodity> stockCommodityList = buildUnlockStockCommodityData(orderDetailList, Boolean.FALSE);
        if (CollUtil.isEmpty(stockCommodityList)) {
            return;
        }
        List<CommodityStock> saveCommodityStocks = new ArrayList<>();
        for (OrderDetail orderDetail : orderDetailList) {
            CommodityStock commodityStock = new CommodityStock();
            commodityStock.setMerCode(orderInfo.getMerCode());
            commodityStock.setErpCode(orderDetail.getErpCode());
            commodityStock.setOrderNo(orderDetail.getOrderNo().toString());
            commodityStock.setOrderDetailId(orderDetail.getId());
            commodityStock.setOnlineStoreCode(orderInfo.getOnlineStoreCode());
            commodityStock.setOrganizationCode(orderInfo.getOrganizationCode());
            commodityStock.setStockQty(orderDetail.getCommodityCount());
            commodityStock.setCreateTime(new Date());
            commodityStock.setSerialNumber(orderDetail.getSerialNumber());
            commodityStock.setType(LocalStockEnum.SUB_ENQUEUE.getType());
            saveCommodityStocks.add(commodityStock);
        }
        this.saveBatch(saveCommodityStocks);
        try {
            StockDeductReqDto stockDeductReqDto = new StockDeductReqDto();
            stockDeductReqDto.setStockList(stockCommodityList);
            stockDeductReqDto.setAdd(Boolean.TRUE);
            stockDeductReqDto.setUserName("系统自动");
            stockDeductReqDto.setOrderNo(orderInfo.getOrderNo());
            stockDeductReqDto.setMerCode(orderInfo.getMerCode());
            stockDeductReqDto.setOnlineStoreCode(orderInfo.getOnlineStoreCode());
            stockDeductReqDto.setPlatformCode(orderInfo.getThirdPlatformCode());
            stockDeductReqDto.setNote(note);
            stockDeductReqDto.setStoreCode(orderInfo.getOrganizationCode());
            stockDeductReqDto.setStoreId(baseInfoCache.getStoreIdByMerCodeOrgCode(orderInfo.getMerCode(), orderInfo.getOrganizationCode()));
            // 商品中台解锁库存
            ResponseBase<StockDeductRspDto> base = middleMerchandiseClient.stockOccupyStockV2(stockDeductReqDto);
            log.info("调用商品中台解锁库存===> requestParam:{}, result:{}", JSON.toJSONString(stockDeductReqDto), JSON.toJSONString(base));
            if (!base.checkSuccess()) {
                throw ExceptionUtil.getWarnException(DsErrorType.MERCHANDISE_STOCK_DEDUCT_ERROR);
            }
            List<String> exceptionErpCode = CollUtil.list(false);
            if (!DsConstants.INTEGER_ZERO.equals(base.getData().getStatus())) {
                List<String> notFoundItem = base.getData().getNotFoundItem();
                List<String> outOfStock = base.getData().getOutOfStock();
                if (CollUtil.isNotEmpty(notFoundItem)) {
                    exceptionErpCode.addAll(notFoundItem);
                }
                if (CollUtil.isNotEmpty(outOfStock)) {
                    exceptionErpCode.addAll(outOfStock);
                }
            }
            saveCommodityStocks.forEach(a -> {
                a.setType(LocalStockEnum.SUB.getType());
                if (CollUtil.isNotEmpty(exceptionErpCode) && exceptionErpCode.contains(a.getErpCode())) {
                    a.setType(LocalStockEnum.SUB_FAIL.getType());
                }
            });
            this.updateBatchById(saveCommodityStocks);
        } catch (Exception e) {
            log.error("通知解锁商品中台解锁库存失败===> {}", e);
            saveCommodityStocks.forEach(a -> a.setType(LocalStockEnum.EXCEPTION.getType()));
            this.updateBatchById(saveCommodityStocks);
        }
    }


    @DS(DsConstants.DB_ORDER_MASTER)
    private List<CommodityStock> getCommodityStockList(Long orderNo) {
        LambdaQueryWrapper<CommodityStock> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CommodityStock::getOrderNo,orderNo);
        return this.list(queryWrapper);
    }

    /**
     * @Description: 区分组合商品
     * @Param: [orderDetailList, pickCountFlag]
     * @return: java.util.List<cn.hydee.middle.business.order.dto.req.StockDeductReqDto.StockCommodity>
     * @Author: syuson
     * @Date: 2021-6-3
     */
    public List<StockDeductReqDto.StockCommodity> buildUnlockStockCommodityData(List<OrderDetail> orderDetailList, boolean pickCountFlag) {
        List<StockDeductReqDto.StockCommodity> stockCommodityList = new ArrayList<>();

        List<StockDeductReqDto.StockCommodity> stockChaiLingCommodityList = new ArrayList<>();
        List<StockDeductReqDto.StockCommodity> finalStockChaiLingCommodityList = stockChaiLingCommodityList;
        orderDetailList.forEach(orderDetail -> {
            Long l = orderDetail.getSerialNumber();
            if (DsConstants.INTEGER_TWO.equals(orderDetail.getChailing())
                    && !StringUtils.isEmpty(orderDetail.getChaiLingOriginalErpCode())) {
                // 拆零商品
                StockDeductReqDto.StockCommodity stockComposeCommodity = null;
                if (pickCountFlag) {
                    stockComposeCommodity = new StockDeductReqDto.StockCommodity(orderDetail.getChaiLingOriginalErpCode(), orderDetail.getPickCount(), l);
                } else {
                    stockComposeCommodity = new StockDeductReqDto.StockCommodity(orderDetail.getChaiLingOriginalErpCode(), orderDetail.getCommodityCount(), l);
                }
                finalStockChaiLingCommodityList.add(stockComposeCommodity);
            } else {
                StockDeductReqDto.StockCommodity stockCommodity = null;
                if (pickCountFlag) {
                    stockCommodity = new StockDeductReqDto.StockCommodity(orderDetail.getErpCode(), orderDetail.getPickCount(),l);
                } else {
                    stockCommodity = new StockDeductReqDto.StockCommodity(orderDetail.getErpCode(), orderDetail.getCommodityCount(), l);
                }
                stockCommodityList.add(stockCommodity);
            }
        });
        if (!CollectionUtils.isEmpty(stockChaiLingCommodityList)) {
            stockChaiLingCommodityList = stockChaiLingCommodityList.stream().distinct().collect(Collectors.toList());
            stockCommodityList.addAll(stockChaiLingCommodityList);
        }
        return stockCommodityList;
    }


    @Override
    public void abnormalExamination(OrderInfo orderInfo, String note) {
        LambdaQueryWrapper<CommodityStock> commodityStockLambdaQueryWrapper = new LambdaQueryWrapper<>();
        commodityStockLambdaQueryWrapper.eq(CommodityStock::getOrderNo, orderInfo.getOrderNo());
        List<CommodityStock> allCommodityStock = this.list(commodityStockLambdaQueryWrapper);
        if (CollUtil.isEmpty(allCommodityStock)) {
            return ;
        }
        // 失败的
        List<CommodityStock> lockExistListOld = allCommodityStock.stream().filter(c -> (LocalStockEnum.ADD_FAIL.getType() == c.getType() || LocalStockEnum.EXCEPTION.getType() == c.getType())  && Objects.nonNull(c.getSerialNumber())).collect(Collectors.toList());
        if (CollUtil.isEmpty(lockExistListOld)) {
            return;
        }

        Map<String, CommodityStock> lockExistMap = lockExistListOld.stream().collect(Collectors.toMap(it -> it.getOrderNo() + "_" + it.getOrderDetailId() + "_" + it.getErpCode(), a -> a,(v1, v2) -> v1));
        List<CommodityStock> lockExistList = new ArrayList<>(lockExistMap.values());
        List<StockDeductReqDto.StockCommodity> stockCommodityList=new ArrayList<>();
        lockExistList.forEach(a->{
            StockDeductReqDto.StockCommodity stockCommodity = new StockDeductReqDto.StockCommodity(a.getErpCode(), a.getStockQty(), a.getSerialNumber());
            stockCommodityList.add(stockCommodity);

        });
        try {
            StockDeductReqDto stockDeductReqDto = new StockDeductReqDto();
            stockDeductReqDto.setStockList(stockCommodityList);
            stockDeductReqDto.setAdd(Boolean.FALSE);
            stockDeductReqDto.setUserName("系统自动");
            stockDeductReqDto.setOrderNo(orderInfo.getOrderNo());
            stockDeductReqDto.setMerCode(orderInfo.getMerCode());
            stockDeductReqDto.setOnlineStoreCode(orderInfo.getOnlineStoreCode());
            stockDeductReqDto.setPlatformCode(orderInfo.getThirdPlatformCode());
            stockDeductReqDto.setNote(note);
            stockDeductReqDto.setStoreCode(orderInfo.getOrganizationCode());
            stockDeductReqDto.setStoreId(baseInfoCache.getStoreIdByMerCodeOrgCode(orderInfo.getMerCode(), orderInfo.getOrganizationCode()));
            // 商品中台解锁库存
            ResponseBase<StockDeductRspDto> base = middleMerchandiseClient.stockOccupyStockV2(stockDeductReqDto);
            log.info("调用商品中台锁定库存===> requestParam:{}, result:{}", JSON.toJSONString(stockDeductReqDto), JSON.toJSONString(base));
            if (!base.checkSuccess()) {
                throw ExceptionUtil.getWarnException(DsErrorType.MERCHANDISE_STOCK_DEDUCT_ERROR);
            }
            List<String> exceptionErpCode = CollUtil.list(false);
            if (!DsConstants.INTEGER_ZERO.equals(base.getData().getStatus())) {
                List<String> notFoundItem = base.getData().getNotFoundItem();
                List<String> outOfStock = base.getData().getOutOfStock();
                if (CollUtil.isNotEmpty(notFoundItem)) {
                    exceptionErpCode.addAll(notFoundItem);
                }
                if (CollUtil.isNotEmpty(outOfStock)) {
                    exceptionErpCode.addAll(outOfStock);
                }
            }
            lockExistList.forEach(a -> {
                a.setType(LocalStockEnum.ADD.getType());
                if (CollUtil.isNotEmpty(exceptionErpCode) && exceptionErpCode.contains(a.getErpCode())) {
                    a.setType(LocalStockEnum.ADD_FAIL.getType());
                }
            });
            this.updateBatchById(lockExistList);
        } catch (Exception e) {
            log.error("通知解锁商品中台锁定失败===> {}", e);
            lockExistList.forEach(a -> a.setType(LocalStockEnum.EXCEPTION.getType()));
            this.updateBatchById(lockExistList);
        }
    }


    @Override
    public void toolUnlockStock(OrderInfo orderInfo) {
        if (Objects.isNull(orderInfo) || !ErpStateEnum.HAS_SALE.getCode().equals(orderInfo.getErpState())) {
            return;
        }
        LambdaQueryWrapper<CommodityStock> commodityStockLambdaQueryWrapper = new LambdaQueryWrapper<>();
        commodityStockLambdaQueryWrapper.eq(CommodityStock::getOrderNo, orderInfo.getOrderNo());
        List<CommodityStock> allCommodityStock = getCommodityStockList(orderInfo.getOrderNo());
        if (CollUtil.isEmpty(allCommodityStock)) {
            return;
        }
        // 锁定的
        List<CommodityStock> lockExistList = allCommodityStock.stream().filter(c -> LocalStockEnum.ADD.getType() == c.getType()).collect(Collectors.toList());
        if (CollUtil.isEmpty(lockExistList)) {
            return;
        }
        // 筛选已经释放的商品
        List<CommodityStock> unlockExistList = allCommodityStock.stream().filter(c -> LocalStockEnum.SUB.getType() == c.getType()).collect(Collectors.toList());
        boolean anyMatch = lockExistList.stream().anyMatch(a -> Objects.nonNull(a.getSerialNumber()));
        if (anyMatch) {
            // 新手动释放
            extracted(orderInfo, unlockExistList, lockExistList);
        }
    }


    @Override
    public void unLockStockB2C(OmsOrderInfo omsOrderInfo) {
        Long orderNo = omsOrderInfo.getOmsOrderNo();
        List<CommodityStock> allCommodityStock = getCommodityStockList(orderNo);
        if(CollUtil.isEmpty(allCommodityStock)){
            allCommodityStock = getCommodityStockList(omsOrderInfo.getOrderNo());
            orderNo=omsOrderInfo.getOrderNo();
        }
        // 锁定的
        List<CommodityStock> lockExistList = allCommodityStock.stream().filter(c -> LocalStockEnum.ADD.getType() == c.getType() && Objects.nonNull(c.getSerialNumber())).collect(Collectors.toList());
        if (CollUtil.isEmpty(lockExistList)) {
            return;
        }
        // 筛选已经释放的商品
        List<CommodityStock> unlockExistList = allCommodityStock.stream().filter(c -> LocalStockEnum.SUB.getType() == c.getType() && Objects.nonNull(c.getSerialNumber())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(unlockExistList)) {
            lockExistList.removeIf(commodityStock ->
                    unlockExistList.stream().map(CommodityStock::getSerialNumber).collect(Collectors.toList()).contains(commodityStock.getSerialNumber()));
        }
        if (CollUtil.isEmpty(lockExistList)) {
            return;
        }

        List<CommodityStock> saveCommodityStocks = new ArrayList<>();
        for (CommodityStock commodityStock : lockExistList) {
            CommodityStock commodityStockNew = new CommodityStock();
            commodityStockNew.setMerCode(commodityStock.getMerCode());
            commodityStockNew.setErpCode(commodityStock.getErpCode());
            commodityStockNew.setOrderNo(orderNo.toString());
            commodityStockNew.setOrderDetailId(commodityStock.getOrderDetailId());
            commodityStockNew.setOnlineStoreCode(commodityStock.getOnlineStoreCode());
            commodityStockNew.setOrganizationCode(commodityStock.getOrganizationCode());
            commodityStockNew.setStockQty(commodityStock.getStockQty());
            commodityStockNew.setCreateTime(new Date());
            commodityStockNew.setSerialNumber(commodityStock.getSerialNumber());
            commodityStockNew.setType(LocalStockEnum.SUB_ENQUEUE.getType());
            commodityStockNew.setStoreId(commodityStock.getStoreId());
            saveCommodityStocks.add(commodityStockNew);
        }

        this.saveBatch(saveCommodityStocks);

        Map<String, List<CommodityStock>> commodityStockMap = saveCommodityStocks.stream()
                .collect(Collectors.groupingBy(
                        CommodityStock::getStoreId,
                        Collectors.toList()
                ));

        List<CommodityStock> updateCommodityStocks = new ArrayList<>();
        commodityStockMap.forEach((storeId, stockList) -> {
            List<StockDeductReqDto.StockCommodity> stockCommodityList = new ArrayList<>();
            stockList.forEach(a -> {
                StockDeductReqDto.StockCommodity stockCommodity = new StockDeductReqDto.StockCommodity(a.getErpCode(), a.getStockQty(), a.getSerialNumber());
                stockCommodityList.add(stockCommodity);
            });

            StockDeductReqDto stockDeductReqDto = new StockDeductReqDto();
            stockDeductReqDto.setStockList(stockCommodityList);
            stockDeductReqDto.setAdd(Boolean.TRUE);
            stockDeductReqDto.setUserName("系统自动");
            stockDeductReqDto.setOrderNo(omsOrderInfo.getOmsOrderNo());
            stockDeductReqDto.setMerCode(omsOrderInfo.getMerCode());
            stockDeductReqDto.setOnlineStoreCode(omsOrderInfo.getOnlineStoreCode());
            stockDeductReqDto.setPlatformCode(omsOrderInfo.getThirdPlatformCode());
            stockDeductReqDto.setNote("订单还库存");
            stockDeductReqDto.setStoreCode(omsOrderInfo.getOrganizationCode());
            stockDeductReqDto.setStoreId(storeId);

            try {
                // 商品中台解锁库存
                ResponseBase<StockDeductRspDto> base = middleMerchandiseClient.occupyStockHandleV2(stockDeductReqDto);
                log.info("调用商品中台锁定库存===> requestParam:{}, result:{}", JSON.toJSONString(stockDeductReqDto), JSON.toJSONString(base));
                // 记录失败日志 不报错 报错中断
                if (!base.checkSuccess()) {
                    stockList.forEach(o -> o.setType(LocalStockEnum.SUB_FAIL.getType()));
                }

                List<String> exceptionErpCode = CollUtil.list(false);
                if (!DsConstants.INTEGER_ZERO.equals(base.getData().getStatus())) {
                    List<String> notFoundItem = base.getData().getNotFoundItem();
                    List<String> outOfStock = base.getData().getOutOfStock();
                    if (CollUtil.isNotEmpty(notFoundItem)) {
                        exceptionErpCode.addAll(notFoundItem);
                    }
                    if (CollUtil.isNotEmpty(outOfStock)) {
                        exceptionErpCode.addAll(outOfStock);
                    }
                }
                stockList.forEach(a -> {
                    a.setType(LocalStockEnum.SUB.getType());
                    if (CollUtil.isNotEmpty(exceptionErpCode) && exceptionErpCode.contains(a.getErpCode())) {
                        a.setType(LocalStockEnum.SUB_FAIL.getType());
                    }
                });
            } catch (Exception e) {
                log.error("通知解锁商品中台锁定失败===> {}", e);
                stockList.forEach(a -> a.setType(LocalStockEnum.EXCEPTION.getType()));
            }
            updateCommodityStocks.addAll(stockList);
        });
        this.updateBatchById(updateCommodityStocks);
    }


    private void extracted(OrderInfo orderInfo, List<CommodityStock> unlockExistList, List<CommodityStock> lockExistList) {
        if (CollUtil.isNotEmpty(unlockExistList)) {
            // 已解锁库存数量 key 订单号+订单详情ID , value 数量
            Map<Long, Integer> unLockQtyMap = new HashMap<>(8);
            for (CommodityStock commodityStock : unlockExistList) {
                Long key = commodityStock.getSerialNumber();
                int qty = unLockQtyMap.getOrDefault(key, 0);
                qty = qty + commodityStock.getStockQty();
                unLockQtyMap.put(key, qty);
            }
            // 迭代器删除不需要释放
            Iterator<CommodityStock> commodityStockIterator = lockExistList.iterator();
            while (commodityStockIterator.hasNext()) {
                CommodityStock next = commodityStockIterator.next();
                Long key = next.getSerialNumber();
                Integer unLockQty = unLockQtyMap.getOrDefault(key, 0);
                int surplusStockQty = next.getStockQty() - unLockQty;
                if (surplusStockQty <= 0) {
                    commodityStockIterator.remove();
                } else {
                    // 特殊处理需要释放的库存
                    next.setStockQty(surplusStockQty);
                }
            }
            if (CollUtil.isEmpty(lockExistList)) {
                return;
            }
        }
        // 如果传入得商品明细为null，将占用库存记录放入明细  没有释放过
        List<OrderDetail> unLockStockListOrderDetail = new ArrayList<>();
        for (CommodityStock commodityStock : lockExistList) {
            OrderDetail orderDetail = new OrderDetail();
            orderDetail.setErpCode(commodityStock.getErpCode());
            orderDetail.setOrderNo(Long.valueOf(commodityStock.getOrderNo()));
            orderDetail.setId(commodityStock.getOrderDetailId());
            orderDetail.setCommodityCount(commodityStock.getStockQty());
            orderDetail.setRefundCount(0);
            orderDetail.setSerialNumber(commodityStock.getSerialNumber());
            unLockStockListOrderDetail.add(orderDetail);
        }

        if (CollUtil.isEmpty(unLockStockListOrderDetail)) {
            return;
        }
        unlockStockAfter(orderInfo, unLockStockListOrderDetail, "手动释放");
    }

    @Override
    public void unLockStockJob(CommodityStockReqDto dto) {
        int count = commodityStockMapper.getUnLockStockJobCount(dto);
        if(count==0){
            return;
        }
        int currentPage = 1;
        int pageSize = 500;
        int totalPageStep = (int) Math.ceil(count * 1.0 / pageSize);
        while (totalPageStep >= currentPage) {
            Page<CommodityStock> page = new Page<>(currentPage, pageSize);
            List<CommodityStock> records = commodityStockMapper.selectCommodityStockPage(page, dto).getRecords();

            ThreadUtils.doSempColl(records, a -> {
                if(StringUtils.isNotBlank(a.getStoreId())){
                    OmsOrderInfo omsOrderInfo = omsOrderInfoMapper.selectOne(new QueryWrapper<OmsOrderInfo>().lambda()
                            .eq(OmsOrderInfo::getOmsOrderNo, a.getOrderNo()).eq(OmsOrderInfo::getIsPostFeeOrder, 0).last("LIMIT 1"));
                    // 加上时间验证
                    if (Objects.nonNull(omsOrderInfo)) {
                        long between = ChronoUnit.DAYS.between(omsOrderInfo.getCreateTime(), LocalDateTime.now());
                        if(between < 0 || between>1){return;}
                        unLockStockB2C(omsOrderInfo);
                    }
                }else {
                    OrderInfo orderInfo = orderInfoMapper.selectOrderInfo(Long.valueOf(a.getOrderNo()));
                    if ( Objects.nonNull(orderInfo)) {
                        // 加入时间验证
                        int diffDays = DateUtil.getDiffDays(new Date(), orderInfo.getCreateTime());
                        if(diffDays < 0|| diffDays >1 ){return;}
                        toolUnlockStock(orderInfo);
                    }
                }
            },8);

            currentPage++;
        }
    }

}
