package cn.hydee.middle.business.order.dto.req.baseinfo;

import cn.hydee.starter.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DsOnlineStoreQueryDTO extends PageBase {

    @ApiModelProperty(hidden = true)
    private String merCode;

    @ApiModelProperty(value = "机构", notes = "可以根据机构名称、机构code模糊查询")
    private String orgKeyWord;

    @ApiModelProperty(value = "all:全部 0：无所属机构 1：有所属机构")
    private String orgFlag;

    @ApiModelProperty(value = "平台")
    private String platform;

    @ApiModelProperty(value = "门店名称")
    private String onlineStoreName;

    @ApiModelProperty(value = "网店名称")
    private String onlineClientName;

    @ApiModelProperty(value = "门店状态")
    private Integer status;

    @ApiModelProperty(value = "营业状态（1：营业中 2：休息）")
    private Integer openStatus;

    @ApiModelProperty(value = "服务模式：B2C O2O")
    private String serviceMode;

    @ApiModelProperty(value = "是否同步库存,0不同步，1同步")
    private Integer syncStock;

    @ApiModelProperty(value = "是否同步价格,0不同步，1同步")
    private Integer syncPrice;

    @ApiModelProperty(value = "机构id集合，用于查询子级机构集合，为商品那边添加的")
    private List<String> orgIdList;

    @ApiModelProperty(value = "网店编码集合，为商品那边添加的")
    private List<String> clientCodeList;

    @ApiModelProperty(value = "门店编码集合，为商品那边添加的")
    private List<String> orgCodeList;

    @ApiModelProperty(value = "线上门店编码集合，为商品那边添加的")
    private List<String> onlineStoreCodeList;

    @ApiModelProperty(value = "传1则校验权限,如果传了orgCodeList或orgIdList，则不管此参数，为商品那边添加的")
    private Integer checkAuth;

    @ApiModelProperty(value = "是否查询全部数据 为商品那边添加的")
    private Boolean hasAll;

    @ApiModelProperty(value = "店铺自动配置状态 1:处理中 2:有失败步骤 3:成功")
    private Integer storeAutoConfigStatus;

    @ApiModelProperty(value = "是否是营业设置页面查询")
    private Boolean openSetSelectFlag;

    @ApiModelProperty(value = "平台店铺编码")
    private String platformShopId;

    @ApiModelProperty(value = "店铺标签id")
    private List<String> tagIds;
}
