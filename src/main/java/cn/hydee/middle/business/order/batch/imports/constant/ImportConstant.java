package cn.hydee.middle.business.order.batch.imports.constant;

import cn.hydee.batch.dto.BatchImportBaseDTO;
import cn.hydee.middle.business.order.batch.imports.dto.*;

/**
 * 导入常量
 */
public interface ImportConstant {

    String OSS_DOWNLOAD_URL = "https://%s.%s/%s";
    String PATH = "order/hydee-business-order/template/%s.xls";

    String SUCC = "成功";
    String FAIL = "失败";

    String ASYNC_STOREDELIVERY = "AsyncStoreDelivery"; // 同步配送门店
    String SF_ASYNC_STOREDELIVERY = "SFAsyncStoreDelivery"; // 顺丰同步配送门店
    String EXPRESS_DEIVERY = "ExpressDelivery"; // 批量发货

    String SET_STOREORGANIZATION = "SetStoreOrganization"; // 批量设置门店所属机构
    String SET_STOREDELIVERY = "SetStoreDelivery"; // 批量设置配送门店

    String ASYNC_OPEN_STOREDELIVERY = "AsyncOpenStoreDelivery"; // 同步开放配送门店

    String COMMON_ADD_ONLINE_STORE = "CommonAddOnlineStore"; // 批量新增线上门店
    String B2C_ADD_ONLINE_STORE = "B2CAddOnlineStore"; // B2C批量新增O2O线上门店

    String ERP_CHECK_HANDLE = "ErpCheckHandle"; // 批量核对erp对账数据

    String BATCH_STORE_TIME_CONFIG = "batchStoreTimeConfig"; // 批量设置门店营业时间

    String BATCH_STORE_STATUS_CONFIG = "batchStoreStatusConfig"; // 批量设置门店营业状态

    String BATCH_ADD_TAG = "batchAddTag"; // 批量新增标签

    enum ImportType {
        // 批量上传配送门店
        ASYNC_STOREDELIVERY(ImportConstant.ASYNC_STOREDELIVERY, BatchAsyncStoreDeliveryDto.class,"批量上传配送门店ID模板","同步配送门店"),
        SF_ASYNC_STOREDELIVERY(ImportConstant.SF_ASYNC_STOREDELIVERY, BatchSFAsyncStoreDeliveryDto.class,"顺丰同城门店上传","顺丰同步配送门店"),
        EXPRESS_DEIVERY(ImportConstant.EXPRESS_DEIVERY, BatchExpressDeliveryDto.class,"批量发货模板","批量发货"),
        SET_STOREORGANIZATION(ImportConstant.SET_STOREORGANIZATION, BatchSetStoreOrganizationDto.class,"批量设置门店所属机构模板","批量设置门店所属机构"),
        SET_STOREDELIVERY(ImportConstant.SET_STOREDELIVERY, BatchSetStoreDeliveryDto.class,"批量设置配送门店模板","批量设置配送门店"),
        ASYNC_OPEN_STOREDELIVERY(ImportConstant.ASYNC_OPEN_STOREDELIVERY, BatchAsyncOpenStoreDeliveryDto.class,"同步开放配送门店","同步开放配送门店"),
        COMMON_ADD_ONLINE_STORE(ImportConstant.COMMON_ADD_ONLINE_STORE, BatchAddOnlineStoreDto.class,"批量新增线上门店模板","批量新增线上门店"),
        B2C_ADD_ONLINE_STORE(ImportConstant.B2C_ADD_ONLINE_STORE, BatchB2CAddOnlineStoreDto.class,"批量新增线上门店模板","B2C批量新增O2O线上门店"),
        ERP_CHECK_HANDLE(ImportConstant.ERP_CHECK_HANDLE, BatchHandleErpCheckResultDto.class,"ERP对账-手工批量核对模板","批量核对erp对账数据"),
        BATCH_STORE_TIME_CONFIG(ImportConstant.BATCH_STORE_TIME_CONFIG, BatchStoreTimeConfigDto.class,"批量设置门店营业时间模板","批量设置门店营业时间"),
        BATCH_STORE_STATUS_CONFIG(ImportConstant.BATCH_STORE_STATUS_CONFIG, BatchStoreStatusConfigDto.class,"批量设置门店营业状态模板","批量设置门店营业状态"),
        BATCH_ADD_TAG(ImportConstant.BATCH_ADD_TAG, BatchAddTagDto.class, "批量新增标签模版", "批量新增标签模版"),
        ;

        private String type;
        private Class<? extends BatchImportBaseDTO> dtoClazz;
        private String templateName;
        private String typeName;

        ImportType(String type, Class dtoClazz, String templateName, String typeName) {
            this.type = type;
            this.dtoClazz = dtoClazz;
            this.templateName = templateName;
            this.typeName = typeName;
        }

        public static ImportType getImportType(String type) {
            for (ImportType importType : ImportType.values()) {
                if(importType.getType().equals(type)) {
                    return importType;
                }
            }
            return null;
        }

        public String getType() {
            return type;
        }

        public Class getDtoClazz() {
            return dtoClazz;
        }

        public String getTemplateName() {
            return templateName;
        }

        public String getTypeName() {
            return typeName;
        }
    }
}
