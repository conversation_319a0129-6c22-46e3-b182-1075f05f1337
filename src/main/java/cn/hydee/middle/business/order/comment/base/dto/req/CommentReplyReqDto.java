  
package cn.hydee.middle.business.order.comment.base.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CommentReplyReqDto{
	@ApiModelProperty(value = "商家编码", hidden = true)
    private String merCode;
	@ApiModelProperty(value = "平台编码", required = true)
    private String thirdPlatformCode;
    @ApiModelProperty(value = "评价md5编码", required = true)
    private String md5Code;
    @ApiModelProperty(value = "评论回复", required = true)
    private String reply;
}
  
