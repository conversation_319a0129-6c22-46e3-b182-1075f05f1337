package cn.hydee.middle.business.order.dto.rsp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 骑手轨迹信息查询返回dto
 */
@Data
public class DeliveryRiderTrailInfoRespDto {

    @ApiModelProperty(value = "系统订单号")
    private Long orderNo;
    @ApiModelProperty(value = "三方平台订单号")
    private String thirdOrderNo;
    @ApiModelProperty(value = "三方平台编码")
    private String platformCode;
    @ApiModelProperty(value = "骑手姓名")
    private String riderName;
    @ApiModelProperty(value = "骑手电话")
    private String riderPhone;
    @ApiModelProperty(value = "轨迹经纬度信息")
    private List<DeliveryRiderTrailInfoRespDto.PointPosition> pointList;

    @Data
    public static class PointPosition{
        @ApiModelProperty(value = "纬度")
        private String latitude;
        @ApiModelProperty(value = "经度")
        private String longitude;
        @ApiModelProperty(value = "当前经纬度的时间")
        private Date pointTime;
    }

}
