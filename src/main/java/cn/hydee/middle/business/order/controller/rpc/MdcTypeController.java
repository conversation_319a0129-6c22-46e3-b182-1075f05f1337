package cn.hydee.middle.business.order.controller.rpc;

import cn.hydee.middle.business.order.feign.MdcTypeClient;
import cn.hydee.middle.business.order.feign.MdcTypeDimensionClient;
import cn.hydee.middle.business.order.domain.CommodityType;
import cn.hydee.middle.business.order.dto.MerCode;
import cn.hydee.middle.business.order.dto.req.*;
import cn.hydee.middle.business.order.dto.rsp.MultilevelTypeDTO;
import cn.hydee.middle.business.order.dto.rsp.TypeDimensionRespDTO;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ErrorType;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("${api.version}/comm-type")
@Api(value = "WEB - 商品分类接口", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, tags = {"商品分类操作接口"})
public class MdcTypeController extends AbstractController {

    @Resource
    private MdcTypeClient commodityTypeClient;
    @Resource
    private MdcTypeDimensionClient commodityTypeDimensionClient;

    @ApiOperation(value = "获取分类列表",notes = "获取分类列表(查询一级分类parentId为0，查询二三级分类传parentId)")
    @GetMapping("/getTypeList/{parentId}/{merCode}")
    public ResponseBase<List<CommodityType>> getTypeList(@PathVariable String parentId, @PathVariable String merCode){
        if(StringUtils.isEmpty(parentId)){
            throw WarnException.builder().code(ErrorType.PARA_ERROR.getCode()).
                    tipMessage(ErrorType.PARA_ERROR.getMsg()).build();
        }
        TypeSearchReqDTO typeSearchReqDTO = new TypeSearchReqDTO();
        typeSearchReqDTO.setMerCode(merCode);
        List<String> ids = new ArrayList<>();
        ids.add(parentId);
        typeSearchReqDTO.setParentIds(ids);

        ResponseBase<List<CommodityType>> base = commodityTypeClient.getTypeList(typeSearchReqDTO);
        if (typeSearchReqDTO.getNotTree() != null && typeSearchReqDTO.getNotTree()) {
            return base;
        }
        List<CommodityType> list = listToTree(base.getData());
        base.setData(list);
        return base;
    }

    @ApiOperation(value = "获取分类树",notes = "获取分类树 merCode-商家编码(默认分类树 merCode='hydee') type-1.分类 2.分组")
    @PostMapping("/getTypeTree")
    public ResponseBase<List<CommodityType>> getTypeTree(@RequestBody TypeSearchReqDTO typeSearchReqDTO, BindingResult result){
        checkValid(result);
        if (typeSearchReqDTO.getUse() != null && typeSearchReqDTO.getUse()) {
            DimensionSearchDTO searchDTO = new DimensionSearchDTO();
            searchDTO.setMerCode(typeSearchReqDTO.getMerCode());
            searchDTO.setUseStatus(1);
            ResponseBase<List<TypeDimensionRespDTO>> list = commodityTypeDimensionClient.queryTypeDimensionTree(searchDTO);
            if (list != null && list.checkSuccess() && list.getData() != null
                    && !list.getData().isEmpty()) {
                typeSearchReqDTO.setDimensionId(list.getData().get(0).getId());
            }
        }
        ResponseBase<List<CommodityType>> base = commodityTypeClient.getTypeList(typeSearchReqDTO);
        if (typeSearchReqDTO.getNotTree() != null && typeSearchReqDTO.getNotTree()) {
            return base;
        }
        List<CommodityType> list = listToTree(base.getData());
        base.setData(list);
        return base;

    }

    @ApiOperation(value = "新增分类",notes = "新增分类")
    @PostMapping("/addType")
    public ResponseBase<CommodityType> addType(@Valid @RequestBody CommodityTypeDTO commodityTypeDTO, BindingResult result, @RequestHeader String userName){
        this.checkValid(result);
        commodityTypeDTO.setUserName(userName);
        return commodityTypeClient.addType(commodityTypeDTO,userName);
    }

    @ApiOperation(value = "修改分类",notes = "修改分类")
    @PutMapping("/updateType")
    public ResponseBase<CommodityType> updateType(@Valid @RequestBody CommodityTypeDTO commodityTypeDTO, BindingResult result, @RequestHeader String userName){
        this.checkValid(result);
        commodityTypeDTO.setUserName(userName);
        return commodityTypeClient.updateType(commodityTypeDTO);
    }

    @ApiOperation(value = "分类或分组排序", notes = "分类或分组排序")
    @PostMapping("/sort")
    public ResponseBase sortType(@RequestBody TypeSortDTO typeSortDTO, BindingResult result) {
        this.checkValid(result);
        List<TypeSortReqDTO> list = typeSortDTO.getList();
        return commodityTypeClient.sortType(list);
    }

    @ApiOperation(value = "删除分组",notes = "删除分组")
    @DeleteMapping("/{id}")
    public ResponseBase<Boolean> deleteType(
            @PathVariable String id,@RequestBody MerCode merCode,
            @RequestHeader String userName){
        if(StringUtils.isEmpty(id) || StringUtils.isEmpty(merCode.getMerCode()) ){
            throw WarnException.builder().code(ErrorType.PARA_ERROR.getCode()).
                    tipMessage(ErrorType.PARA_ERROR.getMsg()).build();
        }
        return commodityTypeClient.deleteType(merCode.getMerCode(),id,userName);

    }

    @ApiOperation(value = "根据分类id集合获取分类列表（反向查找父类）", notes = "根据id集合获取分类列表（反向查找父类）")
    @PostMapping("/parent")
    public ResponseBase<Map<String, MultilevelTypeDTO>> getParentTypeListByCondition(@Valid @RequestBody CommodityTypeQueryDTO queryDTO, BindingResult result) {
        this.checkValid(result);

        return commodityTypeClient.getParentTypeListByCondition(queryDTO);

    }

    private List<CommodityType> listToTree(List<CommodityType> list) {
        List<CommodityType> treeList = new ArrayList<>();
        for (CommodityType tree : list) {
            //找到根
            if (tree.getParentId().equals("0")) {
                treeList.add(tree);
            }
            //找到子
            for (CommodityType treeNode : list) {
                if (treeNode.getParentId() .equals(tree.getId())) {
                    if (tree.getChildren() == null) {
                        tree.setChildren(new ArrayList<>());
                    }
                    tree.getChildren().add(treeNode);
                }
            }
        }
        return treeList;
    }
}
