package cn.hydee.middle.business.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * o2o平台门店信息
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DsOnlineStore implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商户编码
     */
    private String merCode;

    /**
     * o2o平台标记
     */
    private String platformCode;

    /**
     * o2o平台名
     */
    private String platformName;

    /**
     * 网店编码
     */
    private String onlineClientCode;

    /**
     * 网店名称
     */
    private String onlineClientName;

    /**
     * 门店编码
     */
    private String onlineStoreCode;

    /**
     * 门店名称
     */
    private String onlineStoreName;

    /**
     * 线下门店编码
     */
    private String organizationCode;

    /**
     * 线下门店名称（冗余字段）
     */
    private String organizationName;

    /**
     * 门店状态 （1：上线 0：下线 2 已删除）
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 末次修改时间
     */
    private Date modifyTime;

    /**
     * 营业状态（0：歇业 1：营业中 2：休息）
     */
    private Integer openStatus;

    /**
     * 门店地址
     */
    private String address;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 电话
     */
    private String contactPhone;

    /**
     * 城市
     */
    private String city;

    /**
     * 外部门店ID
     */
    private String outShopId;

    /**
     * 服务模式 B2C O2O
     */
    private String serviceMode;

    /**
     * 授权类型 1-商家应用授权，2-服务商应用授权
     */
    private Integer accessType;

    /**
     * 是否同步库存（0不同步，1同步）
     */
    @TableField(exist = false)
    private Integer syncStock;
    /**
     * 库存同步比例
     */
    @TableField(exist = false)
    private Integer syncStockRatio;
    /**
     * 是否同步价格（0不同步，1同步）
     */
    @TableField(exist = false)
    private Integer syncPrice;

    /**
     * 默认配置门店（0：不是 1：是）
     */
    private Integer defaultConfig;

    /**
     * storeoutCode
     */
    private String onlineClientOutCode;

    /**
     * B2C平台的O2O业务的线上门店（0：不是 1：是）
     */
    @TableField("inherit_B2C")
    private Integer inheritB2C;

    /**
     * 三方平台侧的门店ID
     */
    @TableField("platform_shop_id")
    private String platformShopId;

    /**
     * b2c 原o2o门店信息
     */
    @TableField(exist = false)
    private DsOnlineStore sourceO2oStore;


}
