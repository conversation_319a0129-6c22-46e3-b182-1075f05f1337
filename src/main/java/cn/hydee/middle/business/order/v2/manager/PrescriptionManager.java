package cn.hydee.middle.business.order.v2.manager;


import cn.hydee.middle.business.order.entity.OrderDetail;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.entity.OrderPrescription;
import cn.hydee.middle.business.order.service.IPrescriptionApproveService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 审批信息
 *
 * <AUTHOR>
 * @since 2024/03/25
 * */
@Component
public class PrescriptionManager {

    @Autowired
    private IPrescriptionApproveService prescriptionApproveService;

    /**
     * 发送处方审核通知给药事云
     */
    public void sendApproveNotice(OrderInfo orderInfo, Long orderNo, List<OrderDetail> orderDetails, List<OrderPrescription> orderPrescriptions) {
        //prescriptionApproveService.sendApproveNotice(orderInfo, orderNo, orderDetails, orderPrescriptions);

    }


}
