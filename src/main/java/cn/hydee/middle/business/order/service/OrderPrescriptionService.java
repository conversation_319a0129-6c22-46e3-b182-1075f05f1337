package cn.hydee.middle.business.order.service;

import cn.hydee.middle.business.order.domain.OrderInfoAllDomain;
import cn.hydee.middle.business.order.dto.PrescriptionAuditDto;
import cn.hydee.middle.business.order.dto.req.*;
import cn.hydee.middle.business.order.dto.req.prescription.PrescriptionCallbackReqDto;
import cn.hydee.middle.business.order.dto.rsp.ErpPrescriptionQryRespDto;
import cn.hydee.middle.business.order.dto.rsp.OrderPrescriptionRspForErpDto;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.entity.OrderPrescription;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 订单处方信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-10
 */
public interface OrderPrescriptionService extends IService<OrderPrescription> {

    /**
     * 接收审方结果通知
     * @param req
     */
    void receiveCheckResult(OrderPrescriptionReqDto req);

    /**
     * 未实现
     * @param req
     */
    void receiveCheckResultList(List<OrderPrescriptionReqDto> req);

    /**
     * 手动推送订单
     * @param merCode
     * @param orderNo
     */
    void reCheck(String merCode, Long orderNo);

    /**
     * 拉取图片
     * @param orderNo
     * @return
     */
    String resetPic(Long orderNo);

    /**
     * 取消审核
     * @Author: chufeng(2910)
     * @Date: 2020/9/18 16:31
     */
    void cancelCheck(String userId, String merCode, OrderPrescriptionCancelCheckReqDto req);

    /**
     * 修改订单处方图片
     * @param orderPrescription
     * @param addOrderPrescriptionReqDtoList
     * @param orderInfo
     */
    List<OrderPrescription> updateOrderPrescription(OrderPrescription orderPrescription, List<AddOrderPrescriptionReqDto> addOrderPrescriptionReqDtoList,
        OrderInfo orderInfo);

    /**
     * 获取处方单信息
     * @param req
     * @return
     */
    OrderPrescriptionRspForErpDto getPicStream(OrderHandleReqDto req);

    /**
     * 推送三方审方
     * @param orderInfoAllDomain
     * @return
     */
    boolean pushPrescriptionCheck(OrderInfoAllDomain orderInfoAllDomain);

    /**
     * 药事云审方回调
     * @param reqDto
     */
    void prescriptionCallback(PrescriptionCallbackReqDto reqDto);

    /**
     * 拉取平台T-1处方信息
     * @param pageSize
     */
    void syncPrescriptionFromPlat(Integer pageSize);

    /**
     * ERP查询T-2处方信息
     * @param reqDto
     * @return
     */
    IPage<ErpPrescriptionQryRespDto> getPrescriptionData(ErpPrescriptionQryReqDto reqDto);

    void syncPrescriptionByOrderNos(List<Long> orderNos);

    /**
     * 查询审方单药师云审方结果
     *
     * @param orderNo
     * @return void
     */
    PrescriptionAuditDto selectPresAuditResult(String orderNo);

}
