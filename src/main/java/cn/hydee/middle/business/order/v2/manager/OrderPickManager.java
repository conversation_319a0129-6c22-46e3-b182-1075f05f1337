package cn.hydee.middle.business.order.v2.manager;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hydee.middle.business.order.Enums.*;
import cn.hydee.middle.business.order.autopick.entity.OrderAutoPickInfo;
import cn.hydee.middle.business.order.configuration.ThirdPlatformMigrateConfig;
import cn.hydee.middle.business.order.domain.OrderDetailDomain;
import cn.hydee.middle.business.order.domain.OrderInfoAllDomain;
import cn.hydee.middle.business.order.dto.call.rider.CallRiderParamDto;
import cn.hydee.middle.business.order.dto.req.NoErpPickConfirmReqDto;
import cn.hydee.middle.business.order.dto.req.OrderHandleDeliveryOutReqDto;
import cn.hydee.middle.business.order.dto.req.OrderHandlePickConfirmReqDto;
import cn.hydee.middle.business.order.dto.req.OrderPickInfoReqDto;
import cn.hydee.middle.business.order.dto.rsp.AccountEmpResDTO;
import cn.hydee.middle.business.order.dto.rsp.OnlineStoreInfoRspDto;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.DsStoreDeliveryInfoResDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.SysEmployeeResDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.The3DsStoreResDTO;
import cn.hydee.middle.business.order.dto.the3.BaseParamForRequest;
import cn.hydee.middle.business.order.dto.the3.OrderPackingEndDTO;
import cn.hydee.middle.business.order.entity.*;
import cn.hydee.middle.business.order.feign.MiddleBaseInfoClient;
import cn.hydee.middle.business.order.feign.MiddleIdClient;
import cn.hydee.middle.business.order.feign.The3PlatformAdapterClient;
import cn.hydee.middle.business.order.interceptor.GlobalInterceptor;
import cn.hydee.middle.business.order.mapper.*;
import cn.hydee.middle.business.order.route.application.AllotApplicationService;
import cn.hydee.middle.business.order.route.application.command.AllotOrderCreateCommand;
import cn.hydee.middle.business.order.service.DeliveryFeeEconomizeRecordService;
import cn.hydee.middle.business.order.service.OrderDeliveryIdRelationService;
import cn.hydee.middle.business.order.service.OrderDeliveryService;
import cn.hydee.middle.business.order.service.OrderRiderCompareService;
import cn.hydee.middle.business.order.service.OrderRiderPollingService;
import cn.hydee.middle.business.order.service.async.HydeeEsSyncClientAsync;
import cn.hydee.middle.business.order.service.auto.OrderDeliveryRecordServiceAuto;
import cn.hydee.middle.business.order.service.baseinfo.DsOnlineClientService;
import cn.hydee.middle.business.order.service.baseinfo.DsStoreSoundConfigService;
import cn.hydee.middle.business.order.service.rocket.MessageProducerService;
import cn.hydee.middle.business.order.service.suport.OrderBasicService;
import cn.hydee.middle.business.order.service.thirdplatform.ThirdPlatformService;
import cn.hydee.middle.business.order.util.DateUtil;
import cn.hydee.middle.business.order.util.ExceptionUtil;
import cn.hydee.middle.business.order.util.NotifyRedisHelper;
import cn.hydee.middle.business.order.v2.dto.rider.OrderRiderTrailDTO;
import cn.hydee.middle.business.order.v2.feign.hems.HemsCommonClient;
import cn.hydee.middle.business.order.v2.feign.hems.HemsCommonClientB2C;
import cn.hydee.middle.business.order.v2.manager.base.*;
import cn.hydee.middle.business.order.v2.manager.rider.OrderRiderTrailHandlerManager;
import cn.hydee.middle.business.order.yxtadapter.domainservice.hdpos.HdPosGateway;
import cn.hydee.middle.business.order.yxtadapter.domainservice.mdm.MiddleBaseInfoClientAdapter;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.unified.model.HemsBaseData;
import cn.hydee.unified.model.base.BaseHemsResp;
import cn.hydee.unified.model.order.OrderPickCompleteReq;
import cn.hydee.unified.model.order.OrderRiderStatusSyncReq;
import cn.hydee.unified.model.rider.RiderOrderAddReq;
import cn.hydee.unified.model.rider.RiderOrderAddResp;
import cn.hydee.unified.model.rider.RiderPreOrderAddResp;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 拣货操作
 * <AUTHOR>
 * @since 2020/8/24 10:28
 */
@Slf4j
@Component
public class OrderPickManager {


    @Autowired
    private OrderBasicService orderBasicService;

    @Autowired
    private OrderPayInfoMapper orderPayInfoMapper;
    @Autowired
    private OrderDeliveryAddressMapper orderDeliveryAddressMapper;
    @Autowired
    private OrderBasicManager orderBasicManager;
    @Autowired
    private OrderDetailMapper orderDetailMapper;
    @Autowired
    private CityCodeMapper cityCodeMapper;
    @Autowired
    private HemsCommonClient hemsCommonClient;
    @Autowired
    private HemsCommonClientB2C hemsCommonClientB2C;
    @Autowired
    private OrderDeliveryRecordMapper orderDeliveryRecordMapper;
    @Autowired
    private OrderInfoMapper orderInfoMapper;
    @Autowired
    private OrderDeliveryRecordServiceAuto orderDeliveryRecordServiceAuto;
    @Autowired
    private DsDeliveryStoreManager dsDeliveryStoreManager;
    @Autowired
    private OrderHandlerManager orderHandlerManager;
    @Autowired
    private OrderDeliveryLogManager orderDeliveryLogManager;
    @Autowired
    private DeliveryOutManager deliveryOutManager;
    @Autowired
    private MiddleIdClient middleIdClient;
    @Autowired
    private MiddleBaseInfoClientAdapter middleBaseInfoClientAdapter;
    @Autowired
    private OrderEnterAccountManager orderEnterAccountManager;
    @Autowired
    private OrderDeliveryIdRelationService orderDeliveryIdRelationService;
    @Autowired
    private DeliveryManager deliveryManager;
    @Autowired
    private MessageProducerService messageProducerService;
    @Autowired
    private OrderRiderPollingService riderPollingService;
    @Autowired
    private OrderUpdateStoreDeliveryMatchManager updateStoreDeliveryMatchManager;
    @Autowired
    @Lazy
    private BaseInfoManager baseInfoManager;
    @Resource
    private DsStoreSoundConfigService dsStoreSoundConfigService;
    @Autowired
    private DeliverySourceMapMapper deliverySourceMapMapper;
    @Autowired
    private OrderRiderCompareService orderRiderCompareService;
    @Autowired
    private OrderDeliveryCompareMapper orderDeliveryCompareMapper;

    @Resource
    private The3PlatformAdapterClient the3PlatformAdapterClient;

    @Resource
    private BaseParamForRequest baseParamForRequest;

    @Autowired
    private InnerStoreDictionaryMapper innerStoreDictionaryMapper;
    @Autowired
    private HdPosGateway hdPosGateway;
    @Value("#{'${order.source.plat:,}'.split(',')}")
    private List<String> orderSourcePlatList;
    @Value("${callDelayMins:60}")
    private Integer callRiderMins;
    @Value("${autoAccountingFlag:true}")
    private Boolean autoAccountingFlag;
    @Resource
    private FreightOrderHandlerManager freightOrderHandlerManager;
    @Autowired
    private ThirdPlatformMigrateConfig thirdPlatformMigrateConfig;

    @Autowired
    private ThirdPlatformService thirdPlatformService;

    @Autowired
    private DsOnlineClientService onlineClientService;

    @Autowired
    private OrderDeliveryService orderDeliveryService;

    @Autowired
    private AllotApplicationService allocApplicationService;

    @Autowired
    private DeliveryFeeEconomizeRecordService deliveryFeeEconomizeRecordService;
    private static String riderErrMsg = "不支持该支付方式";

    private static String[] parsePatterns = {"yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm"};

    private static Pattern compile = Pattern.compile("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}");


    private HashMap<String, String> statusMap = new HashMap(){{
        put("11_32000","3");
        put("11_21050","3");
        put("11_41000","1");
        put("11_31000","1");
        put("11_33040","4");
        put("11_33060","5");
        put("11_90000","5");
        put("11_20020","6");
        put("11_20040","6");
        put("11_21051","6");
        put("11_20060","6");
        put("11_20030","0");
        put("24_24_1","1");
        put("24_24_5","2");
        put("24_24_7","3");
        put("24_24_8","4");
        put("24_24_9","5");
        put("24_24_10","6");
        put("24_24_15","7");
        put("27_27_2","1");
        put("27_27_4","2");
        put("27_27_8","5");
        put("27_27_9","6");
    }};

    public String getPickConfirmDeliveryType(OrderDeliveryRecord orderDeliveryRecord, String deliveryPlatform, Integer source) {
        if (DeliveryTypeEnum.checkSelfDistribution(orderDeliveryRecord.getDeliveryType())) {
            // 校验配送方式
            if (!DeliveryPlatformEnum.checkSelfDistribution(deliveryPlatform)) {
                throw ExceptionUtil.getWarnException(DsErrorType.PARAM_VALID_ERROR.getCode(), "配送方式不符");
            }
        } else if (DeliveryTypeEnum.checkBuyer(orderDeliveryRecord.getDeliveryType())) {
            return DeliveryPlatformEnum.BUYER.getName();
        } else if (DeliveryTypeEnum.checkExpress(orderDeliveryRecord.getDeliveryType())) {
            return DeliveryPlatformEnum.EXPRESS.getName();
        } else{
            if(DsConstants.INTEGER_TEN.compareTo(source) == 0 && DeliveryTypeEnum.checkPlatformRider(orderDeliveryRecord.getDeliveryType())) {
                return DeliveryPlatformEnum.PLAT_DELIVERY.getName();
            }
        }
        return null;
    }
    public void pickConfirmUpdate(SysEmployeeResDTO employeePickOperator, SysEmployeeResDTO sysEmployeeResDTO,
        OrderInfoAllDomain orderInfo, String pickInfo, Integer source) {
        // 获取平台的订单状态
//        OrderInformation orderInformation = orderBasicManager.getOrderInformation(orderInfo);
//        log.info("orderInformation orderstate:{}",orderInformation.getStatus());
//        Integer orderState = null;
//        String thirdOrderState = orderInfo.getThirdOrderState();
//        if (ObjectUtil.isNotEmpty(orderInformation)) {
//            orderState = transferO2OStatus(statusMap.get(orderInformation.getEcType() + "_" + orderInformation.getStatus()));
//            thirdOrderState = orderInformation.getStatus();
//        }
//
//        if (orderState == null || orderState < OrderStateEnum.UN_DELIVERY.getCode() )  {
//            orderState = OrderStateEnum.UN_DELIVERY.getCode();
//        }
        // 修改订单信息
        OrderInfo updateOrder = new OrderInfo();
//        updateOrder.setOrderState(orderState);
//        updateOrder.setThirdOrderState(thirdOrderState);
        updateOrder.setOrderState(OrderStateEnum.UN_DELIVERY.getCode());
        updateOrder.setPickerId(sysEmployeeResDTO.getEmpId());
        updateOrder.setPickerName(sysEmployeeResDTO.getEmpName());
        updateOrder.setPickOperatorId(employeePickOperator.getEmpCode());
        updateOrder.setPickOperatorName(employeePickOperator.getEmpName());
        updateOrder.setOrderNo(orderInfo.getOrderNo());
        if(orderInfo.getPickTime() == null) {
            updateOrder.setPickTime(new Date());
        }
        int num = orderBasicManager.updateOrder(updateOrder);
        if (num > 0) {
            // 订单日志
//            String extra = String.format("erpAdjustNo:%s", orderInfo.getErpAdjustNo());
            String action = OrderLogEnum.PICK_CONFIRM.getAction();
            if (DsConstants.INTEGER_TWO.equals(source)) {
                action = OrderLogEnum.PICK_CONFIRM_MOBILE.getAction();
            }
            HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, orderInfo.getMerCode(), orderInfo.getOrderNo(),OrderStateEnum.UN_DELIVERY.getCode(),orderInfo.getErpState(),
                    action,OrderLogEnum.getPickConfirmInfo(pickInfo),sysEmployeeResDTO);

//            if (updateOrder.getOrderState() > OrderStateEnum.UN_DELIVERY.getCode()) {
//                HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, orderInfo.getMerCode(), orderInfo.getOrderNo(),orderState,orderInfo.getErpState(),
//                        OrderLogEnum.SYNC_PLAT_ORDER_STATE.getAction(),"平台消息触发:"+OrderLogEnum.getSyncPlatOrderStateInfo(OrderStateEnum.UN_DELIVERY.getCode(), orderState),null);
//            }
            // 关联运费单特殊逻辑处理
            if(Objects.nonNull(orderInfo.getFreightOrderNo())){
                Boolean b = freightOrderHandlerManager.checkAutoEnterAccountFlag(orderInfo.getFreightOrderNo());
                OrderInfo freightOrder = new OrderInfo();
                if(b){
                    freightOrder.setErpState(ErpStateEnum.WAIT_SALE.getCode());
                }
                freightOrder.setPickerId(sysEmployeeResDTO.getEmpId());
                freightOrder.setPickerName(sysEmployeeResDTO.getEmpName());
                freightOrder.setPickOperatorId(employeePickOperator.getEmpCode());
                freightOrder.setPickOperatorName(employeePickOperator.getEmpName());
                freightOrder.setOrderNo(orderInfo.getFreightOrderNo());
                orderBasicManager.updateOrder(freightOrder);
            }
        }

        OrderInfo orderBaseInfo = orderBasicManager.getOrderBaseInfo(orderInfo.getOrderNo());
        NotifyRedisHelper.addDeliveryNotify(orderBaseInfo);
        NotifyRedisHelper.removePickNotify(orderBaseInfo);
    }

    public void pickConfirmUpdateForRobot(OrderInfo orderInfo, OrderAutoPickInfo orderAutoPickInfo,List<OrderPickInfoReqDto> pickInfoReqDtoList,String deliveryPlatform) {
        // 核心代码迁移自 cn.hydee.middle.business.order.v2.manager.OrderPickManager.pickConfirmUpdate
        // 修改订单信息
        OrderInfo updateOrder = new OrderInfo();
        updateOrder.setOrderState(OrderStateEnum.UN_DELIVERY.getCode());
        updateOrder.setPickerId(DsConstants.SYSTEM);
        updateOrder.setPickerName(DsConstants.SYSTEM);
        updateOrder.setPickOperatorId(orderAutoPickInfo.getPickOperatorCode());
        updateOrder.setPickOperatorName(orderAutoPickInfo.getPickOperatorName());
        updateOrder.setOrderNo(orderInfo.getOrderNo());
        if(orderInfo.getPickTime() == null) {
            updateOrder.setPickTime(new Date());
        }
        int num = orderBasicManager.updateOrder(updateOrder);
        if (num > 0) {
            // 订单日志
            String action = OrderLogEnum.PICK_CONFIRM_ROBOT_AUTO.getAction();
            List<String> pickStrList = pickInfoReqDtoList.stream().map((pickInfo) -> new StringBuilder().append("商品编码:").append(pickInfo.getErpCode()).append(",")
                    .append("批号:").append(pickInfo.getCommodityBatchNo()).append(",").append("数量:").append(pickInfo.getCount()).toString()).collect(Collectors.toList());
            // 更新操作
            String pickInfo = "配送方式：" + deliveryPlatform + "，明细：" + Strings.join(pickStrList,';');
            HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, orderInfo.getMerCode(), orderInfo.getOrderNo(),OrderStateEnum.UN_DELIVERY.getCode(),orderInfo.getErpState(),action,OrderLogEnum.getPickConfirmInfo(pickInfo),null);

        }

        OrderInfo orderBaseInfo = orderBasicManager.getOrderBaseInfo(orderInfo.getOrderNo());
        NotifyRedisHelper.addDeliveryNotify(orderBaseInfo);
        NotifyRedisHelper.removePickNotify(orderBaseInfo);
    }

    /**
     * 拣货后置处理
     * @Param:  * @param null
     * @Return: {@link null}
     * @Author: chufeng(2910)
     * @Date: 2020/8/24 10:40
     */
    public void orderPickBehindDeal(String userId,String merCode,OrderInfoAllDomain orderInfo, OnlineStoreInfoRspDto storeInfo, OrderHandlePickConfirmReqDto orderHandleReqDto, OrderDeliveryRecord orderDeliveryRecord){

        //订单更新异步消费
        messageProducerService.produceUpdateOrderMessage(Collections.singletonList(orderInfo.getOrderNo()), OrderUpdateCodeEnum.PICK.getCode());
        // 骑手配送平台字段
        if (DeliveryTypeEnum.checkSelfDistribution(orderDeliveryRecord.getDeliveryType())) {
            //增加是否修改门店校验
            OnlineStoreInfoRspDto newStoreInfo = storeInfo;
            String newStoreCode = orderInfo.getOnlineStoreCode();
            if(orderInfo.isUpdateStore()){
                DsOnlineStore dsOnlineStore = updateStoreDeliveryMatchManager.matchDeliveryStore(orderInfo);
                if(dsOnlineStore != null){
                    newStoreCode = dsOnlineStore.getOnlineStoreCode();
                    newStoreInfo = baseInfoManager.getOnlineStoreInfo(orderInfo.getMerCode(),
                            orderInfo.getThirdPlatformCode(), dsOnlineStore.getOnlineClientCode(), dsOnlineStore.getOnlineStoreCode());
                }
            }
            //收货信息解密
            orderDeliveryService.orderDeliveryAddressDecrypt(OrderChangeSelfDeliveryTypeEnum.SYSTEM_AUTO,orderInfo);
            // 自配送才呼叫骑手
            if (DeliveryPlatformEnum.checkThirdRider(orderHandleReqDto.getDeliveryPlatform())) {
                if (DsConstants.INTEGER_ONE.equals(newStoreInfo.getAutoCallRider())) {
                    //骑手比价处理
                    String comparePlat = orderRiderCompareService.riderCompare(orderInfo,storeInfo);
                    //增加循环呼叫逻辑
                    riderPollingService.validRiderPolling(orderInfo,newStoreInfo,comparePlat);
                    // 获取配送门店
                    DsStoreDeliveryInfoResDTO deliveryStore = dsDeliveryStoreManager.getDeliveryStoreByOnlineStoreId(
                            orderInfo.getMerCode(), newStoreCode, orderInfo.getThirdPlatformCode(),
                            StringUtils.isEmpty(comparePlat) ? orderHandleReqDto.getDeliveryPlatform() : comparePlat);
                    if (deliveryStore == null) {
                        log.error("OrderInfoService: pickConfirm deliveryStore null, orderNo:{}, storeCode:{}",
                                orderInfo.getOrderNo(), orderInfo.getOnlineStoreCode());
                        throw ExceptionUtil.getWarnException(DsErrorType.ORDER_RIDER_ERROR.getCode(), "获取不到配送门店信息");
                    }
                    if (DeliveryStateEnum.UN_CALL.getCode().equals(orderDeliveryRecord.getState())) {
                        try {
                            deliveryManager.callRider(orderInfo, deliveryStore, false, null,false);
                        } catch (Exception e) {
                            throw ExceptionUtil.getWarnException(DsErrorType.CALL_RIDER_ERROR.getCode(),
                                    "拣货完成，但骑手呼叫失败，请至待配送页面修改配送方式重新呼叫骑手！");
                        }
                    }
                }
                // ID1014628 接单呼叫骑手失败了 重新呼叫
                else if(orderDeliveryRecord.getState() < DeliveryStateEnum.UN_TAKE.getCode()
                        || orderDeliveryRecord.getState() > DeliveryStateEnum.COMPLETED.getCode()){
                    //骑手比价处理
                    String comparePlat = orderRiderCompareService.riderCompare(orderInfo,storeInfo);
                    //循环呼叫处理
                    riderPollingService.validRiderPolling(orderInfo,newStoreInfo,comparePlat);

                    DsStoreDeliveryInfoResDTO deliveryStore = dsDeliveryStoreManager.getDeliveryStoreByOnlineStoreId(
                            orderInfo.getMerCode(), newStoreCode, orderInfo.getThirdPlatformCode(),
                            StringUtils.isEmpty(comparePlat) ? orderHandleReqDto.getDeliveryPlatform() : comparePlat);
                    if (deliveryStore == null) {
                        log.error("OrderInfoService: pickConfirm deliveryStore null, orderNo:{}, storeCode:{}",
                                orderInfo.getOrderNo(), newStoreCode);
                        throw ExceptionUtil.getWarnException(DsErrorType.ORDER_RIDER_ERROR.getCode(), "获取不到配送门店信息");
                    }
                    try {
                        deliveryManager.callRider(orderInfo, deliveryStore, false, null,false);
                    } catch (Exception e) {
                        throw ExceptionUtil.getWarnException(DsErrorType.CALL_RIDER_ERROR.getCode(),
                                "拣货完成，但骑手呼叫失败，请至待配送页面修改配送方式重新呼叫骑手！");
                    }
                }
            } else if (DeliveryPlatformEnum.checkStaff(orderHandleReqDto.getDeliveryPlatform())) {
                //收货信息解密
                //orderDeliveryService.orderDeliveryAddressDecrypt(OrderChangeSelfDeliveryTypeEnum.SYSTEM_AUTO,orderInfo);
                // 修改deliveryRecord
                OrderDeliveryRecord odrUpdate = new OrderDeliveryRecord();
                odrUpdate.setOrderNo(orderInfo.getOrderNo());
                odrUpdate.setDeliveryPlatName(orderHandleReqDto.getDeliveryPlatform());
                odrUpdate.setState(DeliveryStateEnum.UN_PICK_UP.getCode());
                odrUpdate.setExceptionReason("");
                int num = orderDeliveryRecordMapper.updateDeliveryRecord(odrUpdate);
                if (num > 0) {
                    // 记录订单配送日志
                    orderDeliveryLogManager.saveOrderDeliveryLog(odrUpdate.getOrderNo(), orderDeliveryRecord.getState(),
                            odrUpdate.getDeliveryPlatName(), orderDeliveryRecord.getRiderName(),
                            orderDeliveryRecord.getRiderPhone(), "员工配送确认拣货");
                    // 通知下发事件  补偿处理
                    messageProducerService.produceUpdateOrderMessage(Collections.singletonList(orderInfo.getOrderNo()), OrderUpdateCodeEnum.CALL_RIDER.getCode());
                   if(orderInfo.getThirdOrderNo().matches("^(B\\d+)$")){
                       log.info("补偿单不通知平台 OrderInfoService: pickConfirm success , orderNo:{}, storeCode:{}, thirdOrderNo:{}",
                               orderInfo.getOrderNo(), newStoreCode, orderInfo.getThirdOrderNo());
                    return;
                   }
                    //同步第三方快递员信息，需要判断美团/饿百平台
                    if (PlatformCodeEnum.MEITUAN.getCode().equals(orderInfo.getThirdPlatformCode()) || PlatformCodeEnum.E_BAI.getCode().equals(orderInfo.getThirdPlatformCode())) {
                        ResponseBase<AccountEmpResDTO> responseBase = middleBaseInfoClientAdapter.queryEmployeeById(orderHandleReqDto.getPickOperatorId());
                        if (responseBase.checkSuccess() && responseBase.getData() != null) {

                            HemsBaseData baseData = hemsCommonClient.constructHemsBaseData(merCode, storeInfo.getPlatformCode(),
                                    storeInfo.getOnlineClientCode(), storeInfo.getSessionKey());
                            OrderRiderStatusSyncReq riderStatusSyncReq = new OrderRiderStatusSyncReq();
                            riderStatusSyncReq.setExpCmpCode(DeliveryPlatformEnum.STAFF.getCode());
                            riderStatusSyncReq.setRiderid(String.valueOf(orderInfo.getOrderNo()));
                            riderStatusSyncReq.setOlOrderNo(orderInfo.getThirdOrderNo());
                            riderStatusSyncReq.setOlShopId(orderInfo.getOnlineStoreCode());
                            riderStatusSyncReq.setRidername(orderHandleReqDto.getPickOperatorName());
                            riderStatusSyncReq.setRidertel(responseBase.getData().getMobile());
                            riderStatusSyncReq.setShopId(storeInfo.getOutShopId());
                            //riderStatusSyncReq.setStatus(String.valueOf(DeliveryStateEnum.DELIVERY_ON.getCode()));
                            boolean needSyncFlag = OrderRiderTrailHandlerManager.addParamAndNeedRequest(orderInfo,riderStatusSyncReq, OrderRiderTrailDTO.builder().build(),true);
                            if(needSyncFlag) {
                                hemsCommonClient.orderRiderStatusSync(riderStatusSyncReq, baseData);
                            }
                        } else {
                            log.warn("同步骑手状态失败，找不到拣货员. orderHandleReqDto:{}.", orderHandleReqDto);
                        }
                    }
                }
            } else if (DeliveryPlatformEnum.checkSjkdps(orderHandleReqDto.getDeliveryPlatform())) {
                // 修改deliveryRecord
                OrderDeliveryRecord odrUpdate = new OrderDeliveryRecord();
                odrUpdate.setOrderNo(orderInfo.getOrderNo());
                odrUpdate.setDeliveryPlatName(orderHandleReqDto.getDeliveryPlatform());
                odrUpdate.setState(DeliveryStateEnum.UN_PICK_UP.getCode());
                odrUpdate.setExceptionReason("");
                int num = orderDeliveryRecordMapper.updateDeliveryRecord(odrUpdate);
                if (num > 0) {
                    // 记录订单配送日志
                    orderDeliveryLogManager.saveOrderDeliveryLog(odrUpdate.getOrderNo(), orderDeliveryRecord.getState(),
                            odrUpdate.getDeliveryPlatName(), orderDeliveryRecord.getRiderName(),
                            orderDeliveryRecord.getRiderPhone(), "商家快递配送确认拣货");
                    // 通知下发事件  补偿处理
                    messageProducerService.produceUpdateOrderMessage(Collections.singletonList(orderInfo.getOrderNo()), OrderUpdateCodeEnum.CALL_RIDER.getCode());
                }
            }

        } else if (DeliveryTypeEnum.checkBuyerAndExpress(orderDeliveryRecord.getDeliveryType())) {
            // 修改deliveryRecord
            OrderDeliveryRecord odrUpdate = new OrderDeliveryRecord();
            odrUpdate.setOrderNo(orderInfo.getOrderNo());
            odrUpdate.setDeliveryPlatName(orderHandleReqDto.getDeliveryPlatform());
            int num = orderDeliveryRecordMapper.updateDeliveryRecord(odrUpdate);
            if (num > 0) {
                // 记录订单配送日志
                orderDeliveryLogManager.saveOrderDeliveryLog(odrUpdate.getOrderNo(), orderDeliveryRecord.getState(),
                        odrUpdate.getDeliveryPlatName(), orderDeliveryRecord.getRiderName(),
                        orderDeliveryRecord.getRiderPhone(), "自提与快递确认拣货");
            }

            //京东健康-到店自提 拣货后呼叫运力，调用配送出库接口
            if(PlatformCodeEnum.JD_HEALTH.getCode().equals(orderInfo.getThirdPlatformCode()) && DeliveryTypeEnum.BUYER_SELF.getCode().equals(orderDeliveryRecord.getDeliveryType())){
                //订单发货
                orderDelivery(userId,merCode,orderInfo,orderDeliveryRecord);
            }

        } else if(DeliveryTypeEnum.checkPlatformRider(orderDeliveryRecord.getDeliveryType())){
            //京东健康且为平台配送，拣货后呼叫运力，调用配送出库接口
            if (PlatformCodeEnum.JD_HEALTH.getCode().equals(orderInfo.getThirdPlatformCode())) {
                //订单发货
                orderDelivery(userId,merCode,orderInfo,orderDeliveryRecord);
            }
            // 2023-11-09 新增逻辑平台配送拣货复合 判断物流单状态 存在门店未拣货骑手取货送达情况
            if(orderDeliveryRecord.getState().equals(DeliveryStateEnum.COMPLETED.getCode())){
                OrderInfoAllDomain newOrderInfo = orderBasicManager.checkOrderAllLock(orderHandleReqDto.getOrderNo(), true);
                OrderInfo orderUpdate = new OrderInfo();
                orderUpdate.setOrderNo(orderInfo.getOrderNo());
                orderUpdate.setCompleteTime(new Date());
                orderUpdate.setOrderState(OrderStateEnum.COMPLETED.getCode());
                orderUpdate.setLockFlag(OrderLockFlagEnum.NOT_LOCK.getCode());
                if (orderInfo.getPickTime() == null) {
                    orderUpdate.setPickTime(new Date());
                }
                //订单完成修改成未催促
                orderUpdate.setRemindFlag(DsConstants.INTEGER_ZERO);
                log.info("超时拣货复合更新-三方单号：{}，旧status：{}", newOrderInfo.getThirdOrderNo(), newOrderInfo.getOrderState());
                int num = orderInfoMapper.updateOrderWithState(orderUpdate, newOrderInfo.getOrderState());
                if (num > 0) {
                    // 订单日志
                    orderBasicService.saveOrderInfoLog(orderInfo, OrderStateEnum.COMPLETED.getCode(),DsConstants.SYSTEM,
                            DsConstants.DELIVERY_SUCCESS, "超时拣货复合",orderInfo.getErpState(),"超时拣货复合--未拣货复核平台骑手已经送达");
                    NotifyRedisHelper.removeAllNotify(orderInfo,storeInfo);
                    SysEmployeeResDTO sysEmployeeResDTO = baseInfoManager.getEmployeeInfoV2(userId);
                    HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, orderInfo.getMerCode(),
                            orderInfo.getOrderNo(),OrderStateEnum.COMPLETED.getCode(),orderInfo.getErpState(),OrderLogEnum.PICK_CONFIRM.getAction(),OrderLogEnum.getPickConfirmInfo("超时拣货复合,未拣货复核平台骑手已经送达"),sysEmployeeResDTO);
                    // 关联运费单特殊逻辑处理
                    if(Objects.nonNull(orderInfo.getFreightOrderNo())){
                        OrderInfo freightOrder = new OrderInfo();
                        freightOrder.setErpState(ErpStateEnum.WAIT_SALE.getCode());
                        freightOrder.setOrderNo(orderInfo.getFreightOrderNo());
                        orderBasicManager.updateOrder(freightOrder);
                    }

                }
            }
        }
        //创建调拨单
        try{
            if(!orderInfo.getOrganizationCode().equals(orderInfo.getSourceOrganizationCode())
                    && StringUtils.isEmpty(orderInfo.getOrganizationCode()) && StringUtils.isEmpty(orderInfo.getSourceOrganizationCode())){
                List<OrderPickInfo> orderPickInfos = BeanUtil.copyToList(orderHandleReqDto.getPickDetailList(), OrderPickInfo.class);
                //查询新的正单获取拣货员信息，不用快照
                OrderInfo newOrderInfo = orderInfoMapper.selectOne(new QueryWrapper<OrderInfo>().lambda().eq(OrderInfo::getOrderNo, orderInfo.getOrderNo()));
                allocApplicationService.createOrderAllotOrder(new AllotOrderCreateCommand(newOrderInfo, orderInfo.getOrderDetailList(), orderPickInfos));
            }
        }catch (Exception e){
            log.info("拣货完成后创建调拨单失败：{}",orderInfo.getOrderNo(),e);
        }

        if(autoAccountingFlag){
            //超时拣货复核后海典POS自动下账
            try {
                hdPosGateway.orderAccounting(orderInfo.getOrderNo().toString());
            }catch (Exception e){
                log.info("orderNo:{} 海典POS自动下账异常{}",orderInfo.getOrderNo(),e);
            }
        }

        // 拣货后打印
        List<OrderDetailDomain> detailDomains =
                orderDetailMapper.selectDetailDomainListByOrderNo(orderInfo.getOrderNo());
        // 打印通知内容
        if (DsConstants.INTEGER_ONE.equals(storeInfo.getAutoPrintPick())) {
            orderHandlerManager.producePrintContentPick(orderInfo, detailDomains, storeInfo, DsConstants.INTEGER_TWO, userId);
        }

        //京东健康不调用拣货接口
        if(PlatformCodeEnum.JD_HEALTH.getCode().equals(orderInfo.getThirdPlatformCode())){
            return;
        }

        // 发送拣货完成通知
		HemsBaseData baseData = hemsCommonClient.constructHemsBaseData(merCode, storeInfo.getPlatformCode(),
				storeInfo.getOnlineClientCode(), storeInfo.getSessionKey());
		OrderPickCompleteReq pickComplete = new OrderPickCompleteReq();
		pickComplete.setDeliveryType(orderDeliveryRecord.getDeliveryType());
		pickComplete.setOlOrderNo(orderInfo.getThirdOrderNo());
		pickComplete.setOlShopId(orderInfo.getOnlineStoreCode());
        pickComplete.setShopId(storeInfo.getOutShopId());

        try {
        	// 美团订单且配送方式为美团平台配送且订单状态为待拣货、待配送
        	// 进行拣货完成通知时，如果通知失败，过3分钟后再自动调用一次，调用4次都不成功则不再调用
//        	log.info("pick complete message start, olorderno:{},thirdPlatformCode:{},deliveryType:{}",
//        			orderHandleReqDto.getOrderNo(),orderInfo.getThirdPlatformCode(),orderDeliveryRecord.getDeliveryType());
    		if ((PlatformCodeEnum.MEITUAN.getCode().equals(orderInfo.getThirdPlatformCode()) || PlatformCodeEnum.TY_O2O.getCode().equals(orderInfo.getThirdPlatformCode()))
        			&& DeliveryTypeEnum.checkPlatformRider(orderDeliveryRecord.getDeliveryType())) {
//        		log.info("meituan order pick complete message notifaction start, olorderno:{}", orderHandleReqDto.getOrderNo());
        		hemsCommonClient.pickCompleteMeiTuan(orderHandleReqDto.getOrderNo(), pickComplete , baseData);
        	} else if (PlatformCodeEnum.belongB2COnlineStore(orderInfo.getThirdPlatformCode())){
                // 【【万和】【天猫同城呼叫运力】：处方订单，需要审方后再呼叫运力】 https://www.tapd.cn/61969829/prong/stories/view/1161969829001030641
    		    if(OrderUpdateHandlerManager.needCallRider(orderInfo.getTopHold(),orderDeliveryRecord.getState(),orderInfo.getOrderState())) {
                    hemsCommonClientB2C.pickCompleteB2C(orderHandleReqDto.getOrderNo(), pickComplete, baseData);
                }
            }else if(PlatformCodeEnum.DOUDIAN.getCode().equals(orderInfo.getThirdPlatformCode())
                    && DeliveryTypeEnum.checkDouDian(orderDeliveryRecord.getDeliveryType())){
                DeliveryPlatformEnum byName = DeliveryPlatformEnum.getByName(orderHandleReqDto.getDeliveryPlatform());
                OrderDeliveryRecord newOrderDeliveryRecord = orderDeliveryRecordMapper.selectByOrderNo(orderInfo.getOrderNo());
                OrderPackingEndDTO dto=new OrderPackingEndDTO();
                dto.setLogisticsCompanyCode(byName.getCode());
                dto.setLogisticsNo(Objects.isNull(newOrderDeliveryRecord.getRiderOrderNo())?"0":newOrderDeliveryRecord.getRiderOrderNo());
                dto.setDeliveryType(orderDeliveryRecord.getDeliveryType());
                dto.setOrderNo(orderInfo.getThirdOrderNo());
                if (thirdPlatformMigrateConfig.isMigrated(orderInfo.getThirdPlatformCode(), orderInfo.getOnlineStoreCode())) {
                    The3DsStoreResDTO storeAccess = onlineClientService.getStoreAccessByStoreCode(orderInfo.getMerCode(),orderInfo.getThirdPlatformCode(),orderInfo.getOnlineStoreCode(),orderInfo.getClientCode());
                    if(DeliveryTypeEnum.PLAT.getCode().equals(orderDeliveryRecord.getDeliveryType())) {
                        thirdPlatformService.packingEnd(storeAccess, dto);
                    }else {
                        OrderLogisticsAddRequest logisticsAddRequest = new OrderLogisticsAddRequest();
                        logisticsAddRequest.setDeliveryType(orderDeliveryRecord.getDeliveryType());
                        logisticsAddRequest.setThirdOrderNo(orderInfo.getThirdOrderNo());
                        logisticsAddRequest.setLogisticsCompanyCode(byName.getCode());
                        logisticsAddRequest.setLogisticsNo(Optional.ofNullable(newOrderDeliveryRecord.getRiderOrderNo()).orElse("-1"));
                        thirdPlatformService.orderShip(storeAccess, logisticsAddRequest);
                    }
                } else {
                    baseParamForRequest.loadMerchantParam(orderInfo.getMerCode(),orderInfo.getThirdPlatformCode(),orderInfo.getOnlineStoreCode(),orderInfo.getClientCode(),dto);
                    the3PlatformAdapterClient.packingEnd(dto);
                }
            }
            else {
        		// 其他平台
        		hemsCommonClient.pickComplete(pickComplete, baseData);
        	}
        } catch (Exception e) {
            log.info("pickConfirm: pickCompleteNotify error", e);
        }
    }


    /**
     * 京东健康呼叫运力（配送出库）
     */
    public void orderDelivery(String userId,String merCode,OrderInfo orderInfo,OrderDeliveryRecord orderDeliveryRecord){
        // 通知第三方,自提就通知第三方配送完成（其中到店自取确认妥投,美团和饿百平台不做处理）
        OrderHandleDeliveryOutReqDto orderHandle = new OrderHandleDeliveryOutReqDto();
        deliveryOutManager.deliveryOutNotifyThirdPlatForm(userId, merCode, orderDeliveryRecord.getDeliveryType(),
                orderHandle, orderInfo, orderDeliveryRecord);
    }

    public void noErpOrderPickBehindDeal(String userId,String merCode, OrderInfoAllDomain orderInfo, OnlineStoreInfoRspDto storeInfo, NoErpPickConfirmReqDto orderHandleReqDto, OrderDeliveryRecord orderDeliveryRecord){
        GlobalInterceptor.tObject.set(orderInfo);

        //订单更新异步消费
        messageProducerService.produceUpdateOrderMessage(Collections.singletonList(orderInfo.getOrderNo()), OrderUpdateCodeEnum.PICK.getCode());
        // 骑手配送平台字段
        if (DeliveryTypeEnum.checkSelfDistribution(orderDeliveryRecord.getDeliveryType())) {
            //增加是否修改门店校验
            OnlineStoreInfoRspDto newStoreInfo = storeInfo;
            String newStoreCode = orderInfo.getOnlineStoreCode();
            if(orderInfo.isUpdateStore()){
                DsOnlineStore dsOnlineStore = updateStoreDeliveryMatchManager.matchDeliveryStore(orderInfo);
                if(dsOnlineStore != null){
                    newStoreCode = dsOnlineStore.getOnlineStoreCode();
                    try {
                        newStoreInfo = baseInfoManager.getOnlineStoreInfo(orderInfo.getMerCode(),
                                orderInfo.getThirdPlatformCode(), orderInfo.getClientCode(), dsOnlineStore.getOnlineStoreCode());
                    }catch (Exception e){
                        log.info("pickconfirm match online store failure after order update store,orderNo: {}, newStoreCode: {}",orderInfo.getOrderNo(),dsOnlineStore.getOnlineStoreCode());
                    }
                }
            }
            // 自配送才呼叫骑手
            if (DeliveryPlatformEnum.checkThirdRider(orderHandleReqDto.getDeliveryPlatform())) {
                if (DsConstants.INTEGER_ONE.equals(newStoreInfo.getAutoCallRider())) {
                    //骑手比价处理
                    String comparePlat = orderRiderCompareService.riderCompare(orderInfo,storeInfo);
                    //骑手循环呼叫处理
                    riderPollingService.validRiderPolling(orderInfo,newStoreInfo,comparePlat);
                    // 获取配送门店
                    DsStoreDeliveryInfoResDTO deliveryStore = dsDeliveryStoreManager.getDeliveryStoreByOnlineStoreId(
                            orderInfo.getMerCode(), newStoreCode, orderInfo.getThirdPlatformCode(),
                            StringUtils.isEmpty(comparePlat) ? orderHandleReqDto.getDeliveryPlatform() : comparePlat);
                    if (deliveryStore == null) {
                        log.error("OrderInfoService: pickConfirm deliveryStore null, orderNo:{}, storeCode:{}",
                                orderInfo.getOrderNo(), newStoreCode);
                        throw ExceptionUtil.getWarnException(DsErrorType.ORDER_RIDER_ERROR.getCode(), "获取不到配送门店信息");
                    }
                    if (DeliveryStateEnum.UN_CALL.getCode().equals(orderDeliveryRecord.getState())) {
                        try {
                            deliveryManager.callRider(orderInfo, deliveryStore, false, null,false);
                        } catch (Exception e) {
                            throw ExceptionUtil.getWarnException(DsErrorType.CALL_RIDER_ERROR.getCode(),
                                    "拣货完成，但骑手呼叫失败，请至待配送页面修改配送方式重新呼叫骑手！");
                        }
                    }
                }
            } else if (DeliveryPlatformEnum.checkStaff(orderHandleReqDto.getDeliveryPlatform())) {
                // 修改deliveryRecord
                OrderDeliveryRecord odrUpdate = new OrderDeliveryRecord();
                odrUpdate.setOrderNo(orderInfo.getOrderNo());
                odrUpdate.setDeliveryPlatName(orderHandleReqDto.getDeliveryPlatform());
                int result = orderDeliveryRecordMapper.updateDeliveryRecord(odrUpdate);
                if (result > 0) {
                    // 记录订单配送日志
                    orderDeliveryLogManager.saveOrderDeliveryLog(odrUpdate.getOrderNo(), orderDeliveryRecord.getState(),
                            odrUpdate.getDeliveryPlatName(), orderDeliveryRecord.getRiderName(),
                            orderDeliveryRecord.getRiderPhone(), "员工配送确认拣货");
                    // 通知下发事件  补偿处理
                    messageProducerService.produceUpdateOrderMessage(Collections.singletonList(orderInfo.getOrderNo()), OrderUpdateCodeEnum.CALL_RIDER.getCode());
                    //同步第三方快递员信息，需要判断美团/饿百平台
                    if (PlatformCodeEnum.MEITUAN.getCode().equals(orderInfo.getThirdPlatformCode()) || PlatformCodeEnum.E_BAI.getCode().equals(orderInfo.getThirdPlatformCode())) {
                        ResponseBase<AccountEmpResDTO> responseBase = middleBaseInfoClientAdapter.queryEmployeeById(orderHandleReqDto.getPickOperatorId());
                        if (responseBase.checkSuccess() && responseBase.getData() != null) {

                            HemsBaseData baseData = hemsCommonClient.constructHemsBaseData(merCode, storeInfo.getPlatformCode(),
                                    storeInfo.getOnlineClientCode(), storeInfo.getSessionKey());
                            OrderRiderStatusSyncReq riderStatusSyncReq = new OrderRiderStatusSyncReq();
                            riderStatusSyncReq.setExpCmpCode(DeliveryPlatformEnum.STAFF.getCode());
                            riderStatusSyncReq.setRiderid(String.valueOf(orderInfo.getOrderNo()));
                            riderStatusSyncReq.setOlOrderNo(orderInfo.getThirdOrderNo());
                            riderStatusSyncReq.setOlShopId(orderInfo.getOnlineStoreCode());
                            riderStatusSyncReq.setRidername(orderHandleReqDto.getPickOperatorName());
                            riderStatusSyncReq.setRidertel(responseBase.getData().getMobile());
                            riderStatusSyncReq.setShopId(storeInfo.getOutShopId());
                            //riderStatusSyncReq.setStatus(String.valueOf(DeliveryStateEnum.DELIVERY_ON.getCode()));
                            boolean needSyncFlag = OrderRiderTrailHandlerManager.addParamAndNeedRequest(orderInfo,riderStatusSyncReq, OrderRiderTrailDTO.builder().build(),true);
                            if(needSyncFlag) {
                                hemsCommonClient.orderRiderStatusSync(riderStatusSyncReq, baseData);
                            }
                        } else {
                            log.warn("同步骑手状态失败，找不到拣货员. orderHandleReqDto:{}.", orderHandleReqDto);
                        }
                    }
                }
            }else if (DeliveryPlatformEnum.checkSjkdps(orderHandleReqDto.getDeliveryPlatform())) {
                // 修改deliveryRecord
                OrderDeliveryRecord odrUpdate = new OrderDeliveryRecord();
                odrUpdate.setOrderNo(orderInfo.getOrderNo());
                odrUpdate.setDeliveryPlatName(orderHandleReqDto.getDeliveryPlatform());
                int result = orderDeliveryRecordMapper.updateDeliveryRecord(odrUpdate);
                if (result > 0) {
                    // 记录订单配送日志
                    orderDeliveryLogManager.saveOrderDeliveryLog(odrUpdate.getOrderNo(), orderDeliveryRecord.getState(),
                            odrUpdate.getDeliveryPlatName(), orderDeliveryRecord.getRiderName(),
                            orderDeliveryRecord.getRiderPhone(), "商家快递配送确认拣货");
                    // 通知下发事件  补偿处理
                    messageProducerService.produceUpdateOrderMessage(Collections.singletonList(orderInfo.getOrderNo()), OrderUpdateCodeEnum.CALL_RIDER.getCode());
                }
            }
        } else if (DeliveryTypeEnum.checkBuyerAndExpress(orderDeliveryRecord.getDeliveryType())) {
            // 修改deliveryRecord
            OrderDeliveryRecord odrUpdate = new OrderDeliveryRecord();
            odrUpdate.setOrderNo(orderInfo.getOrderNo());
            odrUpdate.setDeliveryPlatName(orderHandleReqDto.getDeliveryPlatform());
            int result = orderDeliveryRecordMapper.updateDeliveryRecord(odrUpdate);
            if (result > 0) {
                // 记录订单配送日志
                orderDeliveryLogManager.saveOrderDeliveryLog(odrUpdate.getOrderNo(), orderDeliveryRecord.getState(),
                        odrUpdate.getDeliveryPlatName(), orderDeliveryRecord.getRiderName(),
                        orderDeliveryRecord.getRiderPhone(), "自提与快递确认拣货");
            }

            //京东健康-到店自提 拣货后呼叫运力，调用配送出库接口
            if(PlatformCodeEnum.JD_HEALTH.getCode().equals(orderInfo.getThirdPlatformCode()) && DeliveryTypeEnum.BUYER_SELF.getCode().equals(orderDeliveryRecord.getDeliveryType())){
                //订单发货
                orderDelivery(userId,merCode,orderInfo,orderDeliveryRecord);
            }
        } else if(DeliveryTypeEnum.checkPlatformRider(orderDeliveryRecord.getDeliveryType())){
            //京东健康且为平台配送，拣货后呼叫运力，调用配送出库接口
            if (PlatformCodeEnum.JD_HEALTH.getCode().equals(orderInfo.getThirdPlatformCode())) {
                //订单发货
                orderDelivery(userId,merCode,orderInfo,orderDeliveryRecord);
            }
        }

        // 拣货后打印
        List<OrderDetailDomain> detailDomains =
                orderDetailMapper.selectDetailDomainListByOrderNo(orderInfo.getOrderNo());
        // 打印通知内容
        if (DsConstants.INTEGER_ONE.equals(storeInfo.getAutoPrintPick())) {
            orderHandlerManager.producePrintContentPick(orderInfo, detailDomains, storeInfo, DsConstants.INTEGER_TWO, userId);
        }

        //京东健康不调用拣货接口
        if(PlatformCodeEnum.JD_HEALTH.getCode().equals(orderInfo.getThirdPlatformCode())){
            return;
        }

        // 发送拣货完成通知
		HemsBaseData baseData = hemsCommonClient.constructHemsBaseData(merCode, storeInfo.getPlatformCode(),
				storeInfo.getOnlineClientCode(), storeInfo.getSessionKey());
		OrderPickCompleteReq pickComplete = new OrderPickCompleteReq();
		pickComplete.setDeliveryType(orderDeliveryRecord.getDeliveryType());
		pickComplete.setOlOrderNo(orderInfo.getThirdOrderNo());
		pickComplete.setOlShopId(orderInfo.getOnlineStoreCode());
        pickComplete.setShopId(storeInfo.getOutShopId());
		
        try {
        	// 美团订单且配送方式为美团平台配送且订单状态为待拣货、待配送
        	// 进行拣货完成通知时，如果通知失败，过3分钟后再自动调用一次，调用4次都不成功则不再调用
        	log.info("pick complete message start, olorderno:{},thirdPlatformCode:{},deliveryType:{}", 
        			orderHandleReqDto.getOrderNo(),orderInfo.getThirdPlatformCode(),orderDeliveryRecord.getDeliveryType());
    		if ((PlatformCodeEnum.MEITUAN.getCode().equals(orderInfo.getThirdPlatformCode()) || PlatformCodeEnum.TY_O2O.getCode().equals(orderInfo.getThirdPlatformCode()))
                    && DeliveryTypeEnum.checkPlatformRider(orderDeliveryRecord.getDeliveryType())) {
        		log.info("meituan order pick complete message notifaction start, olorderno:{}", orderHandleReqDto.getOrderNo());
        		hemsCommonClient.pickCompleteMeiTuan(orderHandleReqDto.getOrderNo(), pickComplete , baseData);
        	}else {
        		// 其他平台
        		hemsCommonClient.pickComplete(pickComplete, baseData);
        	}
        } catch (Exception e) {
            log.error("pickConfirm: pickCompleteNotify error", e);
        }
    }

    /**
    * @Description: 构建呼叫骑手参数
    * @Param: [callRiderParamDto]
    * @return: cn.hydee.unified.model.rider.RiderOrderAddReq
    * @Author: syuson
    * @Date: 2021-12-7
    */
    public void buildCallRider(CallRiderParamDto callRiderParamDto) {
        OrderInfo orderInfo = callRiderParamDto.getOrderInfo();
        DsStoreDeliveryInfoResDTO deliveryStore = callRiderParamDto.getDeliveryStore();
        Boolean beRepeat = callRiderParamDto.getBeRepeat();
        String modifyDeliveryPlatformName = callRiderParamDto.getModifyDeliveryPlatformName();
        boolean appointCallRightNowFlag = callRiderParamDto.isAppointCallRightNowFlag();

        OrderPayInfo orderPayInfo = orderPayInfoMapper.selectByOrderNo(orderInfo.getOrderNo());
        OrderDeliveryAddress orderDeliveryAddress = orderDeliveryAddressMapper.selectByOrderNo(orderInfo.getOrderNo());
        OrderDeliveryRecord orderDeliveryRecord =
                orderBasicManager.getOrderDeliveryRecordWithCheck(orderInfo.getOrderNo());
        DeliverySourceMap deliverySourceMap = deliverySourceMapMapper.queryByCode(orderInfo.getThirdPlatformCode(),deliveryStore.getPlatformCode());
        List<OrderDetail> orderDetailList = orderDetailMapper.selectListByOrderNo(orderInfo.getOrderNo());
        String cityCodeStr = "12345";
        CityCode cityCode = cityCodeMapper.selectByCity(deliveryStore.getDeliverStoreCity());
        if (cityCode != null) {
            cityCodeStr = cityCode.getCode();
        }

        // 1 order
        Date now = new Date();
		RiderOrderAddReq addReq = new RiderOrderAddReq();
        RiderOrderAddReq.Order riderOrder = new RiderOrderAddReq.Order();
        // 主要单号
//        riderOrder.setDeliveryId(Long.valueOf(orderInfo.getThirdOrderNo()));
        riderOrder.setDeliveryId(middleIdClient.getId(1).get(0));
        //保存订单配送关系
        OrderDeliveryIdRelation orderDeliveryIdRelation = new OrderDeliveryIdRelation();
        orderDeliveryIdRelation.setDeliveryId(riderOrder.getDeliveryId());
        orderDeliveryIdRelation.setOrderNo(orderInfo.getOrderNo());
        orderDeliveryIdRelation.setMerCode(orderInfo.getMerCode());
        int num = orderDeliveryIdRelationService.saveOrderDeliveryRelation(orderDeliveryIdRelation);
        if(num < 0){
            log.error("save orderDeliveryIdRelation error orderNo:{}",orderInfo.getOrderNo());
        }

        riderOrder.setDeliveryServiceCode(deliveryStore.getDefaultServiceCode());
        // 物流单请求,由系统订单号,更改为平台订单号
        riderOrder.setOrderId(orderInfo.getOrderNo().toString());
        if(OrderTypeEnum.ROBOT_AUTO_PICK.getCode().equals(orderInfo.getOrderType())){
            // 机器自动拣货订单将系统配送单号生成方式优化为：随机两位数字 + 平台订单号 = 系统配送单号
            riderOrder.setOrderId(RandomUtil.randomInt(10,100) + orderInfo.getThirdOrderNo());
        }
        riderOrder.setShopId(deliveryStore.getDeliveryStoreCode());
        riderOrder.setCityCode(cityCodeStr);
        riderOrder.setOrderType(DsConstants.STRING_ONE);
        // 延迟呼叫 预约单呼叫类型为1：立即呼叫
        if (isAppointDelivery(orderInfo) && !appointCallRightNowFlag) {
            riderOrder.setOrderType(DsConstants.STRING_THREE);
            riderOrder.setExpectTime(validate(orderInfo.getDeliveryTimeDesc(), orderInfo));
        }
        riderOrder.setOrderAddTime(DateUtil.parseDateToStr(now, DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS));
        riderOrder.setOrderTotalAmount(orderPayInfo.getTotalAmount().toString());
        riderOrder.setOrderActualAmount(orderPayInfo.getBuyerActualAmount().toString());
        riderOrder.setOrderWeight("0.1");
        riderOrder.setIsInvoiced(0);
        riderOrder.setOrderPaymentStatus(Integer.parseInt(orderPayInfo.getPayStatus()));
        riderOrder.setIsPrepay(0);
        riderOrder.setOrderPaymentMethod(Integer.parseInt(orderPayInfo.getPayType()));
        riderOrder.setIsAgentPayment(0);
        riderOrder.setRequirePaymentPay(0);
        boolean needRepeat = beRepeat;
        if (orderDeliveryRecord.getCancelFlag() == 1) {
            needRepeat = true;
        }

//        List<OrderDeliveryLog> orderDeliveryLogs = orderDeliveryLogMapper.selectList(new QueryWrapper<OrderDeliveryLog>().lambda().eq(OrderDeliveryLog::getOrderNo, orderInfo.getOrderNo())
//                .eq(OrderDeliveryLog::getDeliveryPlatName, DeliveryPlatformEnum.DADA.getName()));
//        if (!CollectionUtils.isEmpty(orderDeliveryLogs)){
//            needRepeat = false;
//        }

        if (DeliveryPlatformEnum.DADA.getName().equals(modifyDeliveryPlatformName)){
            needRepeat = false;
        }

        riderOrder.setIsRepeatOrder(needRepeat);
        riderOrder.setSerialNumber(orderInfo.getDayNum());
        //是否保价 0：非保价；1：保价 顺丰同城必填
        Integer isInsured = 0;
        //是否是专人直送订单 0：否；1：是 顺丰同城必填
        Integer isPersonDirect = 0;
        /*if (modifyDeliveryPlatformName == null) {
            if (DeliveryPlatformEnum.SFTC.getName().equals(orderDeliveryRecord.getDeliveryPlatName())) {
                //顺丰同城配送必填
                isInsured = 1;
                isPersonDirect = 1;
            }
        } else {
            if (DeliveryPlatformEnum.SFTC.getName().equals(modifyDeliveryPlatformName)) {
                //是否保价 0：非保价；1：保价 顺丰同城必填
                isInsured = 1;
                //是否是专人直送订单 0：否；1：是 顺丰同城必填
                isPersonDirect = 1;
            }
        }*/
        riderOrder.setIsInsured(isInsured);
        riderOrder.setIsPersonDirect(isPersonDirect);
        //保价金额 单位 元
        riderOrder.setDeclaredValue("0");
        //糟糕必传0
        riderOrder.setPayTypeCode(0);
        if(deliverySourceMap != null){
            riderOrder.setOrderSource(deliverySourceMap.getOrderSource());
        }
        if(PlatformCodeEnum.MEITUAN.getCode().equals(orderInfo.getThirdPlatformCode())
                && DeliveryPlatformEnum.MEITUAN_RIDER.getCode().equals(deliveryStore.getPlatformCode())){
            riderOrder.setOrderSourceOrderId(orderInfo.getThirdOrderNo());
        }
        // 正式呼叫三方骑手时，将O2O订单的买家备注信息，传给接口中台的【order_remark】字段。https://www.tapd.cn/61969829/prong/stories/view/1161969829001057546
        riderOrder.setOrderRemark(!StringUtils.isEmpty(orderInfo.getBuyerRemark()) ? orderInfo.getBuyerRemark() : orderInfo.getBuyerMessage());

        addReq.setOrder(riderOrder);

        // 2
        RiderOrderAddReq.Transport transport = new RiderOrderAddReq.Transport();
        transport.setName(deliveryStore.getDeliveryStoreName());
        transport.setAddress(deliveryStore.getDeliverStoreAddress());
        transport.setLatitude(deliveryStore.getDeliverStoreLatitude());
        transport.setLongitude(deliveryStore.getDeliverStoreLongitude());
        transport.setPositionSource("3");
        transport.setPhone(deliveryStore.getDeliverStorePhone());
        transport.setRemark("");
        addReq.setTransport(transport);
        // 3
        RiderOrderAddReq.Receiver receiver = new RiderOrderAddReq.Receiver();
        receiver.setName(orderDeliveryAddress.getReceiverName());
        receiver.setPhone(orderDeliveryAddress.getReceiverTelephone());
        receiver.setAddress(orderDeliveryAddress.getFullAddress());
        receiver.setLatitude(orderInfo.getReceiverLat());
        receiver.setLongitude(orderInfo.getReceiverLng());
        receiver.setPositionSource("3");
        addReq.setReceiver(receiver);

        // 4 item
        List<RiderOrderAddReq.Item> items = new ArrayList<>();
        for (OrderDetail orderDetail : orderDetailList) {
        	RiderOrderAddReq.Item riderItem = new RiderOrderAddReq.Item();
            riderItem.setItemId(orderDetail.getErpCode());
            riderItem.setItemName(orderDetail.getCommodityName());
            riderItem.setItemQuantity(orderDetail.getPickCount());
            riderItem.setIsAgentPurchase(0);
            riderItem.setIsNeedPackage(0);
            riderItem.setItemPrice(orderDetail.getPrice());
            riderItem.setItemActualPrice(orderDetail.getTotalAmount());
            // riderItem.setItem_size(orderDetail.getCommoditySpec());
            riderItem.setItemSize(0);
            riderItem.setAgentPurchasePrice(BigDecimal.valueOf(0.0));
            items.add(riderItem);
        }
        addReq.setItems(items);
        callRiderParamDto.setAddReq(addReq);
        callRiderParamDto.setRiderOrder(riderOrder);
    }

    /**
     * 呼叫骑手
     *
     * @return
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: chufeng(2910)
     * @Date: 2020/8/24 10:40
     */
    public OrderDeliveryRecord callRider(OrderInfo orderInfo, DsStoreDeliveryInfoResDTO deliveryStore, Boolean beRepeat, @Nullable String modifyDeliveryPlatformName
        , boolean appointCallRightNowFlag) {

        // 构建参数
        CallRiderParamDto callRiderParamDto = new CallRiderParamDto(orderInfo, deliveryStore, beRepeat, modifyDeliveryPlatformName,appointCallRightNowFlag);
        buildCallRider(callRiderParamDto);
        RiderOrderAddReq addReq = callRiderParamDto.getAddReq();
        RiderOrderAddReq.Order riderOrder = callRiderParamDto.getRiderOrder();

        HemsBaseData baseData = hemsCommonClient.constructHemsBaseData(deliveryStore.getMerCode(), deliveryStore.getPlatformCode(),
                deliveryStore.getDeliveryClientCode(), deliveryStore.getSessionKey());
        //【ID1028857】【美团配送】【对接预充值模式】：预充值模式使用优惠券   美团配送且门店设置方式为余额支付，则调用预充值接口
        if(DeliveryPlatformEnum.MEITUAN_RIDER.getCode().equals(deliveryStore.getPlatformCode())){
            //获取配送模式  0、账期支付，1、余额支付； 逗号分隔如1,2 ；空按账期支付处理，1,2 按账期支付处理
            //前面已默认账期支付，此处只判断余额支付    非空且不包含账期支付  则按余额支付处理
            if(!StringUtils.isEmpty(deliveryStore.getPayTypeCodes()) && (!deliveryStore.getPayTypeCodes().contains(DsConstants.STRING_ZERO))){
                riderOrder.setPayTypeCode(1);
                OrderDeliveryCompare orderDeliveryCompare = orderDeliveryCompareMapper.selectOne(
                        new QueryWrapper<OrderDeliveryCompare>().lambda()
                                .eq(OrderDeliveryCompare::getOrderNo,orderInfo.getOrderNo())
                                .eq(OrderDeliveryCompare::getPlatformCode,DeliveryPlatformEnum.MEITUAN_RIDER.getCode())
                                .eq(OrderDeliveryCompare::getStatus,DsConstants.INTEGER_ONE));
                if(orderDeliveryCompare != null){
                    riderOrder.setCouponsId(orderDeliveryCompare.getCouponsId());
                }else {
                    BaseHemsResp<RiderPreOrderAddResp> preResult = hemsCommonClient.callPreRider(addReq, baseData);
                    if (preResult.isSuccess()) {
                        RiderPreOrderAddResp data = preResult.getData();
                        String couponIds = (data == null ? null : data.getCouponsId());
                        riderOrder.setCouponsId(couponIds);
                    }
                }
            }
        }
        //【ID1034301】【蜂鸟配送平台】【新业务】：非KA新接口模式对接 蜂鸟即配预充值模式，暂无须对接预下单和优惠券接口
        if(DeliveryPlatformEnum.HUMMINGBIRD_JIT.getCode().equals(deliveryStore.getPlatformCode())){
            if(!StringUtils.isEmpty(deliveryStore.getPayTypeCodes()) && !deliveryStore.getPayTypeCodes().contains(DsConstants.STRING_TWO)){
                riderOrder.setPayTypeCode(1);
            }
        }
        log.info("callRider req  addReq:{}   baseData:{}",JSON.toJSONString(addReq), JSON.toJSONString(baseData));
        BaseHemsResp<RiderOrderAddResp> result = hemsCommonClient.callRider(addReq, baseData);
        //糟糕的解决方案，针对 “不支持该支付方式” 特殊处理重新请求一次,10月份需求完善时清理
        if (!result.isSuccess()) {
            if(riderErrMsg.equals(result.getMsg())){
                riderOrder.setPayTypeCode(1);
                addReq.setOrder(riderOrder);
                result = hemsCommonClient.callRider(addReq, baseData);
            }
        }

        if (!result.isSuccess()) {
            OrderDeliveryRecord updateEx = new OrderDeliveryRecord();
            updateEx.setExceptionReason(result.getMsg());
            updateEx.setOrderNo(orderInfo.getOrderNo());
            updateEx.setState(DeliveryStateEnum.EXCEPTION.getCode());
            updateEx.setDeliveryPlatName(DeliveryPlatformEnum.getByCode(deliveryStore.getPlatformCode()));
            updateEx.setRiderAddress("");
            updateEx.setRiderPhone("");
            updateEx.setRiderName("");
            updateEx.setPreCallFlag(DsConstants.INTEGER_ONE);
            orderDeliveryRecordMapper.updateStateException(updateEx);
            // 通知下发事件  补偿处理
            messageProducerService.produceUpdateOrderMessage(Collections.singletonList(orderInfo.getOrderNo()), OrderUpdateCodeEnum.CALL_RIDER.getCode());
            //更新配送费节约记录
            try {
                //更新配送费节约记录
                DeliveryFeeEconomizeRecord deliveryFeeEconomizeRecord = deliveryFeeEconomizeRecordService.getDeliveryFeeEconomizeRecord(orderInfo.getOrderNo());
                if(Objects.nonNull(deliveryFeeEconomizeRecord)){
                    LambdaUpdateWrapper<DeliveryFeeEconomizeRecord> updateWrapper = new LambdaUpdateWrapper<DeliveryFeeEconomizeRecord>()
                        .set(DeliveryFeeEconomizeRecord::getRealDeliveryPlatName, updateEx.getDeliveryPlatName() + "("+DeliveryStateEnum.EXCEPTION.getMsg()+")")
                        .set(DeliveryFeeEconomizeRecord::getRealDeliveryFee,null)
                        .set(DeliveryFeeEconomizeRecord::getRealEconomizeDeliveryFee,null)
                        .set(DeliveryFeeEconomizeRecord::getRiderOrderNo,null)
                        .eq(DeliveryFeeEconomizeRecord::getId,deliveryFeeEconomizeRecord.getId());
                    log.info("呼叫骑手失败后更新配送费节约记录,订单号:{},呼叫响应结果:{}",orderInfo.getOrderNo(),JSONObject.toJSONString(result));
                    deliveryFeeEconomizeRecordService.updateDeliveryFeeEconomizeRecordByUpdateWrapper(updateWrapper);
                }
            }catch (Exception e){
                log.info("呼叫骑手失败后更新配送费节约记录失败,订单号:{},失败原因:{}",orderInfo.getOrderNo(),JSONObject.toJSONString(e.getMessage()));
            }
            log.warn("OrderHandler: callRider error, orderNo:{}, code:{}, msg:{}", orderInfo.getOrderNo(), result.getCode(), result.getMsg());
            String appointment = riderOrder.getOrderType().equals(DsConstants.STRING_THREE) ? "预约" : "";
            orderInfo = orderBasicManager.getOrderBaseInfo(orderInfo.getOrderNo());
            HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ZERO, orderInfo.getMerCode(), orderInfo.getOrderNo(),
                    orderInfo.getOrderState(),orderInfo.getErpState(),
                    OrderLogEnum.CALL_RIDER.getAction(),appointment + "呼叫骑手报错:"+result.getMsg(),null);
            throw ExceptionUtil.getWarnException(DsErrorType.CALL_RIDER_ERROR.getCode(), result.getMsg());
        }

        // 更新
        RiderOrderAddResp riderOrderAddResp = result.getData();
        OrderDeliveryRecord update = new OrderDeliveryRecord();
        if (riderOrderAddResp.getRiderOrderId() != null) {
            update.setRiderOrderNo(riderOrderAddResp.getRiderOrderId());
        }else{
            update.setRiderOrderNo("");
        }
        update.setState(DeliveryStateEnum.UN_TAKE.getCode());
        update.setActualDeliveryFee(BigDecimal.valueOf(riderOrderAddResp.getFee()));
        update.setDeliveryFeeTotal(BigDecimal.valueOf(riderOrderAddResp.getDeliverFee()));
        update.setCallTime(new Date());
        update.setOrderNo(orderInfo.getOrderNo());
        update.setDeliveryPlatName(DeliveryPlatformEnum.getByCode(deliveryStore.getPlatformCode()));
        update.setDeliveryClientCode(deliveryStore.getDeliveryClientCode());
        update.setDeliveryStoreCode(deliveryStore.getDeliveryStoreCode());
        update.setRiderPhone("");
        update.setRiderName("");
        update.setRiderPhone("");

        orderDeliveryRecordServiceAuto.lambdaUpdate().eq(OrderDeliveryRecord::getOrderNo, update.getOrderNo())
                .update(update);

        //更新比价骑手状态
        UpdateWrapper<OrderDeliveryCompare> updateCompare = new UpdateWrapper<>();
        updateCompare.lambda().eq(OrderDeliveryCompare::getOrderNo,orderInfo.getOrderNo())
                .eq(OrderDeliveryCompare::getPlatformCode,deliveryStore.getPlatformCode())
                .set(OrderDeliveryCompare::getStatus,DsConstants.INTEGER_ONE);
        orderDeliveryCompareMapper.update(null,updateCompare);

        orderInfo = orderBasicManager.getOrderBaseInfo(orderInfo.getOrderNo());
        HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, orderInfo.getMerCode(), orderInfo.getOrderNo(),
                orderInfo.getOrderState(),orderInfo.getErpState(),
                OrderLogEnum.CALL_RIDER.getAction(),  OrderLogEnum.getCallRiderInfo(update.getRiderOrderNo(),riderOrder.getOrderType()),null);

        // 通知下发事件
        messageProducerService.produceUpdateOrderMessage(Collections.singletonList(orderInfo.getOrderNo()), OrderUpdateCodeEnum.CALL_RIDER.getCode());

        try {
            //更新配送费节约记录
            DeliveryFeeEconomizeRecord deliveryFeeEconomizeRecord = deliveryFeeEconomizeRecordService.getDeliveryFeeEconomizeRecord(orderInfo.getOrderNo());
            if(Objects.nonNull(deliveryFeeEconomizeRecord)){
                DeliveryFeeEconomizeRecord updateEconomizeRecord = new DeliveryFeeEconomizeRecord();
                updateEconomizeRecord.setId(deliveryFeeEconomizeRecord.getId());
                updateEconomizeRecord.setRiderOrderNo(update.getRiderOrderNo());
                updateEconomizeRecord.setRealDeliveryPlatCode(deliveryStore.getPlatformCode());
                updateEconomizeRecord.setRealDeliveryPlatName(DeliveryPlatformEnum.getByCode(deliveryStore.getPlatformCode()));
                updateEconomizeRecord.setRealDeliveryFee(BigDecimal.valueOf(riderOrderAddResp.getFee()));
                Map<String, BigDecimal> deliveryCodeFeeMap = JSONObject.parseObject(deliveryFeeEconomizeRecord.getDeliveryFeeJson(), new TypeReference<Map<String, BigDecimal>>() {});
                //获取最大的运费
                BigDecimal maxDeliveryFee = Collections.max(deliveryCodeFeeMap.values().stream().filter(Objects::nonNull).collect(Collectors.toList()));
                updateEconomizeRecord.setRealEconomizeDeliveryFee(maxDeliveryFee.subtract(updateEconomizeRecord.getRealDeliveryFee()));
                log.info("呼叫骑手成功后更新配送费节约记录,订单号:{},待更新记录:{},呼叫响应结果:{}",orderInfo.getOrderNo(),JSONObject.toJSONString(updateEconomizeRecord),JSONObject.toJSONString(riderOrderAddResp));
                deliveryFeeEconomizeRecordService.updateDeliveryFeeEconomizeRecordById(updateEconomizeRecord);
            }
        }catch (Exception e){
            log.info("呼叫骑手成功后更新配送费节约记录失败,订单号:{},失败原因:{}",orderInfo.getOrderNo(),JSONObject.toJSONString(e.getMessage()));
        }
        return update;
    }

    /**
    * @Description: 呼叫骑手(延迟或重试)
    * @Param: [orderInfo, deliveryStore, beRepeat, modifyDeliveryPlatformName,appointCallRightNowFlag]
    * @return: void
    * @Author: syuson
    * @Date: 2021-12-7
    */
    public boolean callRiderForDelayAndRetry(OrderInfo orderInfo, DsStoreDeliveryInfoResDTO deliveryStore, Boolean beRepeat, String modifyDeliveryPlatformName
            ,boolean appointCallRightNowFlag) {

        // 构建参数
        CallRiderParamDto callRiderParamDto = new CallRiderParamDto(orderInfo, deliveryStore, beRepeat, modifyDeliveryPlatformName,appointCallRightNowFlag);
        buildCallRider(callRiderParamDto);
        RiderOrderAddReq addReq = callRiderParamDto.getAddReq();
        RiderOrderAddReq.Order riderOrder = callRiderParamDto.getRiderOrder();

        HemsBaseData baseData = hemsCommonClient.constructHemsBaseData(deliveryStore.getMerCode(), deliveryStore.getPlatformCode(),
                deliveryStore.getDeliveryClientCode(), deliveryStore.getSessionKey());
        //【ID1028857】【美团配送】【对接预充值模式】：预充值模式使用优惠券   美团配送且门店设置方式为余额支付，则调用预充值接口
        if(DeliveryPlatformEnum.MEITUAN_RIDER.getCode().equals(deliveryStore.getPlatformCode())){
            //获取配送模式  0、账期支付，1、余额支付； 逗号分隔如1,2 ；空按账期支付处理，1,2 按账期支付处理
            //前面已默认账期支付，此处只判断余额支付    非空且不包含账期支付  则按余额支付处理
            if(!StringUtils.isEmpty(deliveryStore.getPayTypeCodes()) && (!deliveryStore.getPayTypeCodes().contains(DsConstants.STRING_ZERO))){
                riderOrder.setPayTypeCode(1);
                BaseHemsResp<RiderPreOrderAddResp> preResult = hemsCommonClient.callPreRider(addReq, baseData);
                if (preResult.isSuccess()) {
                    RiderPreOrderAddResp data = preResult.getData();
                    String couponIds = (data == null ? null : data.getCouponsId());
                    riderOrder.setCouponsId(couponIds);
                }
            }
        }
        BaseHemsResp<RiderOrderAddResp> result = hemsCommonClient.callRider(addReq, baseData);
        //糟糕的解决方案，针对 “不支持该支付方式” 特殊处理重新请求一次,10月份需求完善时清理
        if (!result.isSuccess()) {
            if(riderErrMsg.equals(result.getMsg())){
                riderOrder.setPayTypeCode(1);
                addReq.setOrder(riderOrder);
                result = hemsCommonClient.callRider(addReq, baseData);
            }
        }

        if (!result.isSuccess()) {
            OrderDeliveryRecord updateEx = new OrderDeliveryRecord();
            updateEx.setExceptionReason(result.getMsg());
            updateEx.setOrderNo(orderInfo.getOrderNo());
            updateEx.setState(DeliveryStateEnum.EXCEPTION.getCode());
            updateEx.setDeliveryPlatName(DeliveryPlatformEnum.getByCode(deliveryStore.getPlatformCode()));
            updateEx.setRiderAddress("");
            updateEx.setRiderPhone("");
            updateEx.setRiderName("");
            orderDeliveryRecordMapper.updateStateException(updateEx);
            log.error("OrderHandler: callRider error, orderNo:{}, code:{}, msg:{}", orderInfo.getOrderNo(),
                    result.getCode(), result.getMsg());
            String appointment = riderOrder.getOrderType().equals(DsConstants.STRING_THREE) ? "预约" : "";
            orderInfo = orderBasicManager.getOrderBaseInfo(orderInfo.getOrderNo());
            HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ZERO, orderInfo.getMerCode(), orderInfo.getOrderNo(),
                    orderInfo.getOrderState(),orderInfo.getErpState(),
                    OrderLogEnum.CALL_RIDER_DELAY.getAction(),appointment + "呼叫骑手报错:"+result.getMsg(),null);
            return false;
        }

        // 更新
        RiderOrderAddResp riderOrderAddResp = result.getData();
        OrderDeliveryRecord update = new OrderDeliveryRecord();
        if (riderOrderAddResp.getRiderOrderId() != null) {
            update.setRiderOrderNo(riderOrderAddResp.getRiderOrderId());
        }
        update.setState(DeliveryStateEnum.UN_TAKE.getCode());
        update.setActualDeliveryFee(BigDecimal.valueOf(riderOrderAddResp.getFee()));
        update.setDeliveryFeeTotal(BigDecimal.valueOf(riderOrderAddResp.getDeliverFee()));
        update.setCallTime(new Date());
        update.setOrderNo(orderInfo.getOrderNo());
        update.setDeliveryPlatName(DeliveryPlatformEnum.getByCode(deliveryStore.getPlatformCode()));
        update.setDeliveryClientCode(deliveryStore.getDeliveryClientCode());
        update.setDeliveryStoreCode(deliveryStore.getDeliveryStoreCode());

        orderDeliveryRecordServiceAuto.lambdaUpdate().eq(OrderDeliveryRecord::getOrderNo, update.getOrderNo())
                .update(update);

        orderInfo = orderBasicManager.getOrderBaseInfo(orderInfo.getOrderNo());
        HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, orderInfo.getMerCode(), orderInfo.getOrderNo(),
                orderInfo.getOrderState(),orderInfo.getErpState(),
                OrderLogEnum.CALL_RIDER_DELAY.getAction(),  OrderLogEnum.getCallRiderInfo(update.getRiderOrderNo(),riderOrder.getOrderType()),null);
        return true;
    }

    public String validate(String deliveryTimeDesc, OrderInfo orderInfo) {
        if (StringUtils.isEmpty(deliveryTimeDesc) || orderInfo == null) {
            return null;
        }
        // 默认提前60分钟呼叫  如有配置 取配置时间 https://www.tapd.cn/61969829/prong/stories/view/1161969829001053837
        Integer bookingRemindTime = DsConstants.DEFAULT_BOOKING_REMIND_TIME;
        DsStoreSoundConfig dsStoreSoundConfig = dsStoreSoundConfigService.getDsStoreSoundConfig(orderInfo.getMerCode(),
                orderInfo.getThirdPlatformCode(), orderInfo.getClientCode(), orderInfo.getOnlineStoreCode());
        if (dsStoreSoundConfig != null && dsStoreSoundConfig.getBookingRemindTime() != null) {
            bookingRemindTime = dsStoreSoundConfig.getBookingRemindTime();
        }

        Date date = null;
        /**
         * 1023798  预约订单-预约配送呼叫三方配送骑手优化
         * 如果预计送达结束时间 - 预计送达开始时间大于1小时，则按照预计送达开始时间呼叫骑手；
         * 如果小于1小时，则将预计送达结束时间 - 1小时开始呼叫骑手。
         * 如果只有一个时间，则将该时间 - 1小时开始呼叫骑手
         */
        Matcher matcher = compile.matcher(deliveryTimeDesc);
        ArrayList<String> timeStrs = new ArrayList<>();
        for (int i = 0; i < 2; i++) {
            if (matcher.find()) {
                timeStrs.add(matcher.group(0));
            } else {
                break;
            }
        }
        if (!CollectionUtils.isEmpty(timeStrs)) {
            int matchCnt = timeStrs.size();
            Date beginDate = null;
            Date endDate = null;
            try {
                beginDate = DateUtils.parseDate(timeStrs.get(0), parsePatterns);
            } catch (ParseException e) {
                log.error("callrider validate exception deliveryTimeDesc{}, exceptionMsg{}", deliveryTimeDesc, e.getMessage());
                return null;
            }
            if (matchCnt == 1) {
                date = DateUtil.addDate(beginDate,0,0,0,0, -bookingRemindTime,0,0);

            } else if (matchCnt == 2) {
                try {
                    endDate = DateUtils.parseDate(timeStrs.get(1), parsePatterns);
                } catch (ParseException e) {
                    log.error("callrider parse endTimeStr exception deliveryTimeDesc{}, exceptionMsg{}", deliveryTimeDesc, e.getMessage());
                    date = DateUtil.addDate(beginDate, 0, 0, 0, 0, -bookingRemindTime, 0, 0);
                }
                if ((endDate.getTime() - beginDate.getTime()) / 1000 - bookingRemindTime * 60 >= 0) {
                    date = beginDate;
                } else {
                    date = DateUtil.addDate(endDate, 0, 0, 0, 0, -bookingRemindTime, 0, 0);
                }
            } else {
                return null;
            }
        } else {
            return null;
        }

        if (date.before(new Date())) {
            return null;
        }

        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date);
    }

    public boolean isAppointDelivery(OrderInfo orderInfo) {
        if (DsConstants.INTEGER_ONE.equals(orderInfo.getDeliveryTimeType())
                && !StringUtils.isEmpty(orderInfo.getDeliveryTimeDesc()) && !StringUtils.isEmpty(validate(orderInfo.getDeliveryTimeDesc(), orderInfo))) {
            return true;
        }
        return false;
    }

    /**
    * @Description: 拣货后是否需要自动下账
    * @Param: [userId, orderNo]
    * @return: java.lang.String
    * @Author: syuson
    * @Date: 2021-5-26
    */
    public String autoEnterAccountAfterPick(String userId,Long orderNo){
        OrderInfoAllDomain orderInfo = orderBasicManager.checkOrderAllLock(orderNo, true);
        OrderPayInfo orderPayInfo = orderInfo.getOrderPayInfo();
        StoreBillConfig storeBillConfig = orderInfo.getStoreBillConfig();
        String billMessage = "";
        if(!orderEnterAccountManager.autoEnterAccountOrderPick(orderPayInfo,storeBillConfig)){
            return billMessage;
        }
        // 平安单子走平安的下账
        if(PlatformCodeEnum.PING_AN_CENTRAL.getCode().equals(orderInfo.getThirdPlatformCode())
            || PlatformCodeEnum.PA_CITY.getCode().equals(orderInfo.getThirdPlatformCode())){
            billMessage = orderEnterAccountManager.enterAccountPingAn(userId, orderInfo, orderPayInfo, null);
        }else {
            billMessage = orderEnterAccountManager.enterAccount(userId, orderInfo, orderPayInfo, Boolean.FALSE, null);
        }
        if(!StringUtils.isEmpty(billMessage)){
            billMessage = DsErrorType.AUTO_ENTER_ACCOUNT_FAIL.getMsg();
        }
        return billMessage;
    }

    /**
     * omsstatus 1 待接单 2待拣货 3待配送 4配送中 5已完成 6订单取消 7订单退款 8待退货 9待退款 0其他
     * o2oorderState 5待处理 10待接单 20待拣货 30待配送 40配送中 100已完成 102已取消 101已关闭
     */
    private Integer transferO2OStatus(String omsStatus) {
        Integer o2oStatus = null;
        if (StringUtils.isEmpty(omsStatus)) {
            return o2oStatus;
        }

        switch (omsStatus) {
            case "1" :
                o2oStatus = OrderStateEnum.UN_TAKE.getCode();
                break;
            case "2" :
                o2oStatus = OrderStateEnum.UN_PICK.getCode();
                break;
            case "3" :
                o2oStatus = OrderStateEnum.UN_DELIVERY.getCode();
                break;
            case "4" :
                o2oStatus = OrderStateEnum.POSTING.getCode();
                break;
            case "5" :
            case "7" :
            case "8" :
            case "9" :
                o2oStatus = OrderStateEnum.COMPLETED.getCode();
                break;
            case "6" :
                o2oStatus = OrderStateEnum.CANCEL.getCode();
                break;
            default:
                o2oStatus = OrderStateEnum.UN_CHECK.getCode();
        }
        return o2oStatus;
    }

}
