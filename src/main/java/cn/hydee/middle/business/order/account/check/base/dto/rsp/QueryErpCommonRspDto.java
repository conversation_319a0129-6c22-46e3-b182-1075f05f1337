
package cn.hydee.middle.business.order.account.check.base.dto.rsp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class QueryErpCommonRspDto {
	
	@ApiModelProperty(value = "有效订单数")
	private Integer validOrderCount;
	@ApiModelProperty(value = "差异订单数")
	private Integer diffOrderCount;
	@ApiModelProperty(value = "oms下账总金额")
	private BigDecimal omsTotalAmount;
	@ApiModelProperty(value = "erp下账总金额")
	private BigDecimal erpTotalAmount;
	@ApiModelProperty(value = "差异总金额")
	private BigDecimal diffTotalAmount;
	
}
  
