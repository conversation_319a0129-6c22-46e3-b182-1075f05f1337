package cn.hydee.middle.business.order.yxtadapter.constant.middle;

/**
 * 对接middle的商品类型枚举
 */
public enum CommodityType {

    NORMAL_COMMODITY(1,"普通商品"),

    GROUP_COMMODITY(2,"组合商品"),

    GIFT_COMMODITY(3,"赠品")
    ;
    private Integer code;
    private String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    CommodityType(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
