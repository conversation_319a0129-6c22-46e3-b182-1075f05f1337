package cn.hydee.middle.business.order.storeautosync.infrastructure.mysql.service.impl;

import cn.hydee.middle.business.order.storeautosync.infrastructure.mysql.entity.DsOnlineStorePullOrderRecord;
import cn.hydee.middle.business.order.storeautosync.infrastructure.mysql.mapper.DsOnlineStorePullOrderRecordMapper;
import cn.hydee.middle.business.order.storeautosync.infrastructure.mysql.service.DsOnlineStorePullOrderRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

@Service
public class DsOnlineStorePullOrderRecordServiceImpl extends ServiceImpl<DsOnlineStorePullOrderRecordMapper, DsOnlineStorePullOrderRecord> implements DsOnlineStorePullOrderRecordService {

}
