package cn.hydee.middle.business.order.yxtadapter.infrastructure.http;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hydee.middle.business.order.Enums.PosModeEnum;
import cn.hydee.middle.business.order.exception.OmsException;
import cn.hydee.middle.business.order.util.HttpClientManager;
import cn.hydee.middle.business.order.util.RsaUtil;
import cn.hydee.middle.business.order.yxtadapter.domain.dictionary.InnerStoreDictionary;
import cn.hydee.middle.business.order.yxtadapter.domain.pos.hdpos.*;
import cn.hydee.middle.business.order.yxtadapter.infrastructure.feign.PosInterfaceClient;
import cn.hydee.starter.dto.ResponseBase;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/19
 * @since 1.0
 */
@Slf4j
@Service
public class HdPosInterfaceClient implements PosInterfaceClient {

    @Value("${business.order.hdpos.h1.url}")
    private String hdPosUrlH1;

    @Value("${business.order.hdpos.h2.url}")
    private String hdPosUrlH2;

    //海典pos正向单下账
    private static final String ORDER_BILL = "/o2o_lingshou_v2";

    //海典pos退款单下账
    private static final String REFUND_ORDER_BILL = "/o2o_refund_v2";

    //海典推送调拨单接口
    private static final String SEND_ROUTE = "/o2o_kucundiaobo_v1";
    private static final String BATCH_NO = "/o2o_batchno";

    private static final String MER_CODE = "500001";

    //海典pos医保正向单下账
    private static final String MedicalInsurance_ORDER_BILL = "/o2o_lingshou_v2_shyb";

    //海典pos医保退款单下账
    private static final String MedicalInsurance_REFUND_ORDER_BILL = "/o2o_refund_v2_shyb";

    @Autowired
    private HttpClientManager httpClientManager;


    /**
     * 海典POS正向单下账接口
     *
     * @param orderBillRequest
     * @return ResponseBase<OrderBillResponse>
     */
    @Override
    public ResponseBase<OrderBillResponse> orderAccounting(OrderBillRequest orderBillRequest,InnerStoreDictionary dictionary) {
        String url = this.getUrlPrefix(dictionary).concat(ORDER_BILL);
        Map<String, String> header = RsaUtil.generateRsa2Sign(orderBillRequest.getMerCode());
        orderBillRequest.setSign(header.get("sign"));
        orderBillRequest.setSignTimestamp(header.get("signTimestamp"));
        Map<String, Object> body = BeanUtil.beanToMap(orderBillRequest);
        log.info("orderAccounting request: url:{}, 请求参数{}", url,JSONObject.toJSONString(orderBillRequest));
        ResponseBase<OrderBillResponse> response = httpClientManager.requestNew(url, body, header, new TypeReference<ResponseBase<OrderBillResponse>>() {
        });
        log.info("orderAccounting responseBase: {}", JSONObject.toJSONString(response));
        return response;
    }

    /**
     * 海典POS逆向单下账接口
     *
     * @param refundBillRequest
     * @return ResponseBase<RefundBillResponse>
     */
    @Override
    public ResponseBase<RefundBillResponse> refundOrderAccounting(RefundBillRequest refundBillRequest, InnerStoreDictionary dictionary) {
        String url = this.getUrlPrefix(dictionary).concat(REFUND_ORDER_BILL);
        //签名处理
        Map<String, String> header = RsaUtil.generateRsa2Sign(refundBillRequest.getMerCode());
        refundBillRequest.setSign(header.get("sign"));
        refundBillRequest.setSignTimestamp(header.get("signTimestamp"));
        Map<String, Object> body = BeanUtil.beanToMap(refundBillRequest);
        log.info("退款单下账请求 url: {} 请求参数{}", url,JSONObject.toJSONString(body));
        ResponseBase<RefundBillResponse> response = httpClientManager.requestNew(url, body, header, new TypeReference<ResponseBase<RefundBillResponse>>() {
        });
        log.info("退款单下账返回 {}", JSONObject.toJSONString(response));
        return response;
    }

    /**
     * 海典路由推送调拨单接口
     *
     * @param allotRequest
     * @param dictionary
     * @return ResponseBase<RouteResponse>
     */
    @Override
    public ResponseBase<AllotResponse> sendRouteAllot(AllotRequest allotRequest, InnerStoreDictionary dictionary) {
        String url = this.getUrlPrefix(dictionary).concat(SEND_ROUTE);
        Map<String, String> header = RsaUtil.generateRsa2Sign(MER_CODE);
        allotRequest.setSign(header.get("sign"));
        allotRequest.setSignTimestamp(header.get("signTimestamp"));
        Map<String, Object> body = BeanUtil.beanToMap(allotRequest);
        log.info("sendRouteAllot request: url:{}, params:{}", url,JSONObject.toJSON(allotRequest));
        ResponseBase<AllotResponse> response = httpClientManager.requestNew(url, body, header, new TypeReference<ResponseBase<AllotResponse>>() {
        });
        log.info("sendRouteAllot responseBase: {}", JSONObject.toJSON(response));
        return response;
    }

    @Override
    public ResponseBase<O2OBatchNoResponse> batchNo(O2OBatchNoRequest allotRequest, InnerStoreDictionary dictionary) {
        String url = this.getUrlPrefix(dictionary).concat(BATCH_NO);
        Map<String, String> header = RsaUtil.generateRsa2Sign(MER_CODE);
        allotRequest.setSign(header.get("sign"));
        allotRequest.setSignTimestamp(header.get("signTimestamp"));
        Map<String, Object> body = BeanUtil.beanToMap(allotRequest);
        log.info("sendRouteAllot request: url:{}, params:{}", url,JSONObject.toJSON(allotRequest));
        ResponseBase<O2OBatchNoResponse> response = httpClientManager.requestNew(url, body, header, new TypeReference<ResponseBase<O2OBatchNoResponse>>() {
        });
        log.info("sendRouteAllot responseBase: {}", JSONObject.toJSON(response));
        return response;
    }

    @Override
    public ResponseBase<OrderBillResponse> medicalInsuranceOrderAccounting(OrderBillRequest orderBillRequest, InnerStoreDictionary dictionary) {
        String url = this.getUrlPrefix(dictionary).concat(MedicalInsurance_ORDER_BILL);
        Map<String, String> header = RsaUtil.generateRsa2Sign(orderBillRequest.getMerCode());
        orderBillRequest.setSign(header.get("sign"));
        orderBillRequest.setSignTimestamp(header.get("signTimestamp"));
        Map<String, Object> body = BeanUtil.beanToMap(orderBillRequest);
        log.info("海典医保订单下账: url:{}, 请求参数{}", url, JSONObject.toJSONString(orderBillRequest));
        ResponseBase<OrderBillResponse> response = httpClientManager.requestNew(url, body, header, new TypeReference<ResponseBase<OrderBillResponse>>() {
        });
        log.info("海典医保订单下账响应结果: {}", JSONObject.toJSONString(response));
        return response;
    }

    @Override
    public ResponseBase<RefundBillResponse> medicalInsuranceRefundOrderAccounting(RefundBillRequest refundBillRequest, InnerStoreDictionary dictionary) {
        String url = this.getUrlPrefix(dictionary).concat(MedicalInsurance_REFUND_ORDER_BILL);
        //签名处理
        Map<String, String> header = RsaUtil.generateRsa2Sign(refundBillRequest.getMerCode());
        refundBillRequest.setSign(header.get("sign"));
        refundBillRequest.setSignTimestamp(header.get("signTimestamp"));
        Map<String, Object> body = BeanUtil.beanToMap(refundBillRequest);
        log.info("海典医保退款单下账请求 url: {} 请求参数{}", url,JSONObject.toJSONString(body));
        ResponseBase<RefundBillResponse> response = httpClientManager.requestNew(url, body, header, new TypeReference<ResponseBase<RefundBillResponse>>() {
        });
        log.info("海典医保退款单下账响应结果 {}", JSONObject.toJSONString(response));
        return response;
    }

    /**
     * 获取url前缀
     *
     * @return String
     */
    private String getUrlPrefix(InnerStoreDictionary dictionary) {
        if (ObjectUtil.isNull(dictionary)) {
            throw new OmsException("字典配置表不存在该门店");
        }
        if (PosModeEnum.POS_HD_H2.getCode().equals(dictionary.getPosMode())
            || PosModeEnum.POS_HD_H1.getCode().equals(dictionary.getPosMode())
        ) {
            if (StringUtils.isEmpty(dictionary.getPosUrl())) {
                throw new OmsException("该门店海典下账请求地址未配置");
            }
            return dictionary.getPosUrl();
        }
        throw new OmsException("字典配置表PosMode值不匹配");
    }
}
