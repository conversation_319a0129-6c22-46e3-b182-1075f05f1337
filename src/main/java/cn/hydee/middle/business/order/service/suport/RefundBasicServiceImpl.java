package cn.hydee.middle.business.order.service.suport;

import cn.hutool.core.collection.CollUtil;
import cn.hydee.erp.model.base.BaseErpResp;
import cn.hydee.erp.model.order.OrderReceiveResp;
import cn.hydee.middle.business.order.Enums.*;
import cn.hydee.middle.business.order.domain.OrderDetailDomain;
import cn.hydee.middle.business.order.domain.RefundOrderDomain;
import cn.hydee.middle.business.order.dto.erp.LockStockRspDto;
import cn.hydee.middle.business.order.dto.req.OrderPickInfoReqDto;
import cn.hydee.middle.business.order.dto.rsp.SearchOrderRspDto;
import cn.hydee.middle.business.order.entity.*;
import cn.hydee.middle.business.order.mapper.OrderInfoMapper;
import cn.hydee.middle.business.order.mapper.OrderPickInfoMapper;
import cn.hydee.middle.business.order.mapper.RefundLogMapper;
import cn.hydee.middle.business.order.mapper.RefundOrderMapper;
import cn.hydee.middle.business.order.refund.context.RefundOrderContext;
import cn.hydee.middle.business.order.refund.creation.type.custom.RefundAllForCancelCreation;
import cn.hydee.middle.business.order.service.CommodityStockService;
import cn.hydee.middle.business.order.service.LocalStockService;
import cn.hydee.middle.business.order.util.EnterpriseWeChatUtil;
import cn.hydee.middle.business.order.util.ErpRspUtil;
import cn.hydee.middle.business.order.util.ExceptionUtil;
import cn.hydee.middle.business.order.v2.annotation.OrderLock;
import cn.hydee.middle.business.order.v2.feign.erp.ErpCommonClient;
import cn.hydee.starter.dto.ResponseBase;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/3/23 16:05
 */
@Service
@Slf4j
public class RefundBasicServiceImpl implements RefundBasicService {
    @Autowired
    private OrderPickInfoMapper orderPickInfoMapper;
    @Autowired
    private OrderBasicService orderBasicService;
    @Autowired
    private RefundOrderMapper refundOrderMapper;
    @Autowired
    private RefundLogMapper refundLogMapper;
    @Autowired
    private OrderInfoMapper orderInfoMapper;
    @Autowired
    private EnterpriseWeChatUtil enterpriseWeChatUtil;
    @Autowired
    private RefundAllForCancelCreation refundAllForCancelCreation;
    @Autowired
    private ErpCommonClient erpCommonClient;
//    @Autowired
//    private LocalStockService localStockService;
    @Autowired
    private CommodityStockService commodityStockService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refundBeforePick(OrderInfo orderInfo, List<OrderDetailDomain> detailList,
                                 RefundOrder refundOrder, List<RefundDetail> refundDetailList) {
        List<OrderDetail> list = new ArrayList<>();
        boolean partRefund = RefundTypeEnum.PART.getCode().equals(refundOrder.getType());
        if (partRefund) {
            for (OrderDetailDomain detailTemp: detailList){
                if (OrderDetailStatusEnum.REPLACE.getCode().equals(detailTemp.getStatus())) {
                    continue;
                }
                if (OrderDetailStatusEnum.REFUND.getCode().equals(detailTemp.getStatus())) {
                    continue;
                }
                if (detailTemp.getPickCount() <= 0) {
                    continue;
                }
                OrderDetail odReqDto = new OrderDetail();
                BeanUtils.copyProperties(detailTemp, odReqDto);
                //【ID1011786】解锁内部会再进行计算，此处取原商品数量
                odReqDto.setCommodityCount(detailTemp.getCommodityCount());
                list.add(odReqDto);
            }
        }
        Map<String, OrderDetailDomain> detailListMap = detailList.stream().collect(Collectors.toMap(p -> String.format("%s_%s", p.getErpCode(), p.getThirdDetailId()), p -> p));
        // 退款时 那退款详情 订单号+erpcode 找到原单详情  如出现多个拿拣货号>退款数量那个商品
        List<OrderDetail> refundSourceList = new ArrayList<>();
        for (RefundDetail refundDetail : refundDetailList) {
            String key = String.format("%s_%s", refundDetail.getErpCode(), refundDetail.getThirdDetailId());
            OrderDetailDomain sourceOrderDetail = detailListMap.get(key);
            if (sourceOrderDetail != null) {
                OrderDetail orderDetail = new OrderDetail();
                orderDetail.setErpCode(refundDetail.getErpCode());
                orderDetail.setOrderNo(refundOrder.getOrderNo());
                orderDetail.setId(sourceOrderDetail.getId());
                orderDetail.setCommodityCount(refundDetail.getRefundCount());
                orderDetail.setRefundCount(0);
                refundSourceList.add(orderDetail);
            }
        }
        if (RefundTypeEnum.PART.getCode().equals(refundOrder.getType())) {
            if (CollUtil.isNotEmpty(refundSourceList)) {
                commodityStockService.unLockStockNew(orderInfo, refundSourceList,"部分退款释放库存",UnLockStockTypeEnum.PART);
            }
        }
        if (RefundTypeEnum.ALL.getCode().equals(refundOrder.getType())) {
            commodityStockService.unLockStockNew(orderInfo, null,"全部退款释放库存",UnLockStockTypeEnum.ALL);
        }
        OrderInfo orderUpdate = null;
        if (!Strings.isEmpty(orderInfo.getErpAdjustNo())) {
            try{
            	// 【ID1017200】 接单锁库存、解锁库存接口归一
                ResponseBase<LockStockRspDto> reLockResp = erpCommonClient.lockStockNoBatchNum(orderInfo.getMerCode(), "", orderInfo, list, IntYesNoEnum.NO.getCode(),DsConstants.NEED_RETRY);
                if (!ErpRspUtil.checkSuccess(reLockResp)) {
                    throw ExceptionUtil.getWarnException(DsErrorType.ERP_REFUND_LOCK_STOCK_ERROR);
                }
                LockStockRspDto lockStockRspDto = reLockResp.getData();
                if (lockStockRspDto.getErrorMsg() != null) {
                    throw ExceptionUtil.getWarnException(DsErrorType.ERP_REFUND_LOCK_STOCK_ERROR.getCode(), reLockResp.getData().getErrorMsg());
                }
                if (lockStockRspDto.getErrorlist() != null && lockStockRspDto.getErrorlist().size() > 0) {
                    throw ExceptionUtil.getWarnException(DsErrorType.ERP_REFUND_LOCK_STOCK_ERROR);
                }
                if (Strings.isEmpty(lockStockRspDto.getAdjustno())) {
                    throw ExceptionUtil.getWarnException(DsErrorType.ERP_REFUND_LOCK_STOCK_ERROR);
                }

                orderInfo.setErpAdjustNo(reLockResp.getData().getAdjustno());
                orderUpdate = new OrderInfo();
                orderUpdate.setOrderNo(orderInfo.getOrderNo());
                orderUpdate.setErpAdjustNo(lockStockRspDto.getAdjustno());
            }catch (Exception e){
                log.error("lockStockNoBatchNum,error,orderNo:{},{}",orderInfo.getOrderNo(),e);
                // 向企业微信发送messageId
                String msg = "【解锁库存失败 】,merCode:"+orderInfo.getMerCode()+",orderNo:"+orderInfo.getOrderNo();
                enterpriseWeChatUtil.sendMsg(DsConstants.UNLOCK_ORDER_ERROR, "",orderInfo.getThirdOrderNo(),msg);
            }
        }
//        else {
//            if (RefundTypeEnum.PART.getCode().equals(refundOrder.getType())) {
//                if (CollUtil.isNotEmpty(refundSourceList)) {
////                    localStockService.unlockStock(orderInfo, refundSourceList, StockSourceEnum.REFUND_UNLOCK);
//                    commodityStockService.unlockStock(orderInfo, refundSourceList);
//                }
//            }
//            if (RefundTypeEnum.ALL.getCode().equals(refundOrder.getType())) {
////                localStockService.unlockStock(orderInfo, null, StockSourceEnum.REFUND_UNLOCK);
//                commodityStockService.unlockStock(orderInfo, refundSourceList);
//            }
//        }

        if (RefundTypeEnum.ALL.getCode().equals(refundOrder.getType())) {
            if (orderUpdate == null) {
                orderUpdate = new OrderInfo();
                orderUpdate.setOrderNo(orderInfo.getOrderNo());
            }
            orderUpdate.setErpState(ErpStateEnum.CANCELED.getCode());
        } else {
            if (OrderStateEnum.CLOSED.getCode().equals(orderInfo.getOrderState())
                    || OrderStateEnum.CANCEL.getCode().equals(orderInfo.getOrderState()) ) {
                if (orderUpdate == null) {
                    orderUpdate = new OrderInfo();
                    orderUpdate.setOrderNo(orderInfo.getOrderNo());
                }
                orderUpdate.setErpState(ErpStateEnum.CANCELED.getCode());
            }
        }
        if (DsConstants.STRING_TWO.equals(orderInfo.getOrderIsNew())) {
            if(orderUpdate == null) {
                orderUpdate = new OrderInfo();
               orderUpdate.setOrderNo(orderInfo.getOrderNo());
            }
            orderUpdate.setErpState(ErpStateEnum.WAIT_SALE.getCode());
        }

        if (orderUpdate != null) {
            orderInfoMapper.updateOrder(orderUpdate);
        }

        Date now = new Date();
        RefundOrder reUpdate = new RefundOrder().setBillTime(now)
                .setRefundNo(refundOrder.getRefundNo())
                .setErpState(RefundErpStateEnum.CANCELED.getCode());
        refundOrderMapper.updateByRefundNo(reUpdate);
        refundOrder.setErpState(RefundErpStateEnum.CANCELED.getCode());
        if (DsConstants.STRING_TWO.equals(orderInfo.getOrderIsNew())) {
            reUpdate = new RefundOrder().setBillTime(now)
                    .setRefundNo(refundOrder.getRefundNo())
                    .setErpState(RefundErpStateEnum.WAIT_REFUND.getCode());
            refundOrderMapper.updateByRefundNo(reUpdate);
            refundOrder.setErpState(RefundErpStateEnum.WAIT_REFUND.getCode());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refundAfterPick(OrderInfo orderInfo, List<OrderDetailDomain> detailList,
                                RefundOrder refundOrder, List<RefundDetail> refundDetailList) {
        Map<String, RefundDetail> refundDetailMap = refundDetailList.stream().collect(Collectors.toMap(p->String.format("%s_%s", p.getErpCode(), p.getThirdDetailId()), p->p));
        List<OrderPickInfoReqDto> pickReqList = new ArrayList<>();
        if (RefundTypeEnum.PART.getCode().equals(refundOrder.getType())) {
            for (OrderDetailDomain detailTemp : detailList) {
                String key = String.format("%s_%s", detailTemp.getErpCode(), detailTemp.getThirdDetailId());
                if (refundDetailMap.containsKey(key)) {
                    // 退款中存在，删除拣货信息
                    if (OrderDetailStatusEnum.REFUND.getCode().equals(detailTemp.getStatus())) {
                        orderPickInfoMapper.deleteByDetailId(detailTemp.getId());
                        continue;
                    }
                    // 退货数量
                    int refundCount = refundDetailMap.get(key).getRefundCount();
                    Iterator<OrderPickInfo> iterator = detailTemp.getOrderPickInfoList().iterator();
                    while (iterator.hasNext()) {
                        if (refundCount <= 0) {
                            break;
                        }
                        OrderPickInfo pickInfo = iterator.next();
                        int pickCount = pickInfo.getCount();
                        if (pickCount > refundCount) {
                            // 当前批号满足退款数量
                            pickInfo.setCount(pickCount - refundCount);
                            // 修改拣货数量
                            orderPickInfoMapper.updateCountById(pickInfo);
                            break;
                        } else {
                            // 当前批号不满足退款数量，该批号所有药品移除。
                            refundCount -= pickInfo.getCount();
                            orderPickInfoMapper.deleteUpById(pickInfo.getId());
                            iterator.remove();
                        }
                    }
                    for (OrderPickInfo op : detailTemp.getOrderPickInfoList()) {
                        OrderPickInfoReqDto opRequest = buildPickInfoReqDto(op);
                        pickReqList.add(opRequest);
                    }
                } else {
                    if (OrderDetailStatusEnum.REPLACE.getCode().equals(detailTemp.getStatus())) {
                        continue;
                    }
                    if (OrderDetailStatusEnum.REFUND.getCode().equals(detailTemp.getStatus())) {
                        continue;
                    }
                    if (detailTemp.getOrderPickInfoList() != null) {
                        for (OrderPickInfo op : detailTemp.getOrderPickInfoList()) {
                            OrderPickInfoReqDto opRequest = buildPickInfoReqDto(op);
                            pickReqList.add(opRequest);
                        }
                    }
                }
            }
        } else {
            // 全部退款，取消所有拣货
            orderPickInfoMapper.deleteAllPickByOrderNo(orderInfo.getOrderNo());
        }

        OrderInfo orderUpdate = null;
        Map<String, OrderDetailDomain> detailListMap = detailList.stream().collect(Collectors.toMap(p -> String.format("%s_%s", p.getErpCode(), p.getThirdDetailId()), p -> p));
        // 退款时 那退款详情 订单号+erpcode 找到原单详情  如出现多个拿拣货号>退款数量那个商品
        List<OrderDetail> refundSourceList = new ArrayList<>();
        for (RefundDetail refundDetail : refundDetailList) {
            String key = String.format("%s_%s", refundDetail.getErpCode(), refundDetail.getThirdDetailId());
            OrderDetailDomain sourceOrderDetail = detailListMap.get(key);
            if (sourceOrderDetail != null) {
                OrderDetail orderDetail = new OrderDetail();
                orderDetail.setErpCode(refundDetail.getErpCode());
                orderDetail.setOrderNo(refundOrder.getOrderNo());
                orderDetail.setId(sourceOrderDetail.getId());
                orderDetail.setCommodityCount(refundDetail.getRefundCount());
                orderDetail.setRefundCount(0);
                refundSourceList.add(orderDetail);
            }
        }
        if (RefundTypeEnum.PART.getCode().equals(refundOrder.getType())) {
            if (CollUtil.isNotEmpty(refundSourceList)) {
                commodityStockService.unLockStockNew(orderInfo, refundSourceList,"部分退款后是释放库2",UnLockStockTypeEnum.PART);
            }
        }
        if (RefundTypeEnum.ALL.getCode().equals(refundOrder.getType())) {
            commodityStockService.unLockStockNew(orderInfo, null,"全额退款后是释放库2",UnLockStockTypeEnum.ALL);
        }
        //如果没有锁库存，则不需要解锁
        if(!Strings.isEmpty(orderInfo.getErpAdjustNo())){
            try{
                // 构建部分退款商品
                List<OrderDetail> releaseDetailList = new ArrayList<>();
                if(!CollectionUtils.isEmpty(detailList) && !CollectionUtils.isEmpty(pickReqList)){
                    Map<String,Integer> erpCodeGoodsTypeMap = detailList.stream().collect(Collectors.toMap(OrderDetailDomain::getErpCode,OrderDetailDomain::getGoodsType,(v1,v2)->v1));
                    releaseDetailList = pickReqList.stream().map(pickInfo -> {{
                        OrderDetail detailData = new OrderDetail();
                        detailData.setErpCode(pickInfo.getErpCode());
                        detailData.setCommodityCount(pickInfo.getCount());
                        detailData.setRefundCount(DsConstants.INTEGER_ZERO);
                        detailData.setGoodsType(erpCodeGoodsTypeMap.get(pickInfo.getErpCode()));
                        return detailData;
                    }}).collect(Collectors.toList());
                }
                // 【ID1019126】调用拣货接口解锁改为调用接单的解锁
                BaseErpResp<OrderReceiveResp> erpResult = erpCommonClient.lockStockNoBatchNo(orderInfo.getMerCode(), "", orderInfo, releaseDetailList,IntYesNoEnum.NO.getCode(),DsConstants.NEED_RETRY);
//                if (null == erpResult || null== erpResult.getData()
//                		|| Strings.isEmpty(erpResult.getData().getAdjustNo())){
//                    throw ExceptionUtil.getWarnException(DsErrorType.ERP_UNLOCK_STOCK_ERROR);
//                }

                orderInfo.setErpAdjustNo("");
                orderUpdate = new OrderInfo();
                orderUpdate.setOrderNo(orderInfo.getOrderNo());
                orderUpdate.setErpAdjustNo("");
            }catch (Exception e){
                log.error("lockStockNoBatchNum,error,orderNo:{},{}",orderInfo.getOrderNo(),e);
                // 向企业微信发送messageId
                String msg = "【解锁库存失败 】,merCode:"+orderInfo.getMerCode()+",orderNo:"+orderInfo.getOrderNo();
                enterpriseWeChatUtil.sendMsg(DsConstants.UNLOCK_ORDER_ERROR, "",orderInfo.getThirdOrderNo(),msg);
            }
        }
//        else {
//            if (RefundTypeEnum.PART.getCode().equals(refundOrder.getType())) {
//                if (CollUtil.isNotEmpty(refundSourceList)) {
////                    localStockService.unlockStock(orderInfo, refundSourceList, StockSourceEnum.REFUND_UNLOCK);
//                    commodityStockService.unlockStock(orderInfo, refundSourceList);
//                }
//            }
//            if (RefundTypeEnum.ALL.getCode().equals(refundOrder.getType())) {
////                localStockService.unlockStock(orderInfo, null, StockSourceEnum.REFUND_UNLOCK);
//                commodityStockService.unlockStock(orderInfo, refundSourceList);
//            }
//        }

        if (RefundTypeEnum.ALL.getCode().equals(refundOrder.getType())) {
            if (orderUpdate == null) {
                orderUpdate = new OrderInfo();
                orderUpdate.setOrderNo(orderInfo.getOrderNo());

            }
            orderUpdate.setErpState(ErpStateEnum.CANCELED.getCode());
        } else {
            if (OrderStateEnum.CLOSED.getCode().equals(orderInfo.getOrderState())
                    || OrderStateEnum.CANCEL.getCode().equals(orderInfo.getOrderState()) ) {
                if (orderUpdate == null) {
                    orderUpdate = new OrderInfo();
                    orderUpdate.setOrderNo(orderInfo.getOrderNo());
                }
                orderUpdate.setErpState(ErpStateEnum.CANCELED.getCode());
            }
        }

        if (DsConstants.STRING_TWO.equals(orderInfo.getOrderIsNew())) {
            if(orderUpdate == null) {
                orderUpdate = new OrderInfo();
                orderUpdate.setOrderNo(orderInfo.getOrderNo());
            }
            orderUpdate.setErpState(ErpStateEnum.WAIT_SALE.getCode());
        }

        if (orderUpdate != null) {
            log.info("orderUpdate ErpState:{},isPass:{}",orderUpdate.getErpState(),orderInfo.getOrderIsNew());
            orderInfoMapper.updateOrder(orderUpdate);
        }

        Date now = new Date();
        RefundOrder reUpdate = new RefundOrder().setBillTime(now)
                .setRefundNo(refundOrder.getRefundNo())
                .setErpState(RefundErpStateEnum.CANCELED.getCode());
        refundOrderMapper.updateByRefundNo(reUpdate);
        refundOrder.setErpState(RefundErpStateEnum.CANCELED.getCode());
        if (DsConstants.STRING_TWO.equals(orderInfo.getOrderIsNew())) {
            reUpdate = new RefundOrder().setBillTime(now)
                    .setRefundNo(refundOrder.getRefundNo())
                    .setErpState(RefundErpStateEnum.WAIT_REFUND.getCode());
            refundOrderMapper.updateByRefundNo(reUpdate);
            refundOrder.setErpState(RefundErpStateEnum.WAIT_REFUND.getCode());
        }
    }

    /**
     * 按批次号构建拣货单元对象
     */
    private OrderPickInfoReqDto buildPickInfoReqDto(OrderPickInfo pickInfo) {
       return  OrderPickInfoReqDto.builder()
               .orderDetailId(pickInfo.getOrderDetailId())
               .commodityBatchNo(pickInfo.getCommodityBatchNo())
               .count(pickInfo.getCount())
               .erpCode(pickInfo.getErpCode())
               .purchasePrice(pickInfo.getPurchasePrice())
               .build();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    @OrderLock
    public RefundOrderDomain createRefundSuccessful(OrderInfo orderInfo) {
        if( !ErpStateEnum.HAS_SALE.getCode().equals(orderInfo.getErpState())){
            return null;
        }
        RefundOrder refundOrderTest=refundOrderMapper.selectByOrderNo(orderInfo.getOrderNo());
        if(refundOrderTest != null){
            return null;
        }

        RefundOrderContext context = refundAllForCancelCreation.createRefundForOrderCancel(orderInfo);

        RefundOrderDomain refundOrderDomain = new RefundOrderDomain();
        refundOrderDomain.setRefundDetailList(context.getRefundDetailList());
        refundOrderDomain.setRefundOrder(context.getRefundOrder());

        return refundOrderDomain;
    }

    @Override
    public boolean orderRefundHasComplete(List<OrderDetailDomain> orderDetailList, List<RefundDetail> refundDetailList) {
        if (orderDetailList == null || refundDetailList == null){
            return false;
        }
        Set<String> erpCodeSet = orderDetailList.stream().map(OrderDetail::getErpCode).
                filter(p -> p != null).collect(Collectors.toSet());
        int size = 0;
        for (RefundDetail refundDetail: refundDetailList){
            if (erpCodeSet.contains(refundDetail.getErpCode())){
                size = size + 1;
            }
        }
        if (orderDetailList.size() == size){
            return true;
        }
        return false;
    }

    @Override
    public void saveRefundLog(RefundOrder refundOrder, String operatorId, String operateDesc, Integer state ,Integer erpState){
        RefundLog refundLog = new RefundLog();
        refundLog.setRefundNo(refundOrder.getRefundNo());
        refundLog.setOrderNo(refundOrder.getOrderNo());
        refundLog.setOperatorId(operatorId);
        refundLog.setOperateDesc(operateDesc);
        refundLog.setState(state);
        refundLog.setErpState(erpState);
        refundLogMapper.insert(refundLog);
    }

    @Override
    @DS(DsConstants.DB_ORDER_SLAVE)
    public Integer aliRefundBillButtonShow(String merCode, String userId, Long orderNo) {
        OrderInfo orderInfo = orderBasicService.getOrderBaseWithCheck(orderNo);
        //订单状态为已关闭或已取消，且下账状态为待锁定、待下账、已下账的才显示该按钮；
        boolean showFlag = OrderStateEnum.CLOSED.getCode().equals(orderInfo.getOrderState())||
                OrderStateEnum.CANCEL.getCode().equals(orderInfo.getOrderState());
        showFlag = showFlag && ( ErpStateEnum.WAIT_PICK.getCode().equals(orderInfo.getErpState())||
                ErpStateEnum.WAIT_SALE.getCode().equals(orderInfo.getErpState())||
                ErpStateEnum.HAS_SALE.getCode().equals(orderInfo.getErpState()) );
        showFlag = showFlag && (  PlatformCodeEnum.ALI_HEALTH.getCode().equals(orderInfo.getThirdPlatformCode())  );
        if(showFlag){
            return DsConstants.INTEGER_ONE;
        }else{
            return DsConstants.INTEGER_ZERO;
        }
    }

    @Override
    public Map<Long,Integer> ydjiaRefundButtonShow(List<SearchOrderRspDto> orderDataList) {
        List<Long> orderNoList = orderDataList.stream().map(SearchOrderRspDto::getOrderNo).collect(Collectors.toList());
        QueryWrapper<RefundOrder> refundOrderQueryWrapper = new QueryWrapper<>();
        refundOrderQueryWrapper.lambda().in(RefundOrder::getOrderNo, orderNoList);
		List<RefundOrder> refundOrderList = refundOrderMapper.selectList(refundOrderQueryWrapper);
		Map<Long,List<RefundOrder>> refundOrderMap = new HashMap<>();
		refundOrderMap = refundOrderList.stream().collect(Collectors.groupingBy(RefundOrder::getOrderNo));

		Map<Long,Integer> ydjiaRefundButtonShowMap = new HashMap<Long,Integer>();
        for (SearchOrderRspDto searchOrderRspDto : orderDataList) {
        	// 微商城主动退款按钮展示 默认0：不展示  1：展示
			if(PlatformCodeEnum.YD_JIA.getCode().equals(searchOrderRspDto.getThirdPlatformCode())
					&& !OrderStateEnum.CANCEL.getCode().equals(searchOrderRspDto.getOrderState())
					&& !OrderStateEnum.CLOSED.getCode().equals(searchOrderRspDto.getOrderState())
					&& CollectionUtils.isEmpty(refundOrderMap.get(searchOrderRspDto.getOrderNo()))) {
				ydjiaRefundButtonShowMap.put(searchOrderRspDto.getOrderNo(), DsConstants.INTEGER_ONE);
			}else {
				ydjiaRefundButtonShowMap.put(searchOrderRspDto.getOrderNo(), DsConstants.INTEGER_ZERO);
			}
		}
        return ydjiaRefundButtonShowMap;
    }

    @Override
    @DS(DsConstants.DB_ORDER_SLAVE)
    public Map<Long,Integer> orderHasRefund(List<Long> orderNoList){
    	QueryWrapper<RefundOrder> refundOrderQueryWrapper = new QueryWrapper<>();
        refundOrderQueryWrapper.lambda().in(RefundOrder::getOrderNo, orderNoList);
		List<RefundOrder> refundOrderList = refundOrderMapper.selectList(refundOrderQueryWrapper);
		Map<Long,List<RefundOrder>> refundOrderMap = new HashMap<>();
		refundOrderMap = refundOrderList.stream().collect(Collectors.groupingBy(RefundOrder::getOrderNo));
		Map<Long,Integer> ydjiaRefundButtonShowMap = new HashMap<Long,Integer>();
        for (Long orderNo : orderNoList) {
        	// 是否有退款单，0：无  1：有
			if(!CollectionUtils.isEmpty(refundOrderMap.get(orderNo))) {
				ydjiaRefundButtonShowMap.put(orderNo, DsConstants.INTEGER_ONE);
			}else {
				ydjiaRefundButtonShowMap.put(orderNo, DsConstants.INTEGER_ZERO);
			}
		}
        return ydjiaRefundButtonShowMap;
    }

    @Override
    public RefundOrder selectByOrderNoOrderByTime(Long orderNo) {
        return refundOrderMapper.selectByOrderNoOrderByTime(orderNo);
    }
}
