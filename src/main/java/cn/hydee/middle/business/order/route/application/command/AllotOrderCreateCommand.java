package cn.hydee.middle.business.order.route.application.command;

import cn.hydee.middle.business.order.dto.rsp.baseinfo.SysEmployeeResDTO;
import cn.hydee.middle.business.order.entity.OrderDetail;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.entity.OrderPickInfo;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/04/01 14:53
 **/
@Data
@AllArgsConstructor
public class AllotOrderCreateCommand {



    private OrderInfo orderInfo;

    private List<OrderDetail> orderDetail;

    private List<OrderPickInfo> orderPickInfo;
}
