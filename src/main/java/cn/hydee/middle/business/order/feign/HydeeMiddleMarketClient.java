package cn.hydee.middle.business.order.feign;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.dto.req.RetrieveRightReqDTO;
import cn.hydee.middle.business.order.dto.rsp.RetrieveRightResDTO;
import cn.hydee.starter.dto.ResponseBase;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(DsConstants.HYDEE_MIDDLE_MARKET)
public interface HydeeMiddleMarketClient {

  /**
   * 用药福利权益回收
   * @param retrieveRightReqDTO
   * @return
   */
  @PostMapping("/1.0/activityMedicalGift/manMade/retrieveRights")
  ResponseBase<RetrieveRightResDTO> retrieveRights(@RequestBody RetrieveRightReqDTO retrieveRightReqDTO);

}
