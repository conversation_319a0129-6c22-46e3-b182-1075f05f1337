package cn.hydee.middle.business.order.dto.fence;

import lombok.Data;

/**
 * 坐标点
 *
 * <AUTHOR>
 * @date 2024/07/31 10:32
 **/
@Data
public class Point {
    public final double longitude;
    public final double latitude;

    public Point(double longitude, double latitude) {
        this.longitude = longitude;
        this.latitude = latitude;
    }

    @Override
    public String toString() {
        return "(" + longitude + ", " + latitude + ")";
    }
}
