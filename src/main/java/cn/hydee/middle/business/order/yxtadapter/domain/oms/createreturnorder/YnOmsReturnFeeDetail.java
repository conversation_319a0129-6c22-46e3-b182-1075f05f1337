package cn.hydee.middle.business.order.yxtadapter.domain.oms.createreturnorder;

import lombok.Builder;
import lombok.Data;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR>
 * Date:  2023/9/2
 */
@Data
@Builder
public class YnOmsReturnFeeDetail implements java.io.Serializable{

    /**
     * 费用金额，单位元
     */
    private String AMOUNT;


    /**
     * 费用编码，支付编码
     *      运费POSTFEE
     *      优惠 COUPON
     *      药联直付 YLPAY
     *      柜台收银 GTSY
     */
    private String FEECODE;

    /**
     * 费用名称
     */
    private String FEENAME;
    /**
     * 费用类型（0：优惠信息；1：支付信息；2：运费、包装费等其他费用）
     */
    private String FEETYPE;
    /**
     * 交易流水号
     */
    private String PAYBATCHNO;


    /**
     * 第三方支付订单号
     */
    private String PAYODERENO;
    /**
     * 订单编号
     */
    private String RETURNCODE;



}
