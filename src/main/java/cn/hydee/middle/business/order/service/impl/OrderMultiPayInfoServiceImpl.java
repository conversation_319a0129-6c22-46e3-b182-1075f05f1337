package cn.hydee.middle.business.order.service.impl;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.Enums.PlatformCodeEnum;
import cn.hydee.middle.business.order.Enums.RefundTypeEnum;
import cn.hydee.middle.business.order.dto.message.NetRefundNotifyMessage;
import cn.hydee.middle.business.order.dto.req.AddOrderInfoReqDto;
import cn.hydee.middle.business.order.dto.rsp.MultiPayType;
import cn.hydee.middle.business.order.dto.rsp.RefundMultiPayType;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.entity.OrderMultiPayInfo;
import cn.hydee.middle.business.order.entity.RefundDetail;
import cn.hydee.middle.business.order.entity.RefundOrder;
import cn.hydee.middle.business.order.mapper.OrderInfoMapper;
import cn.hydee.middle.business.order.mapper.OrderMultiPayInfoMapper;
import cn.hydee.middle.business.order.mapper.RefundDetailMapper;
import cn.hydee.middle.business.order.mapper.RefundOrderMapper;
import cn.hydee.middle.business.order.refund.calc.ICalculateRefundSinkAmountService;
import cn.hydee.middle.business.order.refund.context.RefundOrderContext;
import cn.hydee.middle.business.order.service.OrderMultiPayInfoService;
import cn.hydee.middle.business.order.util.JsonUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 订单多支付关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-01
 */
@Service
@Slf4j
public class OrderMultiPayInfoServiceImpl extends ServiceImpl<OrderMultiPayInfoMapper, OrderMultiPayInfo> implements OrderMultiPayInfoService {
    
    private final OrderMultiPayInfoMapper orderMultiPayInfoMapper;

    private final RefundOrderMapper refundOrderMapper;

    private final OrderInfoMapper orderInfoMapper;

    private final RefundDetailMapper refundDetailMapper;

    @Autowired
    @Lazy
    private ICalculateRefundSinkAmountService calculateRefundSinkAmountService;

    public OrderMultiPayInfoServiceImpl(OrderMultiPayInfoMapper orderMultiPayInfoMapper,RefundOrderMapper refundOrderMapper,OrderInfoMapper orderInfoMapper,RefundDetailMapper refundDetailMapper) {
        this.orderMultiPayInfoMapper = orderMultiPayInfoMapper;
        this.refundOrderMapper = refundOrderMapper;
        this.orderInfoMapper = orderInfoMapper;
        this.refundDetailMapper = refundDetailMapper;
    }

    @Override
    public List<MultiPayType> queryPayList2Account(OrderInfo orderInfo){

//        if (!PlatformCodeEnum.YD_JIA.getCode().equals(orderInfo.getThirdPlatformCode())) {
//            return Collections.emptyList();
//        }
        List<OrderMultiPayInfo> orderMultiPayInfoList = orderMultiPayInfoMapper.selectListByOrderNoForBill(orderInfo.getOrderNo());
        if(CollectionUtils.isEmpty(orderMultiPayInfoList)){
            return Collections.emptyList();
        }
        List<MultiPayType> payDataList = orderMultiPayInfoList.stream().map(orderMultiPayInfo -> {
            MultiPayType payData = new MultiPayType();
            BeanUtils.copyProperties(orderMultiPayInfo,payData);
            return payData;
        }).collect(Collectors.toList());
        return payDataList;
    }

    @Override
    public List<RefundMultiPayType> queryPayList2Refund(RefundOrder refundOrder){

//        if (!PlatformCodeEnum.YD_JIA.getCode().equals(refundOrder.getThirdPlatformCode())) {
//            return Collections.emptyList();
//        }
        List<OrderMultiPayInfo> orderMultiPayInfoList = orderMultiPayInfoMapper.selectListByRefundNo(refundOrder.getRefundNo());
        if(CollectionUtils.isEmpty(orderMultiPayInfoList)){
            return Collections.emptyList();
        }
        List<RefundMultiPayType> payDataList = orderMultiPayInfoList.stream().map(orderMultiPayInfo -> {
            RefundMultiPayType payData = new RefundMultiPayType();
            BeanUtils.copyProperties(orderMultiPayInfo,payData);
            return payData;
        }).collect(Collectors.toList());
        return payDataList;
    }

    @Override
    public void saveMultiPay(OrderInfo orderInfo, AddOrderInfoReqDto addOrderInfoReqDto){
        try{
            Long orderNo = orderInfo.getOrderNo();
            if (!commonCheck(orderNo, orderInfo.getThirdPlatformCode(), addOrderInfoReqDto.getExtrainfo())) {
                return;
            }
            AddOrderInfoReqDto.ExtraInfo extraInfo = JsonUtil.json2Object(addOrderInfoReqDto.getExtrainfo(), AddOrderInfoReqDto.ExtraInfo.class);
            List<AddOrderInfoReqDto.PayType> payDataList = extraInfo.getPayTypeList();
            if(CollectionUtils.isEmpty(payDataList)){
                return;
            }
            List<OrderMultiPayInfo> dataList = buildOrderNoBean(payDataList,orderNo);
            saveUpdateBatch(dataList);
        }catch (Exception e){
            log.info("saveMultiPay error,orderNo:{}",orderInfo.getOrderNo(),e);
        }
    }

    @Override
    public void saveMultiPayRefundByClientC(RefundOrder refundOrder, NetRefundNotifyMessage refundNotifyMessage){
        try{
            Long orderNo = refundOrder.getOrderNo();
            if (!commonCheck(orderNo, refundOrder.getThirdPlatformCode(), refundNotifyMessage.getExtrainfo())) {
                return;
            }
            Long refundNo = refundOrder.getRefundNo();
            if (null == refundNo) {
                return;
            }
            NetRefundNotifyMessage.ExtraInfo extraInfo = JsonUtil.json2Object(refundNotifyMessage.getExtrainfo(), NetRefundNotifyMessage.ExtraInfo.class);
            List<NetRefundNotifyMessage.PayType> payDataList = extraInfo.getPayTypeList();
            if(CollectionUtils.isEmpty(payDataList)){
                return;
            }
            List<OrderMultiPayInfo> dataList = buildRefundBean(payDataList,orderNo,refundNo);
            saveUpdateBatch(dataList);
        }catch (Exception e){
            log.info("saveMultiPayRefund error,orderNo:{},refundNo:{}",refundOrder.getOrderNo(),refundOrder.getRefundNo(),e);
        }
    }

    @Override
    public void saveMultiPayRefundByClientB(RefundOrder refundOrder){
        try{
            Long orderNo = refundOrder.getOrderNo();
            if (null == orderNo) {
                return;
            }
            if(!RefundTypeEnum.ALL.getCode().equals(refundOrder.getType())){
                return;
            }
            Long refundNo = refundOrder.getRefundNo();
            if (null == refundNo) {
                return;
            }
            List<OrderMultiPayInfo> orderDataList = orderMultiPayInfoMapper.selectListByOrderNoForBill(orderNo);
            if(CollectionUtils.isEmpty(orderDataList)){
                return;
            }
            List<OrderMultiPayInfo> refundDataList = buildRefundBeanByOrder(orderDataList, orderNo, refundNo);
            saveUpdateBatch(refundDataList);
        }catch (Exception e){
            log.info("saveMultiPayRefundByClientB error,orderNo:{},refundNo:{}",refundOrder.getOrderNo(),refundOrder.getRefundNo(),e);
        }
    }


    @Override
    public void saveUpdateBatch(List<OrderMultiPayInfo> dataList){
        // 过滤金额为0的支付方式
        /*dataList = dataList.stream().filter(data -> BigDecimal.ZERO.compareTo(data.getPayPrice()) < 0).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(dataList)){
            throw ExceptionUtil.getWarnException(DsErrorType.PARAM_VALID_ERROR.getCode() ,"多支付方式金额不能全都为0");
        }*/
        List<Long> orderNoList = dataList.stream().map(OrderMultiPayInfo::getOrderNo).collect(Collectors.toList());
        orderNoList = orderNoList.stream().distinct().collect(Collectors.toList());
        QueryWrapper<OrderMultiPayInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(OrderMultiPayInfo::getOrderNo,orderNoList);
        List<OrderMultiPayInfo> existDataList = orderMultiPayInfoMapper.selectList(queryWrapper);
        if(CollectionUtils.isEmpty(existDataList)){
            existDataList = Collections.emptyList();
        }
        Map<String,OrderMultiPayInfo> orderNoOrderMultiPayInfoMap = existDataList.stream().collect(Collectors.toMap(OrderMultiPayInfo::uniqueKey, a->a,(v1, v2)->v1));
        List<OrderMultiPayInfo> updateDataList = dataList.stream().filter(data->orderNoOrderMultiPayInfoMap.containsKey(data.uniqueKey())).collect(Collectors.toList());
        List<OrderMultiPayInfo> insertDataList = dataList.stream().filter(data->!orderNoOrderMultiPayInfoMap.containsKey(data.uniqueKey())).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(updateDataList)){
            updateDataList.forEach(data->{
                data.setId(orderNoOrderMultiPayInfoMap.get(data.uniqueKey()).getId());
                data.setModifyTime(new Date());
            });
            updateBatchById(updateDataList);
        }
        if(!CollectionUtils.isEmpty(insertDataList)){
            insertDataList.forEach(data->{
                data.setCreateTime(new Date());
            });
            saveBatch(insertDataList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refundChangePayTypeData(RefundOrder refundOrder,List<NetRefundNotifyMessage.PayType> payDataList,BigDecimal commissionFee){
        if(!PlatformCodeEnum.YD_JIA.getCode().equals(refundOrder.getThirdPlatformCode())){
            return;
        }
        if (null != commissionFee && commissionFee.compareTo(refundOrder.getFeeRefund()) != DsConstants.INTEGER_ZERO) {
            //更新退款单拥挤信息
            RefundOrder upRefundOrder = new RefundOrder();
            upRefundOrder.setRefundNo(refundOrder.getRefundNo());
            upRefundOrder.setFeeRefund(commissionFee);
            upRefundOrder.setTotalAmount(refundOrder.getTotalAmount().subtract(refundOrder.getFeeRefund() == null ? BigDecimal.ZERO : refundOrder.getFeeRefund())
                    .add(refundOrder.getPlatformDiscountRefund() == null ? BigDecimal.ZERO : refundOrder.getPlatformDiscountRefund()));
            refundOrderMapper.updateByRefundNo(upRefundOrder);
            //刷新下账金额信息  orderInfo、refundOrder会重新获取
            refreshRefundSinkAmount(null,refundOrder);
        }

        if(CollectionUtils.isEmpty(payDataList)){
            return;
        }
        List<OrderMultiPayInfo> multiPayInfoList = buildRefundBean(payDataList,refundOrder.getOrderNo(),refundOrder.getRefundNo());
        // 退款审核的最终支付方式覆盖原有支付，PS：因为商城下发不了原支付，改成下游覆盖原始数据
        // 删除数据
        QueryWrapper<OrderMultiPayInfo> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.lambda().eq(OrderMultiPayInfo::getRefundNo,multiPayInfoList.get(0).getRefundNo());
        int delNum = baseMapper.delete(deleteWrapper);
        // 兼容微商城单支付下发后发生产导致的多支付数据场景：原单无支付方式时退款审核也不接支付方式
        if(delNum <= 0){
            return;
        }
        // 录入数据
        saveBatch(multiPayInfoList);
    }

    private void refreshRefundSinkAmount(OrderInfo orderInfo,RefundOrder refundOrder){
        RefundOrderContext context = new RefundOrderContext();
        if(null == orderInfo){
            orderInfo = orderInfoMapper.selectOrderInfo(refundOrder.getOrderNo());
        }
        context.setOrderInfo(orderInfo);
        refundOrder = refundOrderMapper.selectByRefundNo(refundOrder.getRefundNo());
        List<RefundDetail> refundDetailList = refundDetailMapper.selectListByRefundNo(refundOrder.getRefundNo());
        context.setRefundOrder(refundOrder);
        context.setRefundDetailList(refundDetailList);
        calculateRefundSinkAmountService.refreshRefundSinkAmount(context);
        refundDetailMapper.batchUpdateSinkAmountByRefundNo(refundDetailList);
    }

    private List<OrderMultiPayInfo> buildOrderNoBean(List<AddOrderInfoReqDto.PayType> payDataList,Long orderNo){
        return payDataList.stream().map(payData -> {
            OrderMultiPayInfo payInfo = new OrderMultiPayInfo();
            payInfo.setOrderNo(orderNo);
            payInfo.setPayCode(payData.getPayCode());
            payInfo.setPayName(payData.getPayName());
            payInfo.setPayPrice(Optional.ofNullable(payData.getPayPrice()).orElse(BigDecimal.ZERO));
            return payInfo;
        }).collect(Collectors.toList());
    }

    private List<OrderMultiPayInfo> buildRefundBean(List<NetRefundNotifyMessage.PayType> payDataList,Long orderNo,Long refundNo){
        return payDataList.stream().map(payData -> {
            OrderMultiPayInfo payInfo = new OrderMultiPayInfo();
            payInfo.setOrderNo(orderNo);
            payInfo.setRefundNo(refundNo);
            payInfo.setPayCode(payData.getPayCode());
            payInfo.setPayName(payData.getPayName());
            payInfo.setPayPrice(Optional.ofNullable(payData.getPayPrice()).orElse(BigDecimal.ZERO));
            return payInfo;
        }).collect(Collectors.toList());
    }

    private List<OrderMultiPayInfo> buildRefundBeanByOrder(List<OrderMultiPayInfo> orderDataList,Long orderNo, Long refundNo) {
        return orderDataList.stream().map(orderMultiPayInfo -> {
            OrderMultiPayInfo payInfo = new OrderMultiPayInfo();
            payInfo.setOrderNo(orderNo);
            payInfo.setRefundNo(refundNo);
            payInfo.setPayCode(orderMultiPayInfo.getPayCode());
            payInfo.setPayName(orderMultiPayInfo.getPayName());
            payInfo.setPayPrice(Optional.ofNullable(orderMultiPayInfo.getPayPrice()).orElse(BigDecimal.ZERO));
            return payInfo;
        }).collect(Collectors.toList());
    }

    private Boolean commonCheck(Long orderNo, String thirdPlatformCode, String extraInfo) {
        if (null == orderNo) {
            return Boolean.FALSE;
        }
//        if (!PlatformCodeEnum.YD_JIA.getCode().equals(thirdPlatformCode)) {
//            return Boolean.FALSE;
//        }
        if (StringUtils.isEmpty(extraInfo)) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    @Override
    public List<OrderMultiPayInfo> queryOriginalPayTypeListByOrderNo(Long orderNo) {
        QueryWrapper<OrderMultiPayInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OrderMultiPayInfo::getOrderNo,orderNo).isNull(OrderMultiPayInfo::getRefundNo);
        return orderMultiPayInfoMapper.selectList(queryWrapper);
    }
}
