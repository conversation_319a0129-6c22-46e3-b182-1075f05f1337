package cn.hydee.middle.business.order.storeautosync.facade.storeconfig.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hydee.middle.business.order.storeautosync.domain.storeconfig.StoreAutoConfigManager;
import cn.hydee.middle.business.order.storeautosync.facade.storeconfig.entity.StoreAutoConfigSyncMessage;
import cn.hydee.middle.business.order.storeautosync.facade.storeconfig.entity.StoreConfigExecuteRequest;
import cn.hydee.middle.business.order.storeautosync.facade.storeconfig.entity.StoreConfigRepairRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class StoreAutoConfigServiceImpl implements StoreAutoConfigService {

    @Autowired
    private StoreAutoConfigManager storeAutoConfigManager;

    @Override
    public void handleStoreAutoConfigSyncMessage(List<StoreAutoConfigSyncMessage> dataList) {
        if (CollUtil.isEmpty(dataList)) {
            return;
        }
        for (StoreAutoConfigSyncMessage data : dataList) {
            if (StrUtil.isBlank(data.getHandlerName()) || StrUtil.isBlank(data.getOnlineStoreId())) {
                continue;
            }
            storeAutoConfigManager.handleStoreAutoConfigSyncMessage(data);
        }
    }

    @Override
    public void storeConfigRepair(StoreConfigRepairRequest request) {

        storeAutoConfigManager.storeConfigRepair(request);

    }

    @Override
    public void storeConfigExecute(StoreConfigExecuteRequest request) {
        storeAutoConfigManager.storeConfigExecute(request);
    }
}
