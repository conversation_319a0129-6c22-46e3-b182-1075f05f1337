package cn.hydee.middle.business.order.batch.export.dto;

import cn.hydee.middle.business.order.dto.req.PageDeliveryFeeEconomizeReqDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class DeliveryFeeExportEconomizeReqDTO extends PageDeliveryFeeEconomizeReqDto {

  private String merCode;

  private String userId;

  //配送费节约记录导出传0 走doZipExport
  @ApiModelProperty(value="下载类型:0-订单，1-处方图片")
  private Integer downloadType;

}
