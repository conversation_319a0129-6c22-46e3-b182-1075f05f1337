package cn.hydee.middle.business.order.omsauth.dto.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class OmsAuthTimeInfo implements Serializable {

    private static final long serialVersionUID = -8651926438830380237L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @ApiModelProperty(value = "商户编码")
    private String merCode;
    @ApiModelProperty(value = "三方平台编码")
    private String platformCode;
    @ApiModelProperty(value = "服务模式")
    private String serviceMode;
    @ApiModelProperty(value = "运营平台套餐到期名称")
    private String appCode;
    @ApiModelProperty(value = "运营平台套餐状态")
    private Integer appStatus;
    @ApiModelProperty(value = "套餐有效期开始时间")
    private Date startTime;
    @ApiModelProperty(value = "套餐有效期结束时间")
    private Date endTime;

    private Date createTime;

    private Date modifyTime;

}
