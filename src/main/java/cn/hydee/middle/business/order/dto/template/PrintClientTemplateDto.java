package cn.hydee.middle.business.order.dto.template;

import cn.hydee.middle.business.order.Enums.DsConstants;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/04/13
 */
@Data
public class PrintClientTemplateDto implements Serializable {

    private static final long serialVersionUID = -4380535043951587240L;

    @JsonProperty("printtype")
    private int printType;

    @JsonProperty("drawingsprinting")
    private int drawingSprinting;

    @JsonProperty("pagewidth")
    private int pageWidth;

    @JsonProperty("pageheight")
    private int pageHeight;

    @JsonProperty("iscolor")
    private int isColor;

    private int left;
    private int right;
    private int top;
    private int bottom;

    @JsonProperty("printtofile")
    private int printToFile;
    @JsonProperty("fileextension")
    private String fileExtension;

    @JsonProperty("fontstyles")
    private FontStyle fontStyles;

    @JsonProperty("printdata")
    private List<PrintData> printData;

    @Data
    @ToString
    public static class FontStyle{
        @JsonProperty("familyname")
        private String familyName;

        @JsonProperty("fontsize")
        private int fontSize;

        private int bold;
        private int italic;
        private int underline;
        private int strikeout;
    }

    @Data
    @ToString
    public static class PrintData{
        @JsonProperty("rownum")
        private int rowNum;

        @JsonProperty("columnnum")
        private int columnNum;

        private String alignment;
        private int width;
        private int height;

        @JsonProperty("maxlen")
        private int maxLen;

        @JsonProperty("dataformat")
        private String dataFormat;

        @JsonProperty("datapropertyname")
        private String dataPropertyName;

        private int datatype;

        @JsonProperty("grouppropertyname")
        private String groupPropertyName;

        @JsonProperty("fontstyles")
        private FontStyle fontStyles;

        @JsonProperty("textwrap")
        private int textWrap;

        public void buildFontStyleDefault(){
            FontStyle fontStyle = new FontStyle();
            fontStyle.setFamilyName("微软雅黑");
            fontStyle.setFontSize(DsConstants.INTEGER_EIGHT);
            fontStyle.setBold(DsConstants.INTEGER_ZERO);
            fontStyle.setItalic(DsConstants.INTEGER_ZERO);
            fontStyle.setUnderline(DsConstants.INTEGER_ZERO);
            fontStyle.setStrikeout(DsConstants.INTEGER_ZERO);
            this.setFontStyles(fontStyle);
        }
    }

}
