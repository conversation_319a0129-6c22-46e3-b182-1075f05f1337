package cn.hydee.middle.business.order.controller;

import cn.hydee.middle.business.order.Enums.DsErrorType;
import cn.hydee.middle.business.order.Enums.DsOrderUploadConstants;
import cn.hydee.middle.business.order.account.check.base.dto.req.QuerySecondReqDto;
import cn.hydee.middle.business.order.account.check.base.enums.CheckResultEnum;
import cn.hydee.middle.business.order.account.check.export.AccountCheckMergeStrategy;
import cn.hydee.middle.business.order.account.check.export.DiffDataDto;
import cn.hydee.middle.business.order.account.check.service.IAccountCheckService;
import cn.hydee.middle.business.order.dto.OrderQuerySearchReqDto;
import cn.hydee.middle.business.order.dto.req.OrderCountSearchReqDto;
import cn.hydee.middle.business.order.dto.req.RefundPageReqDto;
import cn.hydee.middle.business.order.dto.req.VerifyReqDto;
import cn.hydee.middle.business.order.dto.rsp.OssOrderFileRspDto;
import cn.hydee.middle.business.order.dto.rsp.VerifyRspDto;
import cn.hydee.middle.business.order.mapper.OrderInfoMapper;
import cn.hydee.middle.business.order.mapper.RefundOrderMapper;
import cn.hydee.middle.business.order.service.OrderInfoService;
import cn.hydee.middle.business.order.service.OssOrderFileService;
import cn.hydee.middle.business.order.service.RefundService;
import cn.hydee.middle.business.order.service.VerifyService;
import cn.hydee.middle.business.order.util.AliOssUtil;
import cn.hydee.middle.business.order.util.ExceptionUtil;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.util.DateUtil;
import cn.hydee.starter.util.UUIDUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

/**
 * cn.hydee.middle.business.order.controller
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/4/1 15:05
 **/
@RestController
@RequestMapping("/${api.version}/ds/order/file")
@Api(tags="订单列表导出/导入")
@Slf4j
public class OssFileController extends AbstractController {

    @Autowired
    private OrderInfoService orderInfoService;

    @Autowired
    private OssOrderFileService ossOrderFileService;

    @Autowired
    private RefundService refundService;

    @Autowired
    private VerifyService verifyService;

    @Autowired
    private OrderInfoMapper  orderInfoMapper;
    @Autowired
    private RefundOrderMapper refundOrderMapper;
    @Autowired
    private IAccountCheckService accountCheckService;

    @Value("${spring.application.name}")
    protected String appName;

    @Value("${domain.name:order}")
    protected String domainName;

    private static final String SLASH = "/";

    private static final String EXPORT = "export";

    @PostMapping("/upload/{fileName}")
    @ApiOperation(value="订单列表导出数据,本接口返回fileKey以及访问路径",notes="订单列表导出数据")
    public ResponseBase<OssOrderFileRspDto> getOrderListDataFile(@RequestHeader("userId") String userId,
                                                                 @RequestHeader("merCode") String merCode,
                                                                 @Valid @RequestBody OrderQuerySearchReqDto searchOrderPageReqDto){
        log.info("OssFileController: get message merCode:{} userId:{},data:{}",userId,merCode, JSON.toJSON(searchOrderPageReqDto));
        if(searchOrderPageReqDto.getOrderState()!=null && searchOrderPageReqDto.getOrderState().getOrderStateList().isEmpty()){
             searchOrderPageReqDto.setOrderState(null);
        }
        searchOrderPageReqDto.setCurrentPage(1);
        searchOrderPageReqDto.setPageSize(2001);
        searchOrderPageReqDto.setMerCode(merCode);
        OssOrderFileRspDto rsp = ossOrderFileService.getOrderFileUrl(userId,merCode,searchOrderPageReqDto);
        return generateSuccess(rsp);
    }


    @PostMapping("upload/refundList")
    @ApiOperation(value="退款单数据列表导出数据,本接口返回fileKey以及访问路径",notes="退款单数据列表导出数据")
    public ResponseBase<OssOrderFileRspDto> getRefundOrderListFileUrl(@RequestHeader("merCode") String merCode,
                                                                      @RequestHeader("userId") String userId,
                                                                      @Valid @RequestBody RefundPageReqDto refundPageReqDto) throws IOException{
        refundPageReqDto.setMerCode(merCode);
        return generateSuccess(ossOrderFileService.getRefundOrderListFileUrl(merCode,userId,refundPageReqDto));
    }

    @PostMapping("upload/orderStatistics")
    @ApiOperation(" 订单统计查询导出")
    public ResponseBase<OssOrderFileRspDto> orderStatisticsExport(
            @RequestHeader("userId") String userId,
            @RequestHeader("merCode") String merCode,
            @RequestBody @Valid OrderCountSearchReqDto reqDto) {
        reqDto.setMerCode(merCode);
        reqDto.setUserId(userId);
        List<String> orgList = verifyService.verifyOrgList(merCode, userId, reqDto.getOrganizationCodeList());
        if (CollectionUtils.isEmpty(orgList)) {
            return generateSuccess(new OssOrderFileRspDto());
        }
        reqDto.setOrganizationCodeList(orgList);
        OssOrderFileRspDto ossOrderFileRspDto=new OssOrderFileRspDto().setFileKey("").setFileUrl("");
        try {
            ossOrderFileRspDto=ossOrderFileService.getOrderStatisticListFileUrl(merCode,userId,reqDto);
        } catch (Exception e) {
            throw ExceptionUtil.getWarnException(DsErrorType.UPLOAD_STORE_ORDER_INFO_ERROR);
        }
        return generateSuccess(ossOrderFileRspDto);
    }

    @PostMapping("delete")
    @ApiOperation(value="根据fileKey，删除文件",notes="根据fileKey，删除文件")
    public ResponseBase<String> deleteOrderListDataFile(@RequestParam("fileKey") String fileKey){
        return generateSuccess(ossOrderFileService.deleteOrderListFileByUrl(fileKey));
    }

    @PostMapping("fileUrl")
    @ApiOperation(value="根据fileKey，获取访问路径",notes="根据fileKey，获取访问路径")
    public ResponseBase<String> deleteOrderListDataFileUrl(@RequestParam("fileKey") String fileKey){
        String url = "";
        url=ossOrderFileService.getOrderListFileUrlByFileKey(fileKey);
        return generateSuccess(url);
    }

    @PostMapping("export/accountCheck/diffData")
    @ApiOperation(value="导出对账单差异数据,本接口返回fileKey以及访问路径",notes="导出对账单差异数据")
    public ResponseBase<OssOrderFileRspDto> getAccountCheckDiffDataFileUrl(
    		@RequestHeader("merCode") String merCode,
    		@RequestHeader("userId") String userId,
    		@RequestBody QuerySecondReqDto query) throws IOException{
		query.setMerCode(merCode);
		if(StringUtils.isEmpty(query.getOrganizationCode())){
            throw ExceptionUtil.getWarnException(DsErrorType.ORGANIZATION_CODE_IS_NULL);
        }
		if(StringUtils.isEmpty(query.getPlatformCode())){
            throw ExceptionUtil.getWarnException(DsErrorType.PARAM_VALID_ERROR.getCode(),
            		DsErrorType.PARAM_VALID_ERROR.getMsg()+",平台编码为空");
        }
		if(StringUtils.isEmpty(query.getOnlineStoreCode())){
            throw ExceptionUtil.getWarnException(DsErrorType.PARAM_VALID_ERROR.getCode(),
            		DsErrorType.PARAM_VALID_ERROR.getMsg()+",门店编码为空");
        }
        VerifyRspDto verifyDto = verifyService.verifyTimeAndOrganization(VerifyReqDto.builder()
                .merCode(query.getMerCode())
                .organizationCode(query.getOrganizationCode())
                .userId(userId)
                .storeFlag(0)
                .verifyFlag(1)
                .build());
        query.setOrganizationCodeList(verifyDto.getOrganizatioinList());
        query.setDataTypeList(Collections.singletonList(CheckResultEnum.AMOUNT_DIFF.getCode()));
        
		 //获取数据源
        List<DiffDataDto> data = accountCheckService.getDiffDataForExport(query);
        if(CollectionUtils.isEmpty(data)) {
        	throw ExceptionUtil.getWarnException(DsErrorType.PARAM_VALID_ERROR.getCode(),
            		DsErrorType.PARAM_VALID_ERROR.getMsg()+",待导出数据为空");
        }
		if(DsOrderUploadConstants.ONE_SHEET_MAX_SIZE < (data.size()/3)){
            throw ExceptionUtil.getWarnException(DsErrorType.PARAM_VALID_ERROR.getCode(),
            		DsErrorType.PARAM_VALID_ERROR.getMsg()+",导出数据过多，请调整导出条件，目前数量："+data.size());
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        //返回访问地址
        OssOrderFileRspDto ossOrderFileRspDto = new OssOrderFileRspDto();
        InputStream inputStream = null;
        try {
            EasyExcel//将数据映射到DownloadDTO实体类并响应到浏览器
                    .write(out, DiffDataDto.class)
                    //07的excel版本,节省内存
                    .excelType(ExcelTypeEnum.XLSX)
                    //是否自动关闭输入流
                    .autoCloseStream(Boolean.FALSE)
                    .registerWriteHandler(new AccountCheckMergeStrategy(AccountCheckMergeStrategy.generateCellRangeAddressList(data.size())))
                    .sheet().doWrite(data);
            inputStream = new ByteArrayInputStream(out.toByteArray());
            //上传至oss文件服务
//            String fileKey = UUID.randomUUID().toString().replaceAll("-", "") + ExcelTypeEnum.XLSX.getValue();
            String fileKey = domainName + SLASH
                + appName + SLASH
                + EXPORT + SLASH
                + DateUtil.format(new Date(),"yyyyMM") + SLASH
                + "导出对账单差异数据"  + "-" + DateUtil.getTodayString() + "-" + UUIDUtil.generateUuid()
                + ExcelTypeEnum.XLSX.getValue();
            AliOssUtil.upload(inputStream, fileKey);
            ossOrderFileRspDto.setFileKey(fileKey);
            ossOrderFileRspDto.setFileUrl(AliOssUtil.downloadURL(fileKey));
        }finally {
		    if(null != inputStream){
		        try{
                    inputStream.close();
                }catch (Exception e){
                    log.error(e.getMessage());
                }
            }
            if(null != out) {
		        try {
                    out.close();
                }catch (Exception e){
                    log.error(e.getMessage());
                }
            }
        }
        return generateSuccess(ossOrderFileRspDto);
    }
}
