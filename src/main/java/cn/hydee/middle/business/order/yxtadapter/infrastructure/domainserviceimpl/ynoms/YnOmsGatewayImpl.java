package cn.hydee.middle.business.order.yxtadapter.infrastructure.domainserviceimpl.ynoms;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import cn.hydee.middle.business.order.Enums.*;
import cn.hydee.middle.business.order.Enums.aftersale.AfterSaleTypeEnum;
import cn.hydee.middle.business.order.Enums.aftersale.RefundAuditType;
import cn.hydee.middle.business.order.dto.XyOrderCreatFacade;
import cn.hydee.middle.business.order.dto.req.AddOrderInfoReqDto;
import cn.hydee.middle.business.order.dto.req.StockDeductReqDto;
import cn.hydee.middle.business.order.dto.rsp.OnlineStoreInfoRspDto;
import cn.hydee.middle.business.order.dto.rsp.OrderInfoPageRsp;
import cn.hydee.middle.business.order.dto.rsp.StockDeductRspDto;
import cn.hydee.middle.business.order.entity.*;
import cn.hydee.middle.business.order.feign.MiddleMerchandiseClient;
import cn.hydee.middle.business.order.mapper.*;
import cn.hydee.middle.business.order.service.CommodityExceptionOrderService;
import cn.hydee.middle.business.order.service.CommodityStockService;
import cn.hydee.middle.business.order.service.LocalStockService;
import cn.hydee.middle.business.order.service.OrderInfoService;
import cn.hydee.middle.business.order.service.async.HydeeEsSyncClientAsync;
import cn.hydee.middle.business.order.service.cache.BaseInfoCache;
import cn.hydee.middle.business.order.util.ExceptionUtil;
import cn.hydee.middle.business.order.util.RedisKeyUtil;
import cn.hydee.middle.business.order.util.SpringBeanUtils;
import cn.hydee.middle.business.order.util.redis.RedisLockUtil;
import cn.hydee.middle.business.order.v2.feign.hems.HemsCommonClient;
import cn.hydee.middle.business.order.v2.manager.CommodityHandlerManager;
import cn.hydee.middle.business.order.v2.manager.DeliveryOutManager;
import cn.hydee.middle.business.order.v2.manager.DsOnlineStoreManager;
import cn.hydee.middle.business.order.v2.manager.OrderSingleQueryManager;
import cn.hydee.middle.business.order.v2.manager.base.BaseInfoManager;
import cn.hydee.middle.business.order.v2.manager.base.OrderDeliveryLogManager;
import cn.hydee.middle.business.order.v2.manager.base.OrderInfoManager;
import cn.hydee.middle.business.order.yxtadapter.constant.RobotEnumExample;
import cn.hydee.middle.business.order.yxtadapter.domain.oms.callback.delivery.YnOmsDeliveryCallBackRequest;
import cn.hydee.middle.business.order.yxtadapter.domain.oms.callback.delivery.YnOmsDeliveryCallBackResponse;
import cn.hydee.middle.business.order.yxtadapter.domain.oms.callback.orderstatus.YnOmsOrderStatusCallBackRequest;
import cn.hydee.middle.business.order.yxtadapter.domain.oms.callback.orderstatus.YnOmsOrderStatusCallBackResponse;
import cn.hydee.middle.business.order.yxtadapter.domain.oms.cancelorder.YnOmsCancelOrderRequestCommon;
import cn.hydee.middle.business.order.yxtadapter.domain.oms.cancelorder.YnOmsCancelOrderResponseCommon;
import cn.hydee.middle.business.order.yxtadapter.domain.oms.createorder.YnOmsOrderRequestCommon;
import cn.hydee.middle.business.order.yxtadapter.domain.oms.createorder.YnOmsOrderResponseCommon;
import cn.hydee.middle.business.order.yxtadapter.domain.oms.createreturnorder.YnOmsReturnOrderRequestCommon;
import cn.hydee.middle.business.order.yxtadapter.domain.oms.createreturnorder.YnOmsReturnOrderResponseCommon;
import cn.hydee.middle.business.order.yxtadapter.domain.oms.getorderstatus.YnOmsGetOrderStatusRequest;
import cn.hydee.middle.business.order.yxtadapter.domain.oms.getorderstatus.YnOmsGetOrderStatusResponse;
import cn.hydee.middle.business.order.yxtadapter.domain.oms.pushorderstatus.YnOmsPushOrderStatusRequest;
import cn.hydee.middle.business.order.yxtadapter.domain.oms.pushorderstatus.YnOmsPushOrderStatusResponse;
import cn.hydee.middle.business.order.yxtadapter.domainservice.ynoms.YnOmsGateway;
import cn.hydee.middle.business.order.yxtadapter.infrastructure.domainserviceimpl.util.OmsMsgEncryptedUtil;
import cn.hydee.middle.business.order.yxtadapter.infrastructure.domainserviceimpl.ynoms.convert.YnOmsCreateOrderConvert;
import cn.hydee.middle.business.order.yxtadapter.infrastructure.feign.OmsInterfaceClient;
import cn.hydee.starter.dto.ErrorType;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.unified.model.HemsBaseData;
import cn.hydee.unified.model.order.*;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.yxt.common.wechatrobot.callback.ExampleCallBack;
import com.yxt.common.wechatrobot.util.WxRobotOkHttpUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.client.RestClientException;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR>
 * Date:  2023/9/4
 */
@Slf4j
@Component
public class YnOmsGatewayImpl implements YnOmsGateway {

    @Autowired
    private OrderPickInfoMapper orderPickInfoMapper;


    @Autowired
    private ErpBillInfoMapper erpBillInfoMapper;

    @Autowired
    private OrderDeliveryAddressMapper orderDeliveryAddressMapper;
    @Autowired
    private OrderPayInfoMapper orderPayInfoMapper;
    @Autowired
    private CommodityHandlerManager commodityHandlerManager;
    @Resource
    OmsInterfaceClient ynOmsClient;

    @Autowired
    DsOnlineStoreManager dsOnlineStoreManager;

    @Resource
    private OrderDeliveryRecordMapper orderDeliveryRecordMapper;

    @Resource
    private OrderInfoMapper orderInfoMapper;

    @Resource
    private RefundOrderMapper refundOrderMapper;

    @Resource
    private ErpRefundInfoMapper erpRefundInfoMapper;

    @Resource
    private OrderSingleQueryManager orderSingleQueryManager;

    @Resource
    private OrderInfoManager orderInfoManager;

    @Resource
    private RefundDetailMapper refundDetailMapper;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private OrderGiftInfoMapper orderGiftInfoMapper;
    @Resource
    private OrderDeliveryLogManager orderDeliveryLogManager;

    @Resource
    private OrderPrescriptionMapper orderPrescriptionMapper;
    @Resource
    private OrderDeliveryLogMapper orderDeliveryLogMapper;

    @Autowired
    private OrderInfoService orderInfoService;

    /**
     * 推送订单状态 给雨诺 OMS系统
     *
     * @param req
     */
    @SneakyThrows
    @Override
    public void createOrder(AddOrderInfoReqDto req) {
        String oldOrderNo = req.getOlorderno();
        OrderInfo orderInfo = orderInfoMapper.selectOrderByThirdOrderNo(oldOrderNo);
        String extendInfo = StringUtils.isNotBlank(orderInfo.getExtendInfo()) ? orderInfo.getExtendInfo() : "{}";
        OrderInfoExtendInfo orderInfoExtendInfo = JSON.parseObject(extendInfo, OrderInfoExtendInfo.class);
        if (StringUtils.isNotBlank(orderInfoExtendInfo.getOrderTransportYN()) && "1".equals(orderInfoExtendInfo.getOrderTransportYN())) {
            log.info(" YnOmsGatewayImpl createOrder   req:{}, miss msg:{}", JSON.toJSONString(req), "该订单已经推送过");
            return;
        }

        OrderInfoPageRsp orderInfoPageRsp = new OrderInfoPageRsp();
        BeanUtils.copyProperties(orderInfo, orderInfoPageRsp);
        ArrayList<OrderInfoPageRsp> orderInfoListTemp = Lists.newArrayList(orderInfoPageRsp);
        orderSingleQueryManager.buildRespForOrderNormalQuery(orderInfoListTemp);


        YnOmsOrderRequestCommon createOrderReq = YnOmsCreateOrderConvert.transformerCreateOrder(orderInfoPageRsp);
        ResponseBase<YnOmsOrderResponseCommon> res = ynOmsClient.createOrder(createOrderReq);
        log.info(" YnOmsGatewayImpl createOrder   req:{}, res:{}", JSON.toJSONString(createOrderReq), JSON.toJSONString(res));
        if (!res.checkSuccess()) {
            WxRobotOkHttpUtils.post(RobotEnumExample.OMS_ORDER_CREATE_FAIL, true, new ExampleCallBack(), orderInfo.getOrderNo(), res.getMsg());
            log.error(" YnOmsGatewayImpl createOrder   fail  req:{}, res:{}", JSON.toJSONString(createOrderReq), JSON.toJSONString(res));
            throw ExceptionUtil.getErrorException(ErrorType.CONNECTED_ERROR);
        }
        //微商城的订单只能是自动接单，所以在创建完雨诺订单，需要自动更新订单状态到已接单
        YnOmsPushOrderStatusRequest pushOrderStatusRequest = YnOmsCreateOrderConvert.getYnOmsPushOrderStatusRequest(orderInfo.getThirdOrderNo());
        //订单状态同步到OMS
        ResponseBase<YnOmsPushOrderStatusResponse> orderPushStatus = ynOmsClient.pushOrderStatus(pushOrderStatusRequest);
        log.info(" YnOmsGatewayImpl createOrder pushOrderStatus  req:{}, res:{}", JSON.toJSONString(pushOrderStatusRequest), JSON.toJSONString(orderPushStatus));
        if (!orderPushStatus.checkSuccess()) {
            WxRobotOkHttpUtils.post(RobotEnumExample.OMS_ORDER_PUSH_FAIL, true, new ExampleCallBack(), orderInfo.getOrderNo(), orderPushStatus.getMsg());
            log.error(" YnOmsGatewayImpl createOrder  pushOrderStatusRequest fail  req:{}, res:{}", JSON.toJSONString(pushOrderStatusRequest), JSON.toJSONString(orderPushStatus));
            throw ExceptionUtil.getErrorException(ErrorType.CONNECTED_ERROR);
        }
        UpdateWrapper<OrderInfo> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(OrderInfo::getOrderNo, orderInfo.getOrderNo());
        OrderInfo updater = new OrderInfo();
        orderInfoExtendInfo.setOrderTransportYN("1");
        updater.setExtendInfo(JSON.toJSONString(orderInfoExtendInfo));
        orderInfoMapper.update(updater, wrapper);
        log.info("createOrder success update  orderTransportYN");

    }


    /**
     * 取消订单,给雨诺 OMS系统
     *
     * @param req 订单详细信息
     */
    @Override
    public void cancelOrder(AddOrderInfoReqDto req) {
        String oldOrderNo = req.getOlorderno();
        OrderInfo orderInfo = orderInfoMapper.selectOrderByThirdOrderNo(oldOrderNo);
        String sign = OmsMsgEncryptedUtil.encryptedToSign("ordercode", orderInfo.getThirdOrderNo());
        YnOmsCancelOrderRequestCommon cancelOrderReq = new YnOmsCancelOrderRequestCommon(orderInfo.getThirdOrderNo(), sign);
        ResponseBase<YnOmsCancelOrderResponseCommon> order = ynOmsClient.cancelOrder(cancelOrderReq);
        log.info(" YnOmsGatewayImpl cancelOrder  req:{},res:{}", JSON.toJSONString(cancelOrderReq), JSON.toJSONString(order));
        if (!order.checkSuccess()) {
            WxRobotOkHttpUtils.post(RobotEnumExample.OMS_CANCEL_ORDER_PUSH_FAIL, true, new ExampleCallBack(), oldOrderNo, order.getMsg());
            log.error(" YnOmsGatewayImpl cancelOrder  fail  req:{},res:{}", JSON.toJSONString(cancelOrderReq), JSON.toJSONString(order));
            throw ExceptionUtil.getErrorException(ErrorType.CONNECTED_ERROR);
        }
    }


    /**
     * 创建退单，给雨诺 OMS系统
     *
     * @param returnOrderReq 订单的详细信息
     */
    @Override
    public void createReturnOrderByYN(YnOmsReturnOrderRequestCommon returnOrderReq) {
        ResponseBase<YnOmsReturnOrderResponseCommon> order = ynOmsClient.createReturnOrder(returnOrderReq);
        log.info(" YnOmsGatewayImpl createReturnOrderByYN  req:{}, res:{}", JSON.toJSONString(returnOrderReq), JSON.toJSONString(order));
        if (!order.checkSuccess()) {
            log.error(" YnOmsGatewayImpl createReturnOrderByYN  fail req:{}, res:{}", JSON.toJSONString(returnOrderReq), JSON.toJSONString(order));
            throw ExceptionUtil.getErrorException(ErrorType.CONNECTED_ERROR);
        }
    }


    /**
     * 同步订单状态 给雨诺 OMS系统
     *
     * @param req O2O订单信息数据
     */
    @Override
    public void pushOrderStatus(AddOrderInfoReqDto req) {
        String orderStatus = req.getOmsstatus();

        OrderInfo orderInfo = orderInfoManager.queryOrderInfoByThirdOrderNo(req.getOlorderno());
        OrderInfoPageRsp orderInfoRep = new OrderInfoPageRsp();
        BeanUtils.copyProperties(orderInfo, orderInfoRep);
        orderSingleQueryManager.buildRespForOrderNormalQuery(Lists.newArrayList(orderInfoRep));

        //获取配送信息
        String rideName = "";
        String ridePhone = "";
        String rideDelivery = "";
        OrderDeliveryRecord orderDeliveryRecord = orderInfoRep.getOrderDeliveryRecord();
        if (ObjectUtil.isNotNull(orderDeliveryRecord)) {
            rideName = StrUtil.isEmpty(orderDeliveryRecord.getRiderName()) ? "" : orderDeliveryRecord.getRiderName();
            ridePhone = StrUtil.isEmpty(orderDeliveryRecord.getRiderPhone()) ? "" : orderDeliveryRecord.getRiderPhone();
            rideDelivery = StrUtil.isEmpty(orderDeliveryRecord.getDeliveryPlatName()) ? "" : orderDeliveryRecord.getDeliveryPlatName();
        }

        //订单类型:order 表示订单, returnorder 表示退单
        String orderType = orderStatus.equals("7") ? "returnorder" : "order";
        String omsStatus = changeO2OStatusToOMS(orderStatus);
        YnOmsPushOrderStatusRequest pushOrderStatusRequest = YnOmsPushOrderStatusRequest.builder().ORDERCODE(req.getOlorderno()).STATUS(omsStatus).ORDERTYPE(orderType).ORDERFROM("111").NAME(rideName).PHONE(ridePhone).DELIVERY(rideDelivery).build();

        //获取签名
        String sign = OmsMsgEncryptedUtil.encryptedToSign(pushOrderStatusRequest);
        pushOrderStatusRequest.setSign(sign);

        //订单状态同步到OMS
        ResponseBase<YnOmsPushOrderStatusResponse> order = ynOmsClient.pushOrderStatus(pushOrderStatusRequest);
        if (!order.checkSuccess()) {
            log.error(" request {}, fail to cancel order,{}", JSON.toJSONString(order), order.getMsg());
            throw ExceptionUtil.getErrorException(ErrorType.CONNECTED_ERROR);
        }
    }


    /**
     * 把O2O的订单状态转换成OMS的订单状态
     *
     * @return
     */
    private String changeO2OStatusToOMS(String orderStatus) {
//O2O订单状态   1 等待接单 2等待配送 3等待出库 4配送中 5已完成 6订单取消 7订单退款 0未知状态
// OMS状态
//        35：已接单 37：已拣货 40：呼叫骑手 42: 骑手接单
//        45：骑手到店 53：配送异常  54：骑手取货 58：配送完成
//        110：订单取消  120：退款及再次申请
//        125：退款取消 130：同意退款 131：驳回退款

        String osmStatus = "";
        switch (orderStatus) {
            case "1":
                osmStatus = "";
                break;
            case "2":
                osmStatus = "35";
                break;
            case "3":
                osmStatus = "37";
                break;
            case "4":
                osmStatus = "54";
                break;
            case "5":
                osmStatus = "58";
                break;
            case "6":
                osmStatus = "110";
                break;
            case "7":
                osmStatus = "120";
                break;

        }

        return osmStatus;
    }

    /**
     * 雨诺订单状态同步到海典
     *
     * @param req OMS订单
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public YnOmsOrderStatusCallBackResponse syncOrderStatus(YnOmsOrderStatusCallBackRequest req) {

        if (req.getOrdertype().equals("returnorder")) {
            return syncOrderStatus2(req);
        } else {
            return syncOrderStatus1(req);
        }

    }

    private YnOmsOrderStatusCallBackResponse syncOrderStatus1(YnOmsOrderStatusCallBackRequest req) {
        try {
            String orderCode = req.getOrdercode();
            OrderInfo orderInfo = orderInfoMapper.selectOrderByThirdOrderNo(orderCode);
            if (ObjectUtil.isNull(orderInfo)) {
                log.error("回写订单状态失败,未匹配订单：req {}", JSON.toJSONString(req));
                return YnOmsOrderStatusCallBackResponse.fail("回写订单状态失败,未匹配订单：" + req.getOrdercode());
            }
            if (StrUtil.isNotEmpty(req.getStatus()) && req.getStatus().equals("57") && StrUtil.isNotEmpty(req.getSelfpickcode())) { //自提码校验
                if (!req.getSelfpickcode().equals(orderInfo.getSelfVerifyCode())) {
                    return YnOmsOrderStatusCallBackResponse.fail("自提码匹配失败,请核对自提码.");
                }
            }
            String o2oOrderStatus = transformOrderStatus(req.getStatus());

            if (StringUtils.isBlank(o2oOrderStatus)) {
                //告警
                log.info("订单状态不需要更新 {}", JSON.toJSONString(req));
            } else {
                if (orderInfo.getOrderState() > Integer.valueOf(o2oOrderStatus)) {
                    log.info("订单状态已经更新过了 {}", JSON.toJSONString(req));
                    return YnOmsOrderStatusCallBackResponse.success();
                }
                OrderInfo updateInfo = new OrderInfo();
                updateInfo.setOrderState(Integer.valueOf(o2oOrderStatus));
                orderInfo.setOrderState(Integer.valueOf(o2oOrderStatus));
                if (orderInfo.getErpState() < ErpStateEnum.WAIT_SALE.getCode()) {
                    if (Integer.valueOf(o2oOrderStatus) == OrderStateEnum.UN_DELIVERY.getCode() || Integer.valueOf(o2oOrderStatus) == OrderStateEnum.POSTING.getCode()) {
                        updateInfo.setErpState(ErpStateEnum.WAIT_SALE.getCode());
                        orderInfo.setErpState(ErpStateEnum.WAIT_SALE.getCode());
                    }
                }


                orderInfoMapper.update(updateInfo, new UpdateWrapper<OrderInfo>().lambda().eq(OrderInfo::getOrderNo, orderInfo.getOrderNo()));
                //记录状态日志
                String transType = StrUtil.isEmpty(req.getTranstype()) ? "自配送" : req.getTranstype();
                sendOrderLogByOrderStatus(transType, orderInfo, req.getStatus());


            }
            OrderDeliveryRecord orderDeliveryRecord = this.updateDeliveryRecord2(req, orderInfo.getOrderNo());
            OnlineStoreInfoRspDto storeInfo = SpringUtil.getBean(BaseInfoManager.class).getOnlineStoreInfo(orderInfo.getMerCode(), orderInfo.getThirdPlatformCode(), orderInfo.getClientCode(), orderInfo.getOnlineStoreCode());
            sendMiddle(req, orderInfo, null, req.getStatus(), orderDeliveryRecord, storeInfo);
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            WxRobotOkHttpUtils.post(RobotEnumExample.OMS_ORDER_CALLBACK_FAIL, true, new ExampleCallBack(), req.getOrdercode(), e.getMessage());
            log.error(String.format("回写订单状态失败,请咨询系统管理员. req :%s", JSON.toJSONString(req)), e);
            return YnOmsOrderStatusCallBackResponse.fail("回写订单状态失败,请咨询系统管理员.");
        }

        return YnOmsOrderStatusCallBackResponse.success();
    }


    private YnOmsOrderStatusCallBackResponse syncOrderStatus2(YnOmsOrderStatusCallBackRequest req) {
        try {
            String orderCode = req.getOrdercode();
            RefundOrder refundOrder = refundOrderMapper.selectOne(new QueryWrapper<RefundOrder>().lambda().eq(RefundOrder::getThirdRefundNo, orderCode));
            if (ObjectUtil.isNull(refundOrder)) {
                log.error("回写退单订单状态失败,未匹配退单订单：req {}", JSON.toJSONString(req));
                return YnOmsOrderStatusCallBackResponse.fail("回写退单订单状态失败,未匹配退单订单：" + req.getOrdercode());
            }
            OrderInfo orderInfo = orderInfoMapper.selectOrderByThirdOrderNo(refundOrder.getThirdOrderNo());
            if (ObjectUtil.isNull(orderInfo)) {
                log.error("回写退单订单状态失败,未匹配到正向订单：req {}", JSON.toJSONString(req));
                return YnOmsOrderStatusCallBackResponse.fail("回写订单状态失败,未匹配订单：" + req.getOrdercode());
            }
            if (StrUtil.isNotEmpty(req.getStatus()) && req.getStatus().equals("57") && StrUtil.isNotEmpty(req.getSelfpickcode())) { //自提码校验
                if (!req.getSelfpickcode().equals(orderInfo.getSelfVerifyCode())) {
                    return YnOmsOrderStatusCallBackResponse.fail("自提码匹配失败,请核对自提码.");
                }
            }

            String sendOrderStatus = req.getStatus();
            Integer refundStatus = getRefundStatus(sendOrderStatus, orderInfo);
            if (refundOrder.getState() >= refundStatus) {
                log.info("退款订单状态已经更新过了 {}", JSON.toJSONString(req));
                return YnOmsOrderStatusCallBackResponse.success();
            }


            if (null != refundStatus) {
                RefundOrder updateRefundOrder = new RefundOrder();
                updateRefundOrder.setState(refundStatus); //心云订单已完成
                refundOrder.setState(refundStatus);
                if (sendOrderStatus.equals("126")) {
                    updateRefundOrder.setBillType(DsConstants.INTEGER_TWO);
                    refundOrder.setBillType(DsConstants.INTEGER_TWO);
                }
                if (refundStatus.equals(RefundStateEnum.SUCCESS.getCode())) {
                    updateRefundOrder.setCompleteTime(new Date());
                    refundOrder.setCompleteTime(new Date());
                }
                refundOrderMapper.update(updateRefundOrder, new QueryWrapper<RefundOrder>().lambda().eq(RefundOrder::getRefundNo, refundOrder.getRefundNo()));


            }
            if (sendOrderStatus.equals("126")) {
                updateOrderDetailWithUpdateOrderInfo(refundOrder, orderInfo);
/*
                部分退款重新计算下账金额和财务信息
                RefundAuditHandleReq refundHandle = new RefundAuditHandleReq();
                refundHandle.setType(DsConstants.INTEGER_ONE);
                refundHandle.setDoubleCheck(DsConstants.INTEGER_ZERO);
                refundHandle.setRefundNo(refundOrder.getRefundNo());

                refundService.refundAgreeOnlyMoney(refundOrder.getMerCode(), "1695970231258714114", refundHandle,String.valueOf(refundOrder.getOrderNo())); //TODO userId要换成System
                messageProducerService.produceUpdateOrderMessage(Collections.singletonList(refundOrder.getOrderNo()), OrderUpdateCodeEnum.AGREE_REFUND.getCode());
*/
            }


            if (sendOrderStatus.equals("131")) { //驳回退款
                OrderInfo updateOrderInfo = new OrderInfo();
                updateOrderInfo.setLockFlag(0);
                orderInfoMapper.update(updateOrderInfo, new UpdateWrapper<OrderInfo>().lambda().eq(OrderInfo::getOrderNo, orderInfo.getOrderNo()));
            }

            if (sendOrderStatus.equals("130")) { //同意退款
                //经营数据同步到Kafka
                if (DsConstants.O2O.equals(orderInfo.getServiceMode())) {
                    orderInfoService.analysisPayInfoData(orderInfo.getOrderNo());
                }
            }

            sendRefundOrderLogByStatus(sendOrderStatus, refundOrder, orderInfo);
            //refund 100 已完成 调用商品中台归还库存
            if (null != refundStatus && 100 == refundStatus) {
                //commodityHandlerManager.sendCommodityNoValidOrder(refundOrder);
                stockReturn(orderInfo);
            }

            OrderDeliveryRecord orderDeliveryRecord = new OrderDeliveryRecord();
            orderDeliveryRecord.setDeliveryType("0");
            OnlineStoreInfoRspDto storeInfo = SpringUtil.getBean(BaseInfoManager.class).getOnlineStoreInfo(orderInfo.getMerCode(), orderInfo.getThirdPlatformCode(), orderInfo.getClientCode(), orderInfo.getOnlineStoreCode());
            sendMiddle(req, orderInfo, refundOrder, req.getStatus(), orderDeliveryRecord, storeInfo);
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            WxRobotOkHttpUtils.post(RobotEnumExample.OMS_REFUND_ORDER_CALLBACK_FAIL, true, new ExampleCallBack(), req.getOrdercode(), e.getMessage());
            log.error(String.format("回写退单订单状态失败,请咨询系统管理员. req :%s", JSON.toJSONString(req)), e);
            return YnOmsOrderStatusCallBackResponse.fail("回写退单订单状态失败,请咨询系统管理员.");
        }


        return YnOmsOrderStatusCallBackResponse.success();
    }

    /**
     * 归还扣除库存
     */
    public void stockReturn(OrderInfo orderInfo) {
        List<OrderDetail> orderDetails = orderDetailMapper.selectList(new LambdaQueryWrapper<OrderDetail>().eq(OrderDetail::getOrderNo, orderInfo.getOrderNo()));
        unlockStock(orderInfo, orderDetails);
    }

    private void unlockStock(OrderInfo orderInfo, List<OrderDetail> orderDetailList) {
        if (orderInfo == null) {
            return;
        }
        // https://www.tapd.cn/61969829/prong/stories/view/1161969829001056185
//        if (PlatformCodeEnum.YD_JIA.getCode().equals(orderInfo.getThirdPlatformCode())) {
//            return;
//        }
        String lockKey = RedisKeyUtil.getCommodityStockLock(orderInfo.getOrderNo().toString());
        if (RedisLockUtil.getLockKey(lockKey, String.valueOf(System.currentTimeMillis()))) {
            try {
                //查询已锁库存
                LambdaQueryWrapper<CommodityStock> commodityStockLambdaQueryWrapper = new LambdaQueryWrapper<>();
                commodityStockLambdaQueryWrapper.eq(CommodityStock::getOrderNo, orderInfo.getOrderNo());
                commodityStockLambdaQueryWrapper.eq(CommodityStock::getType, LocalStockEnum.ADD.getType());
                List<CommodityStock> lockExistList = SpringUtil.getBean(CommodityStockService.class).list(commodityStockLambdaQueryWrapper);
                if (CollUtil.isEmpty(lockExistList)) {
                    return;
                }
                Map<String, CommodityStock> lockExistMap = lockExistList.stream().collect(Collectors.toMap(it -> it.getOrderNo() + "_" + it.getOrderDetailId() + "_" + it.getOrganizationCode(), a -> a));

                //查询已解锁库存
                LambdaQueryWrapper<CommodityStock> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(CommodityStock::getOrderNo, orderInfo.getOrderNo());
                queryWrapper.eq(CommodityStock::getType, LocalStockEnum.SUB.getType());
                List<CommodityStock> unlockExistList = SpringUtil.getBean(CommodityStockService.class).list(queryWrapper);
                //已解锁库存数量 key 订单号+订单详情ID , value 数量
                Map<String, Integer> unLockQtyMap = new HashMap<>(8);
                for (CommodityStock commodityStock : unlockExistList) {
                    String key = commodityStock.getOrderNo() + "_" + commodityStock.getOrderDetailId() + "_" + commodityStock.getOrganizationCode();
                    int qty = unLockQtyMap.getOrDefault(key, 0);
                    qty = qty + commodityStock.getStockQty();
                    unLockQtyMap.put(key, qty);
                }
                if (CollUtil.isEmpty(orderDetailList)) {
                    orderDetailList = new ArrayList<>();
                    for (CommodityStock commodityStock : lockExistList) {
                        String key = commodityStock.getOrderNo() + "_" + commodityStock.getOrderDetailId() + "_" + commodityStock.getOrganizationCode();
                        Integer unLockQty = unLockQtyMap.getOrDefault(key, 0);
                        // 剩余解锁库存 = 锁定库存-已解锁库存
                        int surplusStockQty = commodityStock.getStockQty() - unLockQty;
                        if (surplusStockQty <= 0) {
                            continue;
                        }
                        OrderDetail orderDetail = new OrderDetail();
                        orderDetail.setErpCode(commodityStock.getErpCode());
                        orderDetail.setOrderNo(Long.valueOf(commodityStock.getOrderNo()));
                        orderDetail.setId(commodityStock.getOrderDetailId());
                        orderDetail.setCommodityCount(surplusStockQty);
                        orderDetail.setRefundCount(0);
                        orderDetailList.add(orderDetail);
                    }
                }
                //查下erp库存异常数据
                List<CommodityExceptionOrder> commodityExceptionOrders = SpringUtil.getBean(CommodityExceptionOrderService.class)
                        .getListByOrderNo(orderInfo.getOrderNo(), CollUtil.list(false, DsConstants.INTEGER_TWO, DsConstants.INTEGER_THREE));
                List<String> exceptionErpCode = CollUtil.emptyIfNull(commodityExceptionOrders).stream()
                        .filter(it -> OrderDetailStatusEnum.OUT_OF_STOCK.getCode().equals(it.getStatus()))
                        .map(CommodityExceptionOrder::getErpCode)
                        .collect(Collectors.toList());

                Iterator<OrderDetail> iterator = orderDetailList.iterator();
                while (iterator.hasNext()) {
                    OrderDetail next = iterator.next();
                    String key = next.getOrderNo() + "_" + next.getId() + "_" + orderInfo.getOrganizationCode();
                    if (!lockExistMap.containsKey(key)) {
                        iterator.remove();
                        continue;
                    }
                    // erp返回库存不足,不还商品库存
                    if (exceptionErpCode.contains(next.getErpCode())) {
                        iterator.remove();
                        continue;
                    }
                    if (next.getPickCount() <= 0) {
                        continue;
                    }
                    CommodityStock commodityLockStock = lockExistMap.get(key);
                    Integer qty = unLockQtyMap.getOrDefault(key, 0);
                    unLockQtyMap.put(key, qty + next.getPickCount());
                    qty = unLockQtyMap.get(key);
                    // 当前解锁数量+本次解锁数量>已锁定数量  continue
                    if (qty > commodityLockStock.getStockQty()) {
                        iterator.remove();
                    }
                }
                List<StockDeductReqDto.StockCommodity> stockCommodityList = buildStockCommodityData(orderDetailList, Boolean.TRUE);
                StockDeductReqDto stockDeductReqDto = new StockDeductReqDto();
                stockDeductReqDto.setStockList(stockCommodityList);
                stockDeductReqDto.setAdd(Boolean.TRUE);
                stockDeductReqDto.setUserName("系统自动");
                stockDeductReqDto.setOrderNo(orderInfo.getOrderNo());
                stockDeductReqDto.setMerCode(orderInfo.getMerCode());
                stockDeductReqDto.setOnlineStoreCode(orderInfo.getOnlineStoreCode());
                stockDeductReqDto.setPlatformCode(orderInfo.getThirdPlatformCode());
                stockDeductReqDto.setNote("解锁库存");
                stockDeductReqDto.setStoreCode(orderInfo.getOrganizationCode());
                stockDeductReqDto.setStoreId(SpringUtil.getBean(BaseInfoCache.class).getStoreIdByMerCodeOrgCode(orderInfo.getMerCode(),orderInfo.getOrganizationCode()));
                //商品中台解锁库存
                ResponseBase<StockDeductRspDto> base = SpringUtil.getBean(MiddleMerchandiseClient.class).stockOccupyStockV2(stockDeductReqDto);

                log.info("解锁商品中台库存===> requestParam:{}, result:{}",
                        JSON.toJSONString(stockDeductReqDto), JSON.toJSONString(base));
//                Arrays.stream(Thread.currentThread().getStackTrace()).forEach(it -> log.info("解锁商品中台库存===> {}", it));
                if (base.checkSuccess() && DsConstants.INTEGER_ZERO.equals(base.getData().getStatus())) {
                    List<CommodityStock> saveCommodityStocks = new ArrayList<>();
                    for (OrderDetail orderDetail : orderDetailList) {
                        CommodityStock commodityStock = new CommodityStock();
                        commodityStock.setMerCode(orderInfo.getMerCode());
                        commodityStock.setErpCode(orderDetail.getErpCode());
                        commodityStock.setOrderNo(orderDetail.getOrderNo().toString());
                        commodityStock.setOrderDetailId(orderDetail.getId());
                        commodityStock.setOnlineStoreCode(orderInfo.getOnlineStoreCode());
                        commodityStock.setOrganizationCode(orderInfo.getOrganizationCode());
                        commodityStock.setStockQty(orderDetail.getPickCount());
                        commodityStock.setCreateTime(new Date());
                        commodityStock.setType(LocalStockEnum.SUB.getType());
                        saveCommodityStocks.add(commodityStock);
                    }
                    SpringBeanUtils.getBean(CommodityStockService.class).saveBatch(saveCommodityStocks);
                }
            } catch (Exception e) {
                String errorMsg = StrUtil.format("commodityUnlockStock error! orderNo:{}", orderInfo.getOrderNo());
                log.error(errorMsg, e);
            } finally {
                RedisLockUtil.releaseLockKey(lockKey);
            }
        } else {
            log.warn("commodityUnlockStock failed to get lock! orderNo:{}", orderInfo.getOrderNo());
        }
    }

    private List<StockDeductReqDto.StockCommodity> buildStockCommodityData(List<OrderDetail> orderDetailList, boolean pickCountFlag) {
        List<StockDeductReqDto.StockCommodity> stockCommodityList = new ArrayList<>();
        //【ID1055864】下单组合商品改传子商品  https://www.tapd.cn/61969829/prong/stories/view/1161969829001055864
//        List<StockDeductReqDto.StockCommodity> stockComposeCommodityList = new ArrayList<>();
//        List<StockDeductReqDto.StockCommodity> finalStockComposeCommodityList = stockComposeCommodityList;
        List<StockDeductReqDto.StockCommodity> stockChaiLingCommodityList = new ArrayList<>();
        List<StockDeductReqDto.StockCommodity> finalStockChaiLingCommodityList = stockChaiLingCommodityList;
        orderDetailList.forEach(orderDetail -> {
            if (DsConstants.INTEGER_TWO.equals(orderDetail.getChailing())
                    && !StringUtils.isEmpty(orderDetail.getChaiLingOriginalErpCode())) {
                // 拆零商品
                StockDeductReqDto.StockCommodity stockComposeCommodity = null;
                if (pickCountFlag) {
                    stockComposeCommodity = new StockDeductReqDto.StockCommodity(orderDetail.getChaiLingOriginalErpCode(), orderDetail.getRefundCount(),orderDetail.getId());
                } else {
                    stockComposeCommodity = new StockDeductReqDto.StockCommodity(orderDetail.getChaiLingOriginalErpCode(), orderDetail.getCommodityCount(),orderDetail.getId());
                }
                finalStockChaiLingCommodityList.add(stockComposeCommodity);
//            }else if(!StringUtils.isEmpty(orderDetail.getOriginalErpCode()) && null != orderDetail.getOriginalErpCodeNum()){
//                // 组合商品
//                StockDeductReqDto.StockCommodity stockComposeCommodity = null;
//                if (pickCountFlag) {
//                    Integer originalPickCount = orderDetail.getOriginalErpCodeNum() - orderAssembleCommodityRelationService.getRefundOriginalCount(orderDetail);
//                    stockComposeCommodity = new StockDeductReqDto.StockCommodity(orderDetail.getOriginalErpCode(), originalPickCount);
//                }else{
//                    stockComposeCommodity = new StockDeductReqDto.StockCommodity(orderDetail.getOriginalErpCode(), orderDetail.getOriginalErpCodeNum());
//                }
//                finalStockComposeCommodityList.add(stockComposeCommodity);
            } else {
                StockDeductReqDto.StockCommodity stockCommodity = null;
                if (pickCountFlag) {
                    stockCommodity = new StockDeductReqDto.StockCommodity(orderDetail.getErpCode(), orderDetail.getRefundCount(),orderDetail.getId());
                } else {
                    stockCommodity = new StockDeductReqDto.StockCommodity(orderDetail.getErpCode(), orderDetail.getCommodityCount(),orderDetail.getId());
                }
                stockCommodityList.add(stockCommodity);
            }
        });
        if (!org.springframework.util.CollectionUtils.isEmpty(stockChaiLingCommodityList)) {
            stockChaiLingCommodityList = stockChaiLingCommodityList.stream().distinct().collect(Collectors.toList());
            stockCommodityList.addAll(stockChaiLingCommodityList);
        }
//        if(!CollectionUtils.isEmpty(stockComposeCommodityList)){
//            stockComposeCommodityList = stockComposeCommodityList.stream().distinct().collect(Collectors.toList());
//            stockCommodityList.addAll(stockComposeCommodityList);
//        }
//        //同一个订单相同明细.合并去占用库存
//        if (CollUtil.isNotEmpty(stockCommodityList)) {
//            Map<String, Integer> map = new HashMap<>();
//            for (StockDeductReqDto.StockCommodity stockCommodity : stockCommodityList) {
//                Integer stock = map.getOrDefault(stockCommodity.getErpCode(), 0);
//                stock = stock + stockCommodity.getStock();
//                map.put(stockCommodity.getErpCode(), stock);
//            }
//            stockCommodityList.clear();
//            map.forEach((key, value) -> {
//                stockCommodityList.add(new StockDeductReqDto.StockCommodity(key, value));
//            });
//        }
        return stockCommodityList;
    }

    private void updateOrderDetailWithUpdateOrderInfo(RefundOrder refundOrder, OrderInfo orderInfo) {
        List<RefundDetail> refundDetailList = refundDetailMapper.selectListByRefundNo(refundOrder.getRefundNo());
        List<OrderDetail> orderDetailList = orderDetailMapper.selectListByOrderNo(refundOrder.getOrderNo());

        Map<String, OrderDetail> orderDetailMap = orderDetailList.stream().collect(Collectors.toMap(p -> String.format("%s_%s", p.getErpCode(), p.getThirdDetailId()), p -> p));
        for (RefundDetail refundDetail : refundDetailList) {

            String key = String.format("%s_%s", refundDetail.getErpCode(), refundDetail.getThirdDetailId());
            if (orderDetailMap.containsKey(key)) {
                OrderDetail orderDetail = orderDetailMap.get(key);
                int refundCount = refundDetail.getRefundCount();

                int goodsCount = orderDetail.getCommodityCount();
                int alreadyRefundCount = orderDetail.getRefundCount();
                int orderItemStatus = orderDetail.getStatus();

                alreadyRefundCount += refundCount;
                orderDetail.setRefundCount(alreadyRefundCount);

                if (alreadyRefundCount >= goodsCount) {
                    orderItemStatus = OrderDetailStatusEnum.REFUND.getCode();
                    orderDetail.setStatus(orderItemStatus);
                }

                BigDecimal discountAmount = orderDetail.getDiscountAmount().subtract(refundDetail.getCouponAmount() == null ? BigDecimal.ZERO : refundDetail.getCouponAmount()).subtract(refundDetail.getActivityDiscountAmont() == null ? BigDecimal.ZERO : refundDetail.getActivityDiscountAmont());
                discountAmount = discountAmount.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : discountAmount;
                //actualAmount = 商品金额-商家明细优惠金额-调整金额
                BigDecimal foodAmount = new BigDecimal(orderDetail.getCommodityCount().intValue() - alreadyRefundCount).multiply(orderDetail.getPrice());
                BigDecimal actualAmount = foodAmount.subtract(discountAmount).subtract(orderDetail.getAdjustAmount());
                actualAmount = actualAmount.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : actualAmount;

                //修改订单详情状态为已退款
                orderDetailMapper.updateByUniqueKey(new OrderDetail().setOrderNo(refundOrder.getOrderNo()).setErpCode(refundDetail.getErpCode()).setThirdDetailId(refundDetail.getThirdDetailId()).setStatus(orderItemStatus).setRefundCount(alreadyRefundCount).setActualAmount(actualAmount).setDiscountAmount(discountAmount));
            }
        }

        // 订单全部退完？
        boolean wholeOrderRefund = true;
        for (OrderDetail orderDetail : orderDetailList) {
            // 其中任意明细未完成全明细退款，则订单未完成全部退款
            if (!orderDetail.getStatus().equals(OrderDetailStatusEnum.REFUND.getCode())) {
                wholeOrderRefund = false;
                break;
            }
        }

        //更新订单状态
        if (RefundTypeEnum.ALL.getCode().equals(refundOrder.getType())) {
            orderInfo.setOrderState(OrderStateEnum.CLOSED.getCode());
        } else {
            if (wholeOrderRefund) { //部分退款退完后修改为已完成
                orderInfo.setOrderState(OrderStateEnum.CLOSED.getCode());
            }

        }
        log.info("雨诺回调,更新订单状态为,订单号:{},订单状态:{}", orderInfo.getOrderNo(), orderInfo.getOrderState());
        orderInfo.setLockFlag(OrderLockFlagEnum.NOT_LOCK.getCode());
        orderInfoMapper.updateById(orderInfo);
    }

    private void sendRefundOrderLogByStatus(String sendOrderStatus, RefundOrder refundOrder, OrderInfo orderInfo) {
        if (sendOrderStatus.equals("126")) { //雨诺退单确认收货
            HydeeEsSyncClientAsync.refundLogToESNew(DsConstants.INTEGER_ONE, refundOrder, RefundLogEnum.CONFIRM_RECEIVED_GOODS, DsConstants.INTEGER_TWO, null, null);
        } else if (sendOrderStatus.equals("130")) { //雨诺同意退款
            int refundBusinessFlag = DsConstants.INTEGER_ONE;
            if (orderInfo.getErpState() >= ErpStateEnum.HAS_SALE.getCode() || orderInfo.getOrderState() >= OrderStateEnum.POSTING.getCode()) {
                refundBusinessFlag = DsConstants.INTEGER_TWO;
            }
            HydeeEsSyncClientAsync.refundLogToESNew(DsConstants.INTEGER_ONE, refundOrder, RefundLogEnum.AGREE_REFUND_ORDER, refundBusinessFlag, null, null);
        } else if (sendOrderStatus.equals("131")) { //雨诺驳回退款
            HydeeEsSyncClientAsync.refundLogToESNew(DsConstants.INTEGER_ONE, refundOrder, RefundLogEnum.REJECT_REFUND_ORDER_MONEY, DsConstants.INTEGER_ONE, null, null);

        }
    }

    private Integer getRefundStatus(String sendOrderStatus, OrderInfo orderInfo) {
        Integer refundStatus = null;
        if (sendOrderStatus.equals("126")) { //雨诺退单确认收货
            refundStatus = 100;//已完成
        } else if (sendOrderStatus.equals("130")) { //雨诺同意退款  仅退款改为100  退货退款改为20
            refundStatus = 100;//已完成
            if (orderInfo.getOrderState() >= OrderStateEnum.POSTING.getCode() || orderInfo.getErpState() >= ErpStateEnum.HAS_SALE.getCode()) {
                refundStatus = 20;// 待退货
            }
        } else if (sendOrderStatus.equals("131")) { //雨诺驳回退款
            refundStatus = 102;
        }
        return refundStatus;
    }


    /**
     * OMS将两种配送消息传递海典
     * 1、订单呼叫
     * 2、取消骑手
     *
     * @param req OMS订单
     * @return
     */
    @Override
    public YnOmsDeliveryCallBackResponse syncDeliveryInformation(YnOmsDeliveryCallBackRequest req) {
        try {
            String function = req.getFunction();
            DeliveryStateEnum deliveryStateEnum = null;
            if (function.equalsIgnoreCase("create")) {
                deliveryStateEnum = DeliveryStateEnum.UN_TAKE;
            } else if (function.equalsIgnoreCase("cancel")) {
                deliveryStateEnum = DeliveryStateEnum.CANCELED;
            }
            OrderDeliveryRecord odr = new OrderDeliveryRecord();
            odr.setState(ObjectUtil.isNull(deliveryStateEnum) ? null : deliveryStateEnum.getCode());
            updateDeliveryRecord(odr, req.getOrdercode(), "order");
        } catch (Exception e) {
            log.error("同步配送消息失败", e);
            WxRobotOkHttpUtils.post(RobotEnumExample.OMS_DELIVERY_ORDER_CALLBACK_FAIL, true, new ExampleCallBack(), req.getOrdercode(), e.getMessage());
            String error = "后台异常：" + (ObjectUtil.isNotNull(e.getCause()) ? e.getCause().getMessage() : "");
            return YnOmsDeliveryCallBackResponse.fail(error);
        }
        return YnOmsDeliveryCallBackResponse.success();
    }

    @Override
    @Retryable(value = RestClientException.class, backoff = @Backoff(delay = 3000L))
    public Boolean isOrderExist(String oldOrderNo) {
        YnOmsGetOrderStatusRequest getOrderStatusRequest = new YnOmsGetOrderStatusRequest();
        getOrderStatusRequest.setORDERCODE(oldOrderNo);
        getOrderStatusRequest.setORDERFROM("111");
        String sign = OmsMsgEncryptedUtil.encryptedToSign(getOrderStatusRequest);
        getOrderStatusRequest.setSign(sign);
        ResponseBase<YnOmsGetOrderStatusResponse> res = ynOmsClient.getOrderDetail(getOrderStatusRequest);
        log.info(" YnOmsGatewayImpl isOrderExist   req:{}, res:{}", JSON.toJSONString(res), JSON.toJSONString(res));
        if (!res.checkSuccess()) {
            return false;
        }
        return true;
    }

    private void updateDeliveryRecord(OrderDeliveryRecord record, String orderCode, String orderType) {
        OrderInfo orderInfo = findOrderInfo(orderType, orderCode);
        if (ObjectUtil.isNull(orderInfo)) {
            log.error("同步配送消息失败,没有找到订单号：" + orderCode);
            return;
        }
        log.info("雨诺订单回调配送状态更新：" + JSONUtil.toJsonStr(record));
        orderDeliveryRecordMapper.update(record, new UpdateWrapper<OrderDeliveryRecord>().lambda().eq(OrderDeliveryRecord::getOrderNo, orderInfo.getOrderNo()));
    }

    /**
     * 同步订单信息时同时同步配送信息
     *
     * @param req
     * @return OrderDeliveryRecord
     */
    private OrderDeliveryRecord updateDeliveryRecord2(YnOmsOrderStatusCallBackRequest req, Long orderNo) throws UnsupportedEncodingException {

        OrderDeliveryRecord orderDeliveryRecord = orderDeliveryRecordMapper.selectOne(new LambdaQueryWrapper<OrderDeliveryRecord>().eq(OrderDeliveryRecord::getOrderNo, orderNo));
        if (null != orderDeliveryRecord && StringUtils.isNotBlank(req.getTranstype())) {
            String deliveryType = "3";
            Integer state = null;
            String deliveryPlatName = req.getTranstype();
            if (req.getTranstype().contains("员工自送")) {
                deliveryPlatName = DeliveryPlatformEnum.STAFF.getName();
            } else if (req.getTranstype().contains("达达配送")) {
                deliveryPlatName = DeliveryPlatformEnum.DADA.getName();
            } else if (req.getTranstype().contains("美团配送")) {
                deliveryPlatName = DeliveryPlatformEnum.MEITUAN_RIDER.getName();
            } else if (req.getTranstype().contains("D001")) {
                deliveryPlatName = DeliveryPlatformEnum.SFTC.getName();
            }
            orderDeliveryRecord.setDeliveryType(deliveryType);
            orderDeliveryRecord.setDeliveryPlatName(deliveryPlatName);
            String dispatcherName = req.getDispatcher_name();
            if (dispatcherName.contains("%")) {
                dispatcherName = URLDecoder.decode(req.getDispatcher_name(), "UTF-8");
            }
            if ("54".equals(req.getStatus())) {
                state = 3;
            } else if ("58".equals(req.getStatus())) {
                state = 4;
            }
            if (null != state && orderDeliveryRecord.getState() < state) {
                orderDeliveryRecord.setState(state);
            }
            if (StringUtils.isBlank(orderDeliveryRecord.getRiderName()) || !orderDeliveryRecord.getRiderName().equals(dispatcherName)) {
                orderDeliveryRecord.setRiderName(dispatcherName);
                orderDeliveryRecord.setRiderPhone(req.getDispatcher_mobile());
            }
            orderDeliveryRecordMapper.update(orderDeliveryRecord, new UpdateWrapper<OrderDeliveryRecord>().lambda().eq(OrderDeliveryRecord::getOrderNo, orderNo));


        }
        if (null == orderDeliveryRecord) {
            log.warn(" 雨诺回写订单 没有找到配送信息，订单号：" + orderNo);
            orderDeliveryRecord = new OrderDeliveryRecord();
        }
        return orderDeliveryRecord;
    }

    private OrderInfo findOrderInfo(String orderType, String orderCode) {
        Long orderNo = Long.valueOf(orderCode);
        OrderInfo orderInfo1;
        if (orderType.equals("returnorder")) {
            RefundOrder refundOrder = refundOrderMapper.selectOne(new QueryWrapper<RefundOrder>().lambda().eq(RefundOrder::getThirdRefundNo, orderNo));
            if (ObjectUtil.isNull(refundOrder)) {
                String error = "没有找到逆向单：" + orderNo;
                log.error(error);
                return null;
            }
            orderInfo1 = orderInfoMapper.selectOrderByThirdOrderNo(refundOrder.getThirdOrderNo());
        } else {
            orderInfo1 = orderInfoMapper.selectOrderByThirdOrderNo(orderCode);
        }
        if (ObjectUtil.isNull(orderInfo1)) {
            log.error("没有找到此订单：" + orderNo);
        }
        return orderInfo1;
    }


    /**
     * 订单状态发送到小前台
     *
     * @param req
     * @param orderInfo
     * @param refundOrder
     * @param status
     * @param orderDeliveryRecord
     * @param storeInfo
     */
    private void sendMiddle(YnOmsOrderStatusCallBackRequest req, OrderInfo orderInfo, RefundOrder refundOrder, String status, OrderDeliveryRecord orderDeliveryRecord, OnlineStoreInfoRspDto storeInfo) {
        DeliveryOutManager deliveryOutManager = SpringUtil.getBean(DeliveryOutManager.class);
        HemsCommonClient commonClient = SpringUtil.getBean(HemsCommonClient.class);
        switch (req.getStatus()) {
            case "58": //送到
                deliveryOutManager.deliverySuccess(orderInfo.getMerCode(), orderInfo, storeInfo, orderDeliveryRecord);
                break;
            case "57": //自提完成
                OrderUserPickUpGoodsReq userPickupGoodsReq = new OrderUserPickUpGoodsReq();
                userPickupGoodsReq.setOlShopId(orderInfo.getOnlineStoreCode());
                userPickupGoodsReq.setShopId(storeInfo.getOutShopId());
                userPickupGoodsReq.setOlOrderNo(orderInfo.getThirdOrderNo());
                userPickupGoodsReq.setPickUpCode(req.getSelfpickcode());
                userPickupGoodsReq.setOperateMan("system");
                commonClient.orderUserPickupGoods(userPickupGoodsReq, commonClient.constructHemsBaseData(orderInfo.getMerCode(), storeInfo.getPlatformCode(), storeInfo.getOnlineClientCode(), storeInfo.getSessionKey()));
                break;
            case "37":
                log.info("自提货 but 不处理 req {}", req);
                break;
            case "54": //统一处理为待发货
                deliveryOut(req, orderDeliveryRecord, storeInfo, orderInfo);
                break;
            case "110": //订单取消
                OrderCancelReq orderCancel = new OrderCancelReq();
                orderCancel.setOlOrderNo(orderInfo.getThirdOrderNo());
                orderCancel.setOlShopId(orderInfo.getOnlineStoreCode());
                orderCancel.setShopId(storeInfo.getOutShopId());
                orderCancel.setReason("");
                orderCancel.setReasonCode("-99");
                HemsBaseData baseData = commonClient.constructHemsBaseData(orderInfo.getMerCode(), storeInfo.getPlatformCode(), storeInfo.getOnlineClientCode(), storeInfo.getSessionKey());
                commonClient.orderCancel(orderCancel, baseData);
                break;
            case "126": //确认收货
                log.info("同意退款,入口,退款类型:{}", DsConstants.STRING_ZERO.equals(refundOrder.getRefundType()) ? "仅退款" : "退货退款");
                agreeRefund(orderInfo, refundOrder, storeInfo, RefundAuditType.CONFIRM_RECEIVE);
            case "130": //同意退款
                if (DsConstants.STRING_ZERO.equals(refundOrder.getRefundType())) {
                    log.info("同意退款,入口,退款类型:{}", DsConstants.STRING_ZERO.equals(refundOrder.getRefundType()) ? "仅退款" : "退货退款");
                    agreeRefund(orderInfo, refundOrder, storeInfo,RefundAuditType.AGREE_ONLY_REFUND);
                }
                break;
            case "131": //驳回退款
                commonClient = SpringUtil.getBean(HemsCommonClient.class);
                OrderRefundRejectReq refundReject = new OrderRefundRejectReq();
                refundReject.setOlShopId(orderInfo.getOnlineStoreCode());
                refundReject.setShopId(storeInfo.getOutShopId());
                refundReject.setOlOrderNo(orderInfo.getThirdOrderNo());
                refundReject.setRefundId(refundOrder.getThirdRefundNo());
                refundReject.setRefundAmount(refundOrder.getConsumerRefund().toPlainString());
                commonClient.merchantRefuseRefund(refundReject, commonClient.constructHemsBaseData(orderInfo.getMerCode(), storeInfo.getPlatformCode(), storeInfo.getOnlineClientCode(), storeInfo.getSessionKey()), refundOrder, RefundAuditType.REFUSE);
                break;
            default:
                log.info("状态对应不上 先记录日志 req{}", req);
        }
    }

    private void unLockStock(OrderInfo orderInfo, StockSourceEnum stockSourceEnum) {
        log.info("订单推送雨诺释放库存占用 {}", orderInfo);
        //查询订单明细
        List<OrderDetail> orderDetails = orderDetailMapper.selectList(new LambdaQueryWrapper<OrderDetail>().eq(OrderDetail::getOrderNo, orderInfo.getOrderNo()));
        LocalStockService stockService = SpringUtil.getBean(LocalStockService.class);
        stockService.unlockStock(orderInfo, orderDetails, stockSourceEnum);
    }

    private void agreeRefund(OrderInfo orderInfo, RefundOrder refundOrder, OnlineStoreInfoRspDto storeInfo,RefundAuditType refundAuditType) {
        HemsCommonClient commonClient = SpringUtil.getBean(HemsCommonClient.class);
        OrderRefundAgreeReq refundAgree = new OrderRefundAgreeReq();
        refundAgree.setOlOrderNo(orderInfo.getThirdOrderNo());
        refundAgree.setRefundId(refundOrder.getThirdRefundNo());
        refundAgree.setRefundAmount(refundOrder.getConsumerRefund().toPlainString());
        refundAgree.setOlShopId(orderInfo.getOnlineStoreCode());
        refundAgree.setShopId(storeInfo.getOutShopId());
        commonClient.merchantAgreeRefund(refundAgree, commonClient.constructHemsBaseData(orderInfo.getMerCode(), storeInfo.getPlatformCode(), storeInfo.getOnlineClientCode(), storeInfo.getSessionKey()), refundOrder, refundAuditType);
    }

    private void deliveryOut(YnOmsOrderStatusCallBackRequest req, OrderDeliveryRecord orderDeliveryRecord, OnlineStoreInfoRspDto storeInfo, OrderInfo orderInfo) {
        HemsCommonClient commonClient = SpringUtil.getBean(HemsCommonClient.class);
        OrderDeliveryReq orderDeliveryReq = new OrderDeliveryReq();
        orderDeliveryReq.setOlOrderNo(orderInfo.getThirdOrderNo());
        //设置方式
        orderDeliveryReq.setExpCmpId("3");
        orderDeliveryReq.setRiderName(req.getDispatcher_name());
        orderDeliveryReq.setOperateMan("system");
        orderDeliveryReq.setRiderName(req.getDispatcher_name() + " ");
        orderDeliveryReq.setRiderTel(req.getDispatcher_mobile());
        orderDeliveryReq.setOlShopId(orderInfo.getOnlineStoreCode());
        orderDeliveryReq.setShopId(storeInfo.getOutShopId());
        orderDeliveryReq.setLatitude(orderDeliveryRecord.getLatitude());
        orderDeliveryReq.setLongitude(orderDeliveryRecord.getLongitude());
        orderDeliveryReq.setDeliveryStatus("6");
        HemsBaseData baseData = commonClient.constructHemsBaseData(orderInfo.getMerCode(), storeInfo.getPlatformCode(), storeInfo.getOnlineClientCode(), storeInfo.getSessionKey());
        commonClient.orderDeliveryOut(orderDeliveryReq, baseData);
    }

    private static String transformOrderStatus(String sendOrderStatus) {
        String o2oOrderStatus = "";
        switch (sendOrderStatus) {

            case "35":
                o2oOrderStatus = "20";
                break;
            case "37":
            case "40":
            case "45":
                o2oOrderStatus = "30";
                break;
            case "54":
                o2oOrderStatus = "40";
                break;
            case "58":
            case "126":
                o2oOrderStatus = "100";
                break;
            case "33":
            case "110":
            case "130":
                o2oOrderStatus = "101";
                break;

        }

        return o2oOrderStatus;
    }


    private void sendOrderLogByOrderStatus(String transType, OrderInfo orderInfo, String sendOrderStatus) {


        switch (sendOrderStatus) {
            case "37":
                HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, orderInfo.getMerCode(), orderInfo.getOrderNo(), orderInfo.getOrderState(), orderInfo.getErpState(), OrderLogEnum.PICK_CONFIRM.getAction(), "雨诺-已完成拣货", null);
                break;
            case "40":
                HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, orderInfo.getMerCode(), orderInfo.getOrderNo(), orderInfo.getOrderState(), orderInfo.getErpState(), OrderLogEnum.CALL_RIDER.getAction(), "雨诺-呼叫骑手", null);
                break;
            case "45":
                HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, orderInfo.getMerCode(), orderInfo.getOrderNo(), orderInfo.getOrderState(), orderInfo.getErpState(), OrderLogEnum.RIDER_ARRIVED_STORE.getAction(), "雨诺-" + OrderLogEnum.RIDER_ARRIVED_STORE.getInfo(), null);
                break;
            case "54":
                HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, orderInfo.getMerCode(), orderInfo.getOrderNo(), orderInfo.getOrderState(), orderInfo.getErpState(), OrderLogEnum.RIDER_PICKED_GOODS.getAction(), "雨诺-" + OrderLogEnum.RIDER_PICKED_GOODS.getInfo(), null);
                break;
            case "58":
                HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, orderInfo.getMerCode(), orderInfo.getOrderNo(), orderInfo.getOrderState(), orderInfo.getErpState(), OrderLogEnum.SYNC_PLAT_ORDER_STATE.getAction(), "雨诺-" + "订单消息触发:" + OrderLogEnum.getSyncPlatOrderStateInfo(orderInfo.getOrderState(), 100), null);
                break;
            case "110":
            case "130":
                HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, orderInfo.getMerCode(), orderInfo.getOrderNo(), orderInfo.getOrderState(), orderInfo.getErpState(), OrderLogEnum.CANCEL_ORDER.getAction(), "雨诺-" + OrderLogEnum.getCancelOrderInfo("异常订单-取消"), null);
                break;
            case "126":
                HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, orderInfo.getMerCode(), orderInfo.getOrderNo(), orderInfo.getOrderState(), orderInfo.getErpState(), OrderLogEnum.RETURN_MONEY_AND_GOODS.getAction(), "雨诺-" + OrderLogEnum.RETURN_MONEY_AND_GOODS.getInfo(), null);
                break;

        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void afterSaveOrder(XyOrderCreatFacade facade) {
        OrderInfo orderInfo = facade.getOrderInfo();
        OrderInfo old = orderInfoMapper.selectBaseByUnique(orderInfo.getThirdOrderNo(), orderInfo.getThirdPlatformCode());
        if (Objects.nonNull(old)) {
            orderInfoMapper.delete(Wrappers.<OrderInfo>query().lambda().eq(OrderInfo::getId, old.getId()));
            orderPayInfoMapper.delete(Wrappers.<OrderPayInfo>query().lambda().eq(OrderPayInfo::getOrderNo, old.getOrderNo()));
            erpBillInfoMapper.delete(Wrappers.<ErpBillInfo>query().lambda().eq(ErpBillInfo::getOrderNo, old.getOrderNo()));
            orderDeliveryAddressMapper.delete(Wrappers.<OrderDeliveryAddress>query().lambda().eq(OrderDeliveryAddress::getOrderNo, old.getOrderNo()));

            orderDeliveryRecordMapper.delete(Wrappers.<OrderDeliveryRecord>query().lambda().eq(OrderDeliveryRecord::getOrderNo, old.getOrderNo()));
            orderDetailMapper.delete(Wrappers.<OrderDetail>query().lambda().eq(OrderDetail::getOrderNo, old.getOrderNo()));
            orderGiftInfoMapper.delete(Wrappers.<OrderGiftInfo>query().lambda().eq(OrderGiftInfo::getOrderNo, old.getOrderNo()));
            orderDeliveryLogMapper.delete(Wrappers.<OrderDeliveryLog>query().lambda().eq(OrderDeliveryLog::getOrderNo, old.getOrderNo()));
            orderPrescriptionMapper.delete(Wrappers.<OrderPrescription>query().lambda().eq(OrderPrescription::getOrderNo, old.getOrderNo()));
        }


        orderPayInfoMapper.insert(facade.getOrderPayInfo());
        erpBillInfoMapper.insert(facade.getBillInfo());
        orderDeliveryAddressMapper.insert(facade.getOrderDeliveryAddress());
        orderDeliveryRecordMapper.insert(facade.getOrderDeliveryRecord());
        if (CollectionUtils.isNotEmpty(facade.getOrderGiftInfos())) {
            orderGiftInfoMapper.insertBatch(facade.getOrderGiftInfos());
        }
        if (CollectionUtils.isNotEmpty(facade.getOrderDetails())) {
            orderDetailMapper.insertBatch(facade.getOrderDetails());
            List<OrderDetail> orderDetails = orderDetailMapper.selectAllListByOrderNo(orderInfo.getOrderNo());
            facade.setOrderDetails(orderDetails);
        }
        if (Objects.nonNull(facade.getOrderPrescriptionDto())) {
            orderInfo.setOrderType(OrderTypeEnum.PRESCRIPTION_ORDER.getCode());
            orderInfo.setPrescriptionStatus(1);
            orderPrescriptionMapper.insert(facade.getOrderPrescriptionDto());
        }
        orderDeliveryLogManager.saveOrderDeliveryLog(facade.getOrderDeliveryRecord().getOrderNo(), facade.getOrderDeliveryRecord().getState(), facade.getOrderDeliveryRecord().getDeliveryPlatName(), facade.getOrderDeliveryRecord().getRiderName(), facade.getOrderDeliveryRecord().getRiderPhone(), "数据为迁移订单，原订单状态为：" + facade.getOrderState().getCode() + ":" + facade.getOrderState().getMsg());

        orderInfoMapper.insert(orderInfo);
        // orderLog
        HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, orderInfo.getMerCode(), orderInfo.getOrderNo(), orderInfo.getOrderState(), orderInfo.getErpState(), OrderLogEnum.CREATE_YN_OLD_ORDER.getAction(), OrderLogEnum.getCreateYnOldOrderInfo(orderInfo.getOrderNo(), orderInfo.getOrderState()), null);

    }




    @Transactional(rollbackFor = Exception.class)
    public void afterSaveFreightOrder(XyOrderCreatFacade facade,Long orderNo) {
        OrderInfo orderInfo = facade.getOrderInfo();
        OrderInfo old = orderInfoMapper.selectBaseByUnique(orderInfo.getThirdOrderNo(), orderInfo.getThirdPlatformCode());
        if (Objects.nonNull(old)) {
            List<OrderDetail> orderDetails = orderDetailMapper.selectAllListByOrderNo(old.getOrderNo());
            orderInfoMapper.delete(Wrappers.<OrderInfo>query().lambda().eq(OrderInfo::getId, old.getId()));
            orderPayInfoMapper.delete(Wrappers.<OrderPayInfo>query().lambda().eq(OrderPayInfo::getOrderNo, old.getOrderNo()));
            erpBillInfoMapper.delete(Wrappers.<ErpBillInfo>query().lambda().eq(ErpBillInfo::getOrderNo, old.getOrderNo()));
            orderDeliveryAddressMapper.delete(Wrappers.<OrderDeliveryAddress>query().lambda().eq(OrderDeliveryAddress::getOrderNo, old.getOrderNo()));
            orderDeliveryRecordMapper.delete(Wrappers.<OrderDeliveryRecord>query().lambda().eq(OrderDeliveryRecord::getOrderNo, old.getOrderNo()));
            orderDetailMapper.delete(Wrappers.<OrderDetail>query().lambda().eq(OrderDetail::getOrderNo, old.getOrderNo()));
            orderDeliveryLogMapper.delete(Wrappers.<OrderDeliveryLog>query().lambda().eq(OrderDeliveryLog::getOrderNo, old.getOrderNo()));
            orderPickInfoMapper.physicsDeleteByDetailId(orderDetails.get(0).getId());

        }
        orderPayInfoMapper.insert(facade.getOrderPayInfo());
        erpBillInfoMapper.insert(facade.getBillInfo());
        orderDeliveryAddressMapper.insert(facade.getOrderDeliveryAddress());
        orderDeliveryRecordMapper.insert(facade.getOrderDeliveryRecord());

        orderDetailMapper.insert(facade.getOrderDetail());
        // 运费单永远是单条商品明细
        List<OrderDetail> orderDetails = orderDetailMapper.selectAllListByOrderNo(orderInfo.getOrderNo());
        OrderPickInfo orderPickInfo = facade.getOrderPickInfo();
        orderPickInfo.setOrderDetailId(orderDetails.get(0).getId());
        orderPickInfoMapper.insert(orderPickInfo);
        orderDeliveryLogManager.saveOrderDeliveryLog(facade.getOrderDeliveryRecord().getOrderNo(), facade.getOrderDeliveryRecord().getState(), facade.getOrderDeliveryRecord().getDeliveryPlatName(), facade.getOrderDeliveryRecord().getRiderName(), facade.getOrderDeliveryRecord().getRiderPhone(), "平台配送转商家配送生成特殊运费单，请不要处理！：" + OrderStateEnum.getOrderState(orderInfo.getOrderState()).getCode() + ":" + OrderStateEnum.getOrderState(orderInfo.getOrderState()).getMsg());
        orderInfoMapper.insert(orderInfo);

        // 更新原订单编号
        orderInfoMapper.updateOrderFreightOrderNo(orderNo, facade.getOrderInfo().getOrderNo());


        // orderLog
        HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, orderInfo.getMerCode(), orderInfo.getOrderNo(),orderInfo.getOrderState(), orderInfo.getErpState(), OrderLogEnum.CREATE_FREIGHT_ORDER.getAction(), OrderLogEnum.getCreateFreightOrderInfo(orderInfo.getOrderNo(), orderInfo.getOrderState()), null);

    }


    @Transactional(rollbackFor = Exception.class)
    public void saveRefundFreightOrder(XyOrderCreatFacade facade) {
        OrderInfo orderInfo = facade.getOrderInfo();
        RefundOrder refundOrder = facade.getRefundOrder();
        List<RefundDetail> refundDetailList = facade.getRefundDetails();
        ErpRefundInfo erpRefundInfo = facade.getErpRefundInfo();
        OrderInfo updateOrder = new OrderInfo();
        updateOrder.setOrderNo(orderInfo.getOrderNo());
        updateOrder.setOrderState(OrderStateEnum.CLOSED.getCode());
        updateOrder.setRemindFlag(DsConstants.INTEGER_ZERO);
        updateOrder.setLockFlag(OrderLockFlagEnum.NOT_LOCK.getCode());
        orderInfoMapper.updateOrder(updateOrder);
        refundDetailMapper.insertBatch(refundDetailList);
        refundOrderMapper.insert(refundOrder);
        erpRefundInfoMapper.insert(erpRefundInfo);
        HydeeEsSyncClientAsync.refundLogToES(DsConstants.INTEGER_ONE, refundOrder, RefundLogEnum.CREATE_REFUND_ORDER, null, "运费平台退款单关联创建消息", null);

    }



    @Transactional(rollbackFor = Exception.class)
    public void deleteFreightOrder(Long orderNo) {
        OrderInfo old = orderInfoMapper.selectOrderInfo(orderNo);
        if (Objects.nonNull(old) && Objects.nonNull(old.getFreightOrderNo())) {
            Long freightOrderNo = old.getFreightOrderNo();
            List<OrderDetail> orderDetails = orderDetailMapper.selectAllListByOrderNo(freightOrderNo);
            old.setFreightOrderNo(null);
            orderInfoMapper.update(old,Wrappers.<OrderInfo>query().lambda().eq(OrderInfo::getId, old.getId()));
            orderPayInfoMapper.delete(Wrappers.<OrderPayInfo>query().lambda().eq(OrderPayInfo::getOrderNo, freightOrderNo));
            erpBillInfoMapper.delete(Wrappers.<ErpBillInfo>query().lambda().eq(ErpBillInfo::getOrderNo, freightOrderNo));
            orderDeliveryAddressMapper.delete(Wrappers.<OrderDeliveryAddress>query().lambda().eq(OrderDeliveryAddress::getOrderNo,freightOrderNo));
            orderDeliveryRecordMapper.delete(Wrappers.<OrderDeliveryRecord>query().lambda().eq(OrderDeliveryRecord::getOrderNo, freightOrderNo));
            orderDetailMapper.delete(Wrappers.<OrderDetail>query().lambda().eq(OrderDetail::getOrderNo, freightOrderNo));
            orderDeliveryLogMapper.delete(Wrappers.<OrderDeliveryLog>query().lambda().eq(OrderDeliveryLog::getOrderNo, freightOrderNo));
            orderPickInfoMapper.physicsDeleteByDetailId(orderDetails.get(0).getId());

            OrderInfo freightOrderInfo = orderInfoMapper.selectOrderInfo(freightOrderNo);
            if (Objects.nonNull(freightOrderInfo)) {
                orderInfoMapper.delete(Wrappers.<OrderInfo>query().lambda().eq(OrderInfo::getId, freightOrderInfo.getId()));
            }
        }
    }
}



