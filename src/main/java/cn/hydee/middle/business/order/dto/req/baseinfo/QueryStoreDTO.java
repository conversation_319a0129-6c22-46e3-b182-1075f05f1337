package cn.hydee.middle.business.order.dto.req.baseinfo;


import cn.hydee.starter.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class QueryStoreDTO extends PageBase {
    @ApiModelProperty(value = "商家编码")
    @NotEmpty
    private String merCode;
    @ApiModelProperty(value = "门店名称")
    private String storeName;
    @ApiModelProperty(value = "是否上线门店（0非上线门店，1上线门店）")
   private Integer onlineStatus;
    @ApiModelProperty(value = "状态（0停用，1启用）")
    private Integer status;
    private Boolean excelFlag;
    @ApiModelProperty(value = "门店编码list")
    private List<String> stCodeList;
    @ApiModelProperty(value = "门店编码")
    private String stCode;
    @ApiModelProperty(value = "搜索关键字，用于通过门店编码/门店名称模糊查询")
    private String searchKey;
    @ApiModelProperty(value = "是否要支持普通快递才查出来 0不需要支持，1只有配置了普通快递的门店才能查出来")
    private Integer isdelivery;

    @ApiModelProperty(value = "B2C O2O")
    private String serviceMode;

    @ApiModelProperty(value = "门店ID列表")
    private List<String> list;

    @ApiModelProperty(value = "是否有电商云线上门店")
    private Integer onlineStoreStatus;


}
