package cn.hydee.middle.business.order.batch.export.processor.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.IoUtil;
import cn.hydee.middle.business.order.batch.export.constant.ExportTypeConstant;
import cn.hydee.middle.business.order.batch.export.dto.DeliveryFeeExportEconomizeReqDTO;
import cn.hydee.middle.business.order.batch.export.util.ExportExcelUtil;
import cn.hydee.middle.business.order.dto.rsp.DeliveryFeeEconomizeExportRspDto;
import cn.hydee.middle.business.order.dto.rsp.PageDeliveryFeeEconomizeRspDto;
import cn.hydee.middle.business.order.entity.ExportTask;
import cn.hydee.middle.business.order.service.DeliveryFeeEconomizeRecordService;
import cn.hydee.middle.business.order.util.ExceptionUtil;
import cn.hydee.middle.business.order.util.JsonUtil;
import cn.hydee.starter.dto.ErrorType;
import cn.hydee.starter.util.DateUtil;
import cn.hydee.starter.util.UUIDUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import net.lingala.zip4j.core.ZipFile;
import net.lingala.zip4j.model.ZipParameters;
import net.lingala.zip4j.util.Zip4jConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service(ExportTypeConstant.SERVICE_PREFIX + ExportTypeConstant.DELIVERY_FEE_ECONOMIZE_EXPORT)
@Slf4j
public class DeliveryFeeEconomizeExportProcessor extends AbstractExportTaskProcessor<DeliveryFeeEconomizeExportRspDto>{

  @Autowired
  private DeliveryFeeEconomizeRecordService deliveryFeeEconomizeRecordService;

  @Value("${exportExcelMaxRow:20000}")
  private Integer MAX_ROW;

  @Override
  protected Class<DeliveryFeeEconomizeExportRspDto> getExportClass() {
    return DeliveryFeeEconomizeExportRspDto.class;
  }
  @Override
  public List<DeliveryFeeEconomizeExportRspDto> getExportDateList(ExportTask task) {
    //此方法弃用，走doExport分页写入
    return Lists.newArrayList();
  }

  @Override
  public void doZipExport(ZipFile zipFile, Class exportClass, ExportTask task, String filePath){
    DeliveryFeeExportEconomizeReqDTO reqDTO = JsonUtil.json2Object(task.getRequestJson(), DeliveryFeeExportEconomizeReqDTO.class);
    if(Objects.isNull(reqDTO)){
      throw ExceptionUtil.getWarnException(ErrorType.PARA_ERROR);
    }
    int totalCount = deliveryFeeEconomizeRecordService.getCount(reqDTO);
    if(totalCount == 0){
      throw ExceptionUtil.getWarnException(ErrorType.EXCEL_ROW_NULL);
    }
    ZipParameters parameters = new ZipParameters();
    parameters.setCompressionMethod(Zip4jConstants.COMP_DEFLATE);
    parameters.setCompressionLevel(Zip4jConstants.DEFLATE_LEVEL_NORMAL);
    int xlsSum = totalCount % MAX_ROW == 0 ? (totalCount / MAX_ROW) : (totalCount / MAX_ROW) + 1;
    int currentPage = 1, pageSize = 1000;
    boolean flag = false;
    File existedXlsxFile = null;
    try {
      for (int i = 0; i < xlsSum; i++) {
        String fileName = getFileNamePrefix() + "-" + DateUtil.getTodayString() + "-" + UUIDUtil.generateUuid() + "-" + i;
        String absXlsxFilePath = filePath + File.separator + fileName + XLSX;
        existedXlsxFile = new File(absXlsxFilePath);
        if (existedXlsxFile.exists()) {
          existedXlsxFile.delete();
        }
        FileOutputStream out = new FileOutputStream(absXlsxFilePath);
        ExcelWriter excelWriter = EasyExcelFactory.write(out).head(getExportClass()).registerWriteHandler(getCellWriteHandler(pageSize)).build();
        // 创建一个写sheet
        WriteSheet writeSheet = ExportExcelUtil.getWriteSheet(getFileNamePrefix());
        int index = 0, totalPage = (MAX_ROW / pageSize);
        while (index < totalPage) {
          reqDTO.setCurrentPage(currentPage);
          reqDTO.setPageSize(pageSize);
          IPage<PageDeliveryFeeEconomizeRspDto> page = deliveryFeeEconomizeRecordService.pageDeliveryFeeEconomizeRecord(reqDTO);
          if (Objects.isNull(page) || CollUtil.isEmpty(page.getRecords())) {
            flag = true;
            break;
          }
          excelWriter.write(buildExportRsp(page.getRecords()), writeSheet);
          currentPage++;
          index++;
        }
        excelWriter.finish();
        IoUtil.close(out);
        zipFile.addFile(new File(absXlsxFilePath), parameters);
        existedXlsxFile.delete();
        if (flag) {
          break;
        }
      }
    }catch (Exception e){
      throw new RuntimeException(e);
    }finally {
      if (Objects.nonNull(existedXlsxFile)) {
        existedXlsxFile.delete();
      }
    }
  }

  private List<DeliveryFeeEconomizeExportRspDto> buildExportRsp(List<PageDeliveryFeeEconomizeRspDto> rspDtos){
    List<DeliveryFeeEconomizeExportRspDto> excelRspDtos = new ArrayList<>();
    rspDtos.forEach(e->{
      DeliveryFeeEconomizeExportRspDto excelRspDto = BeanUtil.toBean(e, DeliveryFeeEconomizeExportRspDto.class);
      excelRspDto.setOrderNoStr(String.valueOf(e.getOrderNo()));
      if(CollUtil.isNotEmpty(e.getDeliveryFeeMap())){
        String deliveryFees = e.getDeliveryFeeMap().entrySet().stream()
            .map(entry -> entry.getKey() + ":" + (Objects.isNull(entry.getValue())?"":entry.getValue()))
            .collect(Collectors.joining("\n"));
        excelRspDto.setDeliveryFees(deliveryFees);
      }
      excelRspDtos.add(excelRspDto);
    });
    return excelRspDtos;
  }


  @Override
  protected String getFileNamePrefix() {
    return "配送费节约记录";
  }
}
