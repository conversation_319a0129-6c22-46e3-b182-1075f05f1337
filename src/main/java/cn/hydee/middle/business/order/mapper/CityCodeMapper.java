package cn.hydee.middle.business.order.mapper;

import cn.hydee.middle.business.order.entity.CityCode;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 城市编码表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-07
 */
@Repository
public interface CityCodeMapper extends BaseMapper<CityCode> {
    CityCode selectByCity(String name);

    int updateByUnique(CityCode cityCode);
}
