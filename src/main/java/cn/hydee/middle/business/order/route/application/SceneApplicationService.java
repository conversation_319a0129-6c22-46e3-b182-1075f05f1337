package cn.hydee.middle.business.order.route.application;

import cn.hydee.middle.business.order.route.application.command.SceneOperateCommand;
import cn.hydee.middle.business.order.route.application.command.SceneQueryCommand;
import cn.hydee.middle.business.order.route.application.representation.RuleRepresentationDto;
import cn.hydee.middle.business.order.route.application.representation.SceneDetailsRepresentationDto;
import cn.hydee.middle.business.order.route.application.representation.SceneRepresentationDto;
import cn.hydee.middle.business.order.route.application.representation.SceneSelectedRepresentationDto;
import cn.hydee.starter.dto.PageBase;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/03/20 14:04
 **/
public interface SceneApplicationService {

  /**
   * 获取场景分页集合
   * @param sceneQueryCommand
   * @return
   */
  IPage<SceneRepresentationDto> pageSceneScene(SceneQueryCommand sceneQueryCommand);

  /**
   * 获取场景详情
   * @param sceneId
   * @return
   */
  SceneDetailsRepresentationDto getSceneDetail(String sceneId);

  /**
   * 保存场景
   * @param sceneOperateCommand
   * @return
   */
  int saveScene(SceneOperateCommand sceneOperateCommand);

  /**
   * 更新场景
   * @param sceneOperateCommand
   * @return
   */
  int updateScene(SceneOperateCommand sceneOperateCommand);

  /**
   * 删除场景
   * @param sceneId
   * @return
   */
  int deleteScene(String sceneId);

  /**
   * 获取规则数据集合
   * @return
   */
  List<RuleRepresentationDto> listRule();

  /**
   * 获取可选择场景
   * @param pageBase
   * @return
   */
  IPage<SceneSelectedRepresentationDto> pageSceneSelected(PageBase pageBase);

  /**
   * 获取场景下的规则明细
   * @param sceneId
   * @return
   */
  List<RuleRepresentationDto> sceneRuleDetail(String sceneId);
}
