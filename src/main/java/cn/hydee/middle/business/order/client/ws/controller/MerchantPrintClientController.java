package cn.hydee.middle.business.order.client.ws.controller;


import cn.hydee.middle.business.order.client.ws.dto.MerchantPrintVersionDTO;
import cn.hydee.middle.business.order.client.ws.dto.req.MerchantPrintAndConfigDTO;
import cn.hydee.middle.business.order.client.ws.dto.req.UpdateConfigDTO;
import cn.hydee.middle.business.order.client.ws.service.MerchantPrintVersionService;
import cn.hydee.middle.business.order.service.DsOfflineStoreConfigService;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/${api.version}/ds/print/merchant/client")
@Api(tags = "商户打印机管理")
public class MerchantPrintClientController extends AbstractController {

    @Autowired
    private MerchantPrintVersionService merchantPrintVersionService;
    @Autowired
    private DsOfflineStoreConfigService dsOfflineStoreConfigService;

    @ApiOperation(value = "线下门店对应的所有打印机信息", notes = "线下门店对应的所有打印机信息")
    @PostMapping("/allPrintClient")
    public ResponseBase<List<MerchantPrintVersionDTO>> allPrintClient(@RequestHeader("userId") String userId, @RequestHeader("merCode") String merCode,
                                                                      @RequestParam("organizationCode") String organizationCode) {
        return generateSuccess(merchantPrintVersionService.allPrintClientByStoreCode(merCode,organizationCode));
    }

    @ApiOperation(value = "线下门店配置打印机模板", notes = "线下门店配置打印机模板")
    @PostMapping("/updateTemplateById")
    public ResponseBase<String> updateTemplateById(@RequestHeader("userId") String userId, @RequestHeader("merCode") String merCode
            ,@RequestParam("id") Long id,@RequestParam("printTemplateIdList") List<Long> printTemplateIdList) {
        merchantPrintVersionService.updateEntityById(id,printTemplateIdList);
        return generateSuccess("更新成功");
    }

    @ApiOperation(value = "线下门店删除打印机", notes = "线下门店删除打印机")
    @PostMapping("/deletePrintClientById")
    public ResponseBase<String> deletePrintClientById(@RequestHeader("userId") String userId, @RequestHeader("merCode") String merCode
            ,@RequestParam("idList") List<Long> idList) {
        merchantPrintVersionService.deleteEntityById(idList);
        return generateSuccess("删除成功");
    }

    @ApiOperation(value = "线下门店对应的所有打印机信息(含门店配置)", notes = "线下门店对应的所有打印机信息(含门店配置)")
    @PostMapping("/allPrintClientConfig")
    public ResponseBase<MerchantPrintAndConfigDTO> allPrintClientConfig(@RequestHeader("userId") String userId, @RequestHeader("merCode") String merCode,
                                                                        @RequestParam("organizationCode") String organizationCode) {
        return generateSuccess(merchantPrintVersionService.allPrintClientConfig(merCode,organizationCode));
    }

    @ApiOperation(value = "开关线下门店配置", notes = "开关线下门店配置")
    @PostMapping("offline/updateConfig")
    public ResponseBase<String> updateConfig(@RequestHeader("userId") String userId, @RequestHeader("merCode") String merCode
            , @RequestBody UpdateConfigDTO updateConfigDTO) {
        updateConfigDTO.setMerCode(merCode);
        dsOfflineStoreConfigService.updateConfig(updateConfigDTO);
        return generateSuccess("更新成功");
    }
}

