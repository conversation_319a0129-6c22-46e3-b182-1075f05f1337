package cn.hydee.middle.business.order.yxtadapter.domain.correctiontool.qry.pick;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/9
 * @since 1.0
 */
@Data
public class OrderPickQryResponse implements Serializable {

    @ApiModelProperty(value ="商品图片")
    private String mainPic;

    @ApiModelProperty(value ="商品名称")
    private String commodityName;

    @ApiModelProperty(value ="商品三方平台编码")
    private String platformSkuId;

    @ApiModelProperty(value ="erp编码")
    private String erpCode;

    @ApiModelProperty(value ="条形编码")
    private String barCode;

    @ApiModelProperty(value ="商品规格")
    private String commoditySpec;

    @ApiModelProperty(value ="商品数量")
    private Integer commodityCount;

    @ApiModelProperty(value = "商品批号")
    List<CommodityBatchDto> batchStockList;
}
