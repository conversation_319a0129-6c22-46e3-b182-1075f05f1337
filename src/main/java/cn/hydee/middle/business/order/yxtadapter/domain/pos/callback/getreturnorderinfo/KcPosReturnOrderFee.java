package cn.hydee.middle.business.order.yxtadapter.domain.pos.callback.getreturnorderinfo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR>
 * Date:  2023/9/6
 */
@Data
public class KcPosReturnOrderFee implements java.io.Serializable {

    /**
     * 费用编码
     */
    private String FEECODE;

    /**
     * 费用名
     */
    private String FEENAME;


    /**
     * POS中支付方式的代码
     */
    private String PAYTYPECODE;


    /**
     * 支付方式，这里的文本是线上平台传回的文本，未必和POS支付方式代码一一对应
     */
    private String PAYTYPE;
    /**
     * 费用金额
     */
    private BigDecimal AMOUNT;


    /**
     * 第三方支付支付订单号
     */
    private String PAYODERENO;
    /**
     * 费用类型（0：优惠信息；1：支付信息；2：运费、包装费等其他费用）
     */
    private int FEETYPE;
    /**
     * 交易流水号
     */
    private String PAYBATCHNO;
    
}
