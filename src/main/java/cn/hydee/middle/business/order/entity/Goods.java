package cn.hydee.middle.business.order.entity;

import lombok.*;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class Goods {
    /**
     * 挂单号
     */
    private String  cfNo;
    /**
     * 商品名称
     */
    private String  goodsName;
    /**
     * 生产厂家
     */
    private String  factory;
    /**
     * 规格
     */
    private String specs;
    /**
     * 单位
     */
    private String  unit;
    /**
     * 数量
     */
    private String quantity;
    /**
     * 剂型
     */
    private String  dosageForm;
    /**
     * 创建时间
     */
    private String  createTime;
    /**
     * 用法
     */
    private String usageForm;
    /**
     * 用量
     */
    private String dosage;
    /**
     * 用药频次
     */
    private String frequency;
    /**
     * 药物编号
     */
    private  String drugId;
    /**
     * 批次
     */
    private  String  bats;
    /**
     * 库存
     */
    private String  stock;

    /**
     * erp商品编码
     */
    private String erpCode;

}
