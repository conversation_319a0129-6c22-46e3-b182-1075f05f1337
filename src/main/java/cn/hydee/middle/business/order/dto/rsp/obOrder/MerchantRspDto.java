
package cn.hydee.middle.business.order.dto.rsp.obOrder;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MerchantRspDto implements Serializable {
	private static final long serialVersionUID = 4753082175645372321L;

	@ApiModelProperty(value = "详细地址")
	private String address;
	@ApiModelProperty(value = "区域")
	private String area;
	@ApiModelProperty(value = "区域Id")
	private Integer areaId;
	@ApiModelProperty(value = "所属事业部")
	private String bedept;
	@ApiModelProperty(value = "城市")
	private String city;
	@ApiModelProperty(value = "企业名称")
	private String companyName;
	@ApiModelProperty(value = "联系电话")
	private String contactInfo;
	@ApiModelProperty(value = "创建人")
	private String createName;
	@ApiModelProperty(value = "创建时间")
	private Date createTime;
	@ApiModelProperty(value = "客服电话")
	private String customerMobile;
	@ApiModelProperty(value = "企业微信Appid")
	private String enterpriseWxAppid;
	@ApiModelProperty(value = "商家ERP类型")
	private Integer erpType;
	@ApiModelProperty(value = "ERP代理地址")
	private String erpUrl;
	@ApiModelProperty(value = "ID")
	private String id;
	@ApiModelProperty(value = "是否新老商城(0-否，1-是)")
	private Integer isNewMarket;
	@ApiModelProperty(value = "是否同步白名单：是-1；否-0")
	private Integer isSyncWhite;
	@ApiModelProperty(value = "纬度")
	private String latitude;
	@ApiModelProperty(value = "经度")
	private String longitude;
	@ApiModelProperty(value = "商家编码")
	private String merCode;
	@ApiModelProperty(value = "商家GUID,字段已删除")
	private String merGuid;
	@ApiModelProperty(value = "商家LOGO")
	private String merLogo;
	@ApiModelProperty(value = "商家名称")
	private String merName;
	@ApiModelProperty(value = "商家介绍")
	private String merRemark;
	@ApiModelProperty(value = "商家状态")
	private Integer merStatus;
	@ApiModelProperty(value = "商家类型")
	private Integer merType;
	@ApiModelProperty(value = "修改人")
	private String modifyName;
	@ApiModelProperty(value = "上线终端")
	private Integer onlineClient;
	@ApiModelProperty(value = "上线门店")
	private Integer onlineStore;
	@ApiModelProperty(value = "上线门店数")
	private Integer onlineStoreNumber;
	@ApiModelProperty(value = "负责人邮箱")
	private String principalMail;
	@ApiModelProperty(value = "负责人电话")
	private String principalMobile;
	@ApiModelProperty(value = "负责人名称")
	private String principalName;
	@ApiModelProperty(value = "省份")
	private String province;
	@ApiModelProperty(value = "公众号原始id")
	private String publicNoId;
	@ApiModelProperty(value = "公众号昵称")
	private String publicNoNick;
	@ApiModelProperty(value = "备注")
	private String remark;
	@ApiModelProperty(value = "销售姓名")
	private String saleName;
	@ApiModelProperty(value = "销售电话")
	private String saleTel;
	@ApiModelProperty(value = "门店数")
	private Integer storeNumber;

}
