package cn.hydee.middle.business.order.account.check.base.enums;

import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2021年07月09日 12:47
 */
public enum TCGDeliveryStatusEnum {
    // 转换成oms status 0待呼叫 1待接单，2待取货，3配送中，4已签收, 5已取消，6已过期，7异常

    // 待接单
    INIT(1,30,"待接单"),
    // 物流接单待商家作业
    ACCEPTED(2,30,"物流接单待商家作业"),
    // 商家作业中
    WAREHOUSE_SENT(2,30,"商家作业中"),
    // 商家作业完成物流作业中
    DISPATCH(2,30,"商家作业完成物流作业中"),
    // 物流已揽收
    LOGISTIC_SHIP(2,30,"物流已揽收"),
    // 配送中
    SHIP(3,40,"配送中"),
    // 配送已妥投
    SIGN_SUCCESS(4,100,"配送已妥投"),
    // 配送异常
    SIGN_ERROR(7,30,"配送异常"),
    // 配送拒收
    SIGN_REFUSE(7,30,"配送拒收"),
    // 取消
    CANCELED(5,30,"取消");

    private Integer deliveryStatus;  // orderDeliveryRecord.State
    private Integer orderState;  // orderInfo.orderState
    private String desc;

    TCGDeliveryStatusEnum(Integer status, Integer orderState, String desc) {
        this.deliveryStatus = status;
        this.orderState = orderState;
        this.desc = desc;
    }

    public static Integer getDeliveryStatusByName(String name) {
        if (StringUtils.isEmpty(name)) {
            return 0;
        }
        for (TCGDeliveryStatusEnum value : TCGDeliveryStatusEnum.values()) {
            if (value.name().equals(name)) {
                return value.getDeliveryStatus();
            }
        }
        return 0;
    }

    public static Integer getOrderStateByName(String name) {
        if (StringUtils.isEmpty(name)) {
            return 30;
        }
        for (TCGDeliveryStatusEnum value : TCGDeliveryStatusEnum.values()) {
            if (value.name().equals(name)) {
                return value.getOrderState();
            }
        }
        return 30;
    }

    public static String getDescByName(String name) {
        if (StringUtils.isEmpty(name)) {
            return "";
        }
        for (TCGDeliveryStatusEnum value : TCGDeliveryStatusEnum.values()) {
            if (value.name().equals(name)) {
                return value.getDesc();
            }
        }
        return "";
    }

    public Integer getDeliveryStatus() {
        return deliveryStatus;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getOrderState() {
        return orderState;
    }
}
