/*
package cn.hydee.middle.business.order.storeautosync.domain.storeconfig.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hydee.middle.business.order.Enums.PlatformCodeEnum;
import cn.hydee.middle.business.order.dto.req.OrderRepairReqDto;
import cn.hydee.middle.business.order.entity.DsOnlineStore;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.entity.StoreBillConfig;
import cn.hydee.middle.business.order.feign.The3PlatformAdapterClient;
import cn.hydee.middle.business.order.mapper.OrderInfoMapper;
import cn.hydee.middle.business.order.mapper.StoreBillConfigMapper;
import cn.hydee.middle.business.order.service.OrderRepairService;
import cn.hydee.middle.business.order.service.rocket.MessageProducerService;
import cn.hydee.middle.business.order.storeautosync.constants.StoreSyncStatus;
import cn.hydee.middle.business.order.storeautosync.domain.storeconfig.entity.StoreAutoConfigDTO;
import cn.hydee.middle.business.order.storeautosync.facade.storeconfig.entity.StoreAutoConfigSyncMessage;
import cn.hydee.middle.business.order.storeautosync.infrastructure.mysql.entity.BaseStepEntity;
import cn.hydee.middle.business.order.storeautosync.infrastructure.mysql.entity.DsOnlineStoreAuthRecord;
import cn.hydee.middle.business.order.storeautosync.infrastructure.mysql.entity.DsOnlineStoreAutoConfigStep;
import cn.hydee.middle.business.order.storeautosync.infrastructure.mysql.entity.DsOnlineStorePullOrderRecord;
import cn.hydee.middle.business.order.storeautosync.infrastructure.mysql.service.DsOnlineStoreAuthRecordService;
import cn.hydee.middle.business.order.storeautosync.infrastructure.mysql.service.DsOnlineStorePullOrderRecordService;
import cn.hydee.middle.business.order.storeautosync.integration.entity.GetOrderIdListRequestDto;
import cn.hydee.middle.business.order.domain.ThirdPlatformRequest;
import cn.hydee.starter.dto.ResponseBase;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class OrderPullStepHandler extends StoreConfigAbstractHandler {

    @Autowired
    private MessageProducerService messageProducerService;

    @Autowired
    private DsOnlineStoreAuthRecordService dsOnlineStoreAuthRecordService;

    @Autowired
    private The3PlatformAdapterClient theThirdPlatFormClient;

    @Autowired
    private OrderRepairService orderRepairService;

    @Autowired
    private OrderInfoMapper orderInfoMapper;

    @Autowired
    private DsOnlineStorePullOrderRecordService dsOnlineStorePullOrderRecordService;

    @Autowired
    private StoreBillConfigMapper storeBillConfigMapper;

    @Override
    public Consumer<DsOnlineStoreAutoConfigStep> handle(StoreAutoConfigDTO onlineStore) {
        //发送mq异步处理
        StoreAutoConfigSyncMessage syncMessage = new StoreAutoConfigSyncMessage(this.getClass().getSimpleName(), onlineStore.getId().toString());
        SendResult sendResult = messageProducerService.storeAutoConfigSyncMessage(syncMessage);
        //处理中
        BaseStepEntity baseStepEntity = new BaseStepEntity();
        baseStepEntity.setCreateTime(LocalDateTime.now());
        baseStepEntity.setStatus(StoreSyncStatus.PROCESSING.getCode());
        baseStepEntity.setRemark(sendResult.getMsgId());
        if (!SendStatus.SEND_OK.equals(sendResult.getSendStatus())) {
            baseStepEntity.setStatus(StoreSyncStatus.FAIL.getCode());
            baseStepEntity.setRemark("mq发送失败");
        }
        return step -> step.setOrderPullStep(baseStepEntity);
    }

    @Override
    public Consumer<DsOnlineStoreAutoConfigStep> handleSync(DsOnlineStore onlineStore, final DsOnlineStoreAutoConfigStep step) {

        BaseStepEntity baseStepEntity = step.getOrderPullStep();

        //先判断处理状态
        if (baseStepEntity == null || !baseStepEntity.getStatus().equals(StoreSyncStatus.PROCESSING.getCode())) {
            return null;
        }
        baseStepEntity.setStatus(StoreSyncStatus.SUCCESS.getCode());
        baseStepEntity.setFinishTime(LocalDateTime.now());
        beforeCheck(onlineStore, baseStepEntity);
        if (baseStepEntity.getStatus().equals(StoreSyncStatus.FAIL.getCode())) {
            return configStep -> configStep.setOrderPullStep(baseStepEntity);
        }
        //获取需要补单的三方单号
        List<DsOnlineStorePullOrderRecord> pullOrderRecordList = getNeedRepairOrderList(onlineStore, step, baseStepEntity);
        if (baseStepEntity.getStatus().equals(StoreSyncStatus.FAIL.getCode())) {
            return configStep -> configStep.setOrderPullStep(baseStepEntity);
        }
        if (CollUtil.isEmpty(pullOrderRecordList)) {
            baseStepEntity.setRemark("补单成功 0条");
            return configStep -> configStep.setOrderPullStep(baseStepEntity);
        }
        //补单操作
        repairOrder(onlineStore, pullOrderRecordList, baseStepEntity);
        return configStep -> configStep.setOrderPullStep(baseStepEntity);
    }

    private void beforeCheck(DsOnlineStore onlineStore, BaseStepEntity baseStepEntity) {
        //判断机构配置
        if (StrUtil.isBlank(onlineStore.getOrganizationCode())) {
            baseStepEntity.setStatus(StoreSyncStatus.FAIL.getCode());
            baseStepEntity.setRemark("缺少机构配置");
            return;
        }
        //判断下账状态
        LambdaQueryWrapper<StoreBillConfig> configQueryWrapper = Wrappers.<StoreBillConfig>lambdaQuery()
                .eq(StoreBillConfig::getMerCode, onlineStore.getMerCode())
                .eq(StoreBillConfig::getPlatformCode, onlineStore.getPlatformCode())
                .eq(StoreBillConfig::getStoreCode, onlineStore.getOnlineStoreCode())
                .eq(StoreBillConfig::getClientCode, onlineStore.getOnlineClientCode());
        Integer count = storeBillConfigMapper.selectCount(configQueryWrapper);
        if (count <= 0) {
            baseStepEntity.setStatus(StoreSyncStatus.FAIL.getCode());
            baseStepEntity.setRemark("缺少下账配置");
        }
    }

    */
/**
     * 美团补单:
     * 1 通过店铺编码去授权记录中是否有对应的门店，若有则通过补单接口补单：授权成功时间——当前时间段订单；
     * 2 若授权记录无对应门店，则查询同步时间——当前时间进行补单；
     * 饿了么，京东到家，则查询同步时间——当前时间进行补单；
     * 补单成功，则记录补单成功 xx条，补单成功0条，也算成功；
     *//*

    private List<DsOnlineStorePullOrderRecord> getNeedRepairOrderList(DsOnlineStore onlineStore, DsOnlineStoreAutoConfigStep step, BaseStepEntity baseStepEntity) {
        LocalDateTime startTime = step.getCreateTime();
        if (onlineStore.getPlatformCode().equals(PlatformCodeEnum.MEITUAN.getCode())) {
            LambdaQueryWrapper<DsOnlineStoreAuthRecord> queryWrapper = Wrappers.<DsOnlineStoreAuthRecord>lambdaQuery()
                    .eq(DsOnlineStoreAuthRecord::getOnlineStoreCode, onlineStore.getOnlineStoreCode())
                    .orderByDesc(DsOnlineStoreAuthRecord::getAuthTime)
                    .last(" limit 1");
            List<DsOnlineStoreAuthRecord> storeAuthRecords = dsOnlineStoreAuthRecordService.list(queryWrapper);
            if (CollUtil.isNotEmpty(storeAuthRecords)) {
                DsOnlineStoreAuthRecord storeAuthRecord = storeAuthRecords.get(0);
                if (!storeAuthRecord.getAuthStatus().equals(StoreSyncStatus.SUCCESS.getCode())) {
                    baseStepEntity.setStatus(StoreSyncStatus.FAIL.getCode());
                    baseStepEntity.setRemark("拉取订单失败，原因：授权未完成");
                    return new ArrayList<>(0);
                }
                startTime = storeAuthRecord.getAuthTime();
            }
        }
        ThirdPlatformRequest<GetOrderIdListRequestDto> platformRequest = new ThirdPlatformRequest<>();
        platformRequest.setEccode(onlineStore.getPlatformCode());
        platformRequest.setClientid(onlineStore.getOnlineClientCode());
        GetOrderIdListRequestDto requestDto = new GetOrderIdListRequestDto();
        requestDto.setStoreCode(onlineStore.getOnlineStoreCode());
        requestDto.setStartDate(LocalDateTimeUtil.formatNormal(startTime));
        requestDto.setEndDate(LocalDateTimeUtil.formatNormal(LocalDateTime.now()));
        platformRequest.setBody(requestDto);
        ResponseBase<List<String>> thirdOrderResponse = theThirdPlatFormClient.getOrderIdListByTime(platformRequest);
        if (!thirdOrderResponse.checkSuccess()) {
            baseStepEntity.setStatus(StoreSyncStatus.FAIL.getCode());
            baseStepEntity.setRemark("拉取订单失败，原因：the3platform接口调用失败:" + thirdOrderResponse.getMsg());
            return new ArrayList<>(0);
        }
        List<String> thirdOrderNoList = thirdOrderResponse.getData();
        if (CollUtil.isEmpty(thirdOrderNoList)) {
            return new ArrayList<>(0);
        }
        LambdaQueryWrapper<OrderInfo> orderQueryWrapper = Wrappers.<OrderInfo>lambdaQuery()
                .select(OrderInfo::getThirdOrderNo)
                .eq(OrderInfo::getThirdPlatformCode, onlineStore.getPlatformCode())
                .in(OrderInfo::getThirdOrderNo, thirdOrderNoList);
        List<OrderInfo> orderInfos = orderInfoMapper.selectList(orderQueryWrapper);
        Set<String> hasExistThirdNoSet = new HashSet<>();
        if (CollUtil.isNotEmpty(orderInfos)) {
            hasExistThirdNoSet = orderInfos.stream().map(OrderInfo::getThirdOrderNo).collect(Collectors.toSet());
        }
        //保存记录
        Set<String> finalHasExistThirdNoSet = hasExistThirdNoSet;
        List<DsOnlineStorePullOrderRecord> pullOrderRecords = thirdOrderNoList.stream().map(thirdOrderNo -> {
            DsOnlineStorePullOrderRecord dsOnlineStorePullOrderRecord = new DsOnlineStorePullOrderRecord();
            dsOnlineStorePullOrderRecord.setStoreConfigStepId(step.getId());
            dsOnlineStorePullOrderRecord.setOnlineStoreId(onlineStore.getId());
            dsOnlineStorePullOrderRecord.setOnlineStoreCode(onlineStore.getPlatformCode());
            dsOnlineStorePullOrderRecord.setThirdOrderNo(thirdOrderNo);
            //标记已经同步的订单
            if (finalHasExistThirdNoSet.contains(thirdOrderNo)) {
                dsOnlineStorePullOrderRecord.setStatus(1);
            }
            return dsOnlineStorePullOrderRecord;
        }).collect(Collectors.toList());
        dsOnlineStorePullOrderRecordService.saveBatch(pullOrderRecords);
        return pullOrderRecords.stream().filter(record -> record.getStatus() == null).collect(Collectors.toList());
    }

    private void repairOrder(DsOnlineStore onlineStore, List<DsOnlineStorePullOrderRecord> pullOrderRecordList, BaseStepEntity baseStepEntity) {
        List<String> thirdOrderNoList = pullOrderRecordList.stream().map(DsOnlineStorePullOrderRecord::getThirdOrderNo).distinct().collect(Collectors.toList());
        //补单操作
        OrderRepairReqDto repairReqDto = new OrderRepairReqDto();
        repairReqDto.setRepairType(1);
        repairReqDto.setMerCode(onlineStore.getMerCode());
        repairReqDto.setClientCode(onlineStore.getOnlineClientCode());
        repairReqDto.setStoreCode(onlineStore.getOnlineStoreCode());
        repairReqDto.setPlatformCode(onlineStore.getPlatformCode());
        repairReqDto.setThirdOrderNoList(thirdOrderNoList);
        List<String> faildThirdOrderNoList = orderRepairService.repair(repairReqDto);
        if (CollUtil.isNotEmpty(faildThirdOrderNoList)) {
            baseStepEntity.setStatus(StoreSyncStatus.FAIL.getCode());
            baseStepEntity.setRemark("补单成功 " + (thirdOrderNoList.size() - faildThirdOrderNoList.size()) + "条，补单失败 " + faildThirdOrderNoList.size() + "条");
        } else {
            baseStepEntity.setRemark("补单成功 " + thirdOrderNoList.size() + "条");
        }
        pullOrderRecordList.forEach(record -> record.setStatus(faildThirdOrderNoList.contains(record.getThirdOrderNo()) ? 3 : 2));
        dsOnlineStorePullOrderRecordService.updateBatchById(pullOrderRecordList);
    }

    @Override
    public Integer getOrder() {
        return 10;
    }

    @Override
    public Function<DsOnlineStoreAutoConfigStep, Boolean> dependencyHandlerResult() {
        return step -> step.getBillStep().getStatus().equals(StoreSyncStatus.SUCCESS.getCode())
                && step.getOrganizationStep().getStatus().equals(StoreSyncStatus.SUCCESS.getCode());
    }

    @Override
    public Consumer<DsOnlineStoreAutoConfigStep> callback(String errorMessage) {
        BaseStepEntity baseStepEntity = new BaseStepEntity();
        baseStepEntity.setStatus(StoreSyncStatus.FAIL.getCode());
        baseStepEntity.setFinishTime(LocalDateTime.now());
        baseStepEntity.setRemark(errorMessage);
        return step -> step.setOrderPullStep(baseStepEntity);
    }

    @Override
    public Consumer<DsOnlineStoreAutoConfigStep> repair(DsOnlineStore onlineStore, DsOnlineStoreAutoConfigStep step) {
        BaseStepEntity orderPullStep = new BaseStepEntity();
        orderPullStep.setCreateTime(LocalDateTime.now());
        if(step.getOrderPullStep() != null){
            orderPullStep.setCreateTime(step.getOrderPullStep().getCreateTime());
        }
        orderPullStep.setStatus(StoreSyncStatus.SUCCESS.getCode());
        orderPullStep.setFinishTime(LocalDateTime.now());
        //前置判断
        beforeCheck(onlineStore, orderPullStep);
        if (orderPullStep.getStatus().equals(StoreSyncStatus.FAIL.getCode())) {
            return configStep -> configStep.setOrderPullStep(orderPullStep);
        }
        //从记录表中查询补单失败的记录
        LambdaQueryWrapper<DsOnlineStorePullOrderRecord> orderPullRecordWrapper = Wrappers.<DsOnlineStorePullOrderRecord>lambdaQuery()
                .eq(DsOnlineStorePullOrderRecord::getStoreConfigStepId, step.getId())
                .eq(DsOnlineStorePullOrderRecord::getStatus,3);
        List<DsOnlineStorePullOrderRecord> pullOrderRecordList = dsOnlineStorePullOrderRecordService.list(orderPullRecordWrapper);
        if(CollUtil.isEmpty(pullOrderRecordList)){
            //获取需要补单的三方单号
            pullOrderRecordList = getNeedRepairOrderList(onlineStore, step, orderPullStep);
        }
        if (CollUtil.isEmpty(pullOrderRecordList)) {
            orderPullStep.setRemark("补单成功 0条");
            return configStep -> configStep.setOrderPullStep(orderPullStep);
        }
        //补单操作
        repairOrder(onlineStore, pullOrderRecordList, orderPullStep);
        return configStep -> {
            if (step.getOrganizationStep().getStatus() != StoreSyncStatus.SUCCESS.getCode()) {
                configStep.getOrganizationStep().setStatus(StoreSyncStatus.SUCCESS.getCode());
                configStep.getOrganizationStep().setFinishTime(LocalDateTime.now());
            }
            if (step.getBillStep().getStatus() != StoreSyncStatus.SUCCESS.getCode()) {
                configStep.getBillStep().setStatus(StoreSyncStatus.SUCCESS.getCode());
                configStep.getBillStep().setFinishTime(LocalDateTime.now());
            }
            configStep.setOrderPullStep(orderPullStep);
        };
    }
}
*/
