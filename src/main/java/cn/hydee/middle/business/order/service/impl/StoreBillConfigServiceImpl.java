package cn.hydee.middle.business.order.service.impl;

import cn.hydee.middle.business.order.Enums.ClientConfTypeEnum;
import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.Enums.DsErrorType;
import cn.hydee.middle.business.order.dto.StoreBillConfigDTO;
import cn.hydee.middle.business.order.dto.StoreBillInit;
import cn.hydee.middle.business.order.dto.StoreQueryBase;
import cn.hydee.middle.business.order.dto.req.ClientBillConfigReqDTO;
import cn.hydee.middle.business.order.dto.req.batch.StoreBillConfigInfo;
import cn.hydee.middle.business.order.dto.rsp.ClientBillConfigRspDTO;
import cn.hydee.middle.business.order.dto.rsp.ClientConfAndPayCodeResDto;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.SysEmployeeResDTO;
import cn.hydee.middle.business.order.entity.DsOnlineStore;
import cn.hydee.middle.business.order.entity.ErpPayMode;
import cn.hydee.middle.business.order.entity.StoreBillConfig;
import cn.hydee.middle.business.order.feign.MiddleBaseInfoClient;
import cn.hydee.middle.business.order.mapper.StoreBillConfigMapper;
import cn.hydee.middle.business.order.service.ErpPayModeService;
import cn.hydee.middle.business.order.service.PlatformPayModeRelationService;
import cn.hydee.middle.business.order.service.StoreBillConfigService;
import cn.hydee.middle.business.order.service.baseinfo.DsOnlineStoreService;
import cn.hydee.middle.business.order.storeautosync.constants.StoreBillConfigMode;
import cn.hydee.middle.business.order.util.ExceptionUtil;
import cn.hydee.middle.business.order.util.PageTool;
import cn.hydee.middle.business.order.yxtadapter.domainservice.mdm.MiddleBaseInfoClientAdapter;
import cn.hydee.starter.dto.ErrorType;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.starter.util.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 商家网店下账配置信息表 服务实现类
 * </p>
 *
 * <AUTHOR> 2020-08-03调整下账
 * @since 2020-07-02
 */
@Slf4j
@Service
public class StoreBillConfigServiceImpl extends ServiceImpl<StoreBillConfigMapper, StoreBillConfig> implements StoreBillConfigService {

    @Autowired
    private StoreBillConfigMapper clientBillConfigMapper;

    @Autowired
    private ErpPayModeService erpPayModeService;

    @Autowired
    private MiddleBaseInfoClientAdapter middleBaseInfoClientAdapter;

    @Lazy
    @Autowired
    private DsOnlineStoreService dsOnlineStoreService;

    @Autowired
    private PlatformPayModeRelationService platformPayModeRelationService;


    @Override
    public StoreBillConfig getLatestBillConfig(StoreQueryBase queryBase) {
        return clientBillConfigMapper.getLatestBillConfig(queryBase);
    }


    @Override
    public int initStoreBillConf(StoreBillInit billInit, DsOnlineStore defaultOnlineStore) {
        if (CollectionUtils.isEmpty(billInit.getStoreCodeList())) {
            return 0;
        }
        // 过滤配置已经存在的,并从创建中移除
        List<String> hasList = clientBillConfigMapper.findConfigForInit(billInit);
        billInit.getStoreCodeList().removeAll(hasList);
        if (CollectionUtils.isEmpty(billInit.getStoreCodeList())) {
            return 0;
        }
        //查询门店信息
        List<DsOnlineStore> dsOnlineStore = dsOnlineStoreService.queryManyDsStoreStore(billInit);
        if (CollectionUtils.isEmpty(dsOnlineStore)) {
            return 0;
        }
        // 构建默认的下账配置信息
        List<StoreBillConfig> addList = new ArrayList<>();
        StoreBillConfig defaultStoreBillConfig = null;

        if (Objects.isNull(defaultOnlineStore)) {
            defaultOnlineStore = dsOnlineStoreService.qryDefaultDsOnlineStore(billInit.getMerCode(), billInit.getPlatformCode());
        }

        if (Objects.nonNull(defaultOnlineStore) && Objects.nonNull(defaultOnlineStore.getOnlineStoreCode())) {
            StoreQueryBase queryBase = new StoreQueryBase();
            queryBase.setMerCode(billInit.getMerCode());
            queryBase.setPlatformCode(billInit.getPlatformCode());
            queryBase.setClientCode(defaultOnlineStore.getOnlineClientCode());
            queryBase.setStoreCode(defaultOnlineStore.getOnlineStoreCode());
            defaultStoreBillConfig = baseMapper.getLatestBillConfig(queryBase);
        }

        StoreBillConfig finalDefaultStoreBillConfig = defaultStoreBillConfig;
        dsOnlineStore.forEach(onlineStore -> {
            StoreQueryBase queryBase = new StoreQueryBase();
            queryBase.setStoreCode(onlineStore.getOnlineStoreCode());
            queryBase.setClientCode(billInit.getClientCode());
            queryBase.setMerCode(billInit.getMerCode());
            queryBase.setPlatformCode(onlineStore.getPlatformCode());
            StoreBillConfig entity = StoreBillConfigInfo.buildDefaultEntity(queryBase, finalDefaultStoreBillConfig);
            entity.setStoreName(onlineStore.getOnlineStoreName());
            entity.setPlatformName(onlineStore.getPlatformName());
            addList.add(entity);
        });
        if (CollectionUtils.isEmpty(addList)) {
            return 0;
        }
        int line = baseMapper.insertBatch(addList);
        if (line != 0) {
            erpPayModeService.initPayCodeConf(addList, defaultStoreBillConfig == null ? 0L : defaultStoreBillConfig.getId());
            // 处理平台支付
            platformPayModeRelationService.platformPayModeBuild(addList, defaultStoreBillConfig);
        }
        return line;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean createClientBillConf(String userId, StoreBillConfigInfo clientBillConfig) {
        // 验证商家门店是否合法，注释掉验证。等待后续删除
//		boolean validMerchantClient = isValidMerchantClient(configInfo.getMerCode(), configInfo.getPlatformCode(),
//				configInfo.getClientCode());
//		if (!validMerchantClient) {
//			String platName = configInfo.getPlatformCode();
//			if (!org.springframework.util.StringUtils.isEmpty(configInfo.getPlatformCode())) {
//				platName = PlatformCodeEnum.getByCode(configInfo.getPlatformCode()).getType();
//			}
//			String msg = String.format("商户编码[%s]平台[%s]网店编码[%s]不存在", configInfo.getMerCode(), platName,configInfo.getClientCode());
//			throw ExceptionUtil.getWarnException(DsErrorType.ONLINE_CLIENT_IS_NOT_EXIST.getCode(),msg);
//		}
        ResponseBase<SysEmployeeResDTO> sysEmployeeRes = middleBaseInfoClientAdapter.getEmployeeByUserId(userId);
        StoreBillConfig entity = StoreBillConfigInfo.buildEntity(clientBillConfig);
        entity.setConfType(ClientConfTypeEnum.PRESENT_CONF.getCode());
        SysEmployeeResDTO sysEmployeeResDTO = sysEmployeeRes.getData();
        String userName = userId;
        if (sysEmployeeResDTO != null) {
            if (!StringUtils.isEmpty(sysEmployeeResDTO.getEmpName())) {
                userName = sysEmployeeResDTO.getEmpName();
            } else {
                userName = sysEmployeeResDTO.getAccount();
            }
        }
        entity.setCreator(userName);
        //查询门店信息
        StoreQueryBase queryBase = new StoreQueryBase();
        queryBase.setPlatformCode(clientBillConfig.getPlatformCode());
        queryBase.setMerCode(clientBillConfig.getMerCode());
        queryBase.setClientCode(clientBillConfig.getClientCode());
        queryBase.setStoreCode(clientBillConfig.getStoreCode());
        DsOnlineStore dsOnlineClient = dsOnlineStoreService.queryDsStoreStore(queryBase);
        if (dsOnlineClient == null) {
            throw WarnException.builder().code(DsErrorType.ONLINE_STORE_UN_EXIST.getCode()).tipMessage(DsErrorType.ONLINE_STORE_UN_EXIST.getMsg()).build();
        }
        entity.setPlatformName(dsOnlineClient.getPlatformName());
        entity.setStoreName(dsOnlineClient.getOnlineStoreName());
        //设置是否开启收银员录入 、是否开启班次录入 、 是否下账到erp
        entity.setCheckerType(clientBillConfig.getCheckerType() == null ? 0 : clientBillConfig.getCheckerType());
        entity.setFrequencyType(clientBillConfig.getFrequencyType() == null ? 0 : clientBillConfig.getFrequencyType());
        entity.setBillErpSetting(clientBillConfig.getBillErpSetting() == null ? 0 : clientBillConfig.getBillErpSetting());
        clientBillConfigMapper.insert(entity);
        //查询最新新增的记录的主键ID
        Long newSetId = getLatestBillConfig(queryBase).getId();
        entity.setId(newSetId);
        //修改该商户平台门店下的其他配置为历史配置
        int line = updateClientBillConf(entity);
        if (line == 0) {
            log.info("商家：{} 对应信息下无历史下账配置", queryBase);
        }
        if (!CollectionUtils.isEmpty(clientBillConfig.getErpPayModeList())) {
            int size = clientBillConfig.getErpPayModeList().size();
            long count = clientBillConfig.getErpPayModeList().stream().map(ErpPayMode::getPayModeId).distinct().count();
            if (count < size) {
                //如果有重复则抛异常
                throw ExceptionUtil.getWarnException(ErrorType.DATA_ERROR.getCode(), "支付编码重复");
            }
            //修改该商户平台门店下的其他配置为历史配置
            erpPayModeService.updatePayMode(clientBillConfig.getMerCode(), clientBillConfig.getPlatformCode(), clientBillConfig.getClientCode(), clientBillConfig.getStoreCode());
            //新增支付方式配置
            //处理平台编码网店编码为空的情况
            clientBillConfig.getErpPayModeList().forEach(item -> {
                item.setPlatformCode(clientBillConfig.getPlatformCode());
                item.setClientCode(clientBillConfig.getClientCode());
            });
            erpPayModeService.saveErpPayModeBatch(userName, clientBillConfig.getMerCode(), clientBillConfig.getErpPayModeList(), newSetId);

        }
        // 处理平台支付对数据
        if (DsConstants.INTEGER_ONE.equals(clientBillConfig.getOnlinePayType())) {
            platformPayModeRelationService.saveOrUpdateConfigData(clientBillConfig, newSetId);
        }
        return true;
    }

    /**
     * 修改门店下账配置，其他配置为历史配置
     *
     * @param storeBillConfig 新配置
     * @return 影响的行数
     */
    private int updateClientBillConf(StoreBillConfig storeBillConfig) {
        //在新增当前使用配置后，修改之前使用过的配置为历史配置
        StoreBillConfig updateClientConf = new StoreBillConfig();
        updateClientConf.setConfType(ClientConfTypeEnum.HISTORY_CONF.getCode());
        //修改这个商户平台网店下的不为最后新增数据且配置类型不为初始化配置的修改为历史配置
        QueryWrapper<StoreBillConfig> qw = new QueryWrapper<>();
        //用id集合更新，线上sql并发修改问题
        qw.lambda().eq(StoreBillConfig::getMerCode, storeBillConfig.getMerCode()).eq(StoreBillConfig::getPlatformCode, storeBillConfig.getPlatformCode()).eq(StoreBillConfig::getClientCode, storeBillConfig.getClientCode()).eq(StoreBillConfig::getStoreCode, storeBillConfig.getStoreCode()).notIn(StoreBillConfig::getId, storeBillConfig.getId()).notIn(StoreBillConfig::getConfType, ClientConfTypeEnum.INIT_CONF.getCode());
        List<StoreBillConfig> storeBillConfigs = clientBillConfigMapper.selectList(qw);
        List<Long> ids = storeBillConfigs.stream().map(s -> s.getId()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        UpdateWrapper<StoreBillConfig> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().in(StoreBillConfig::getId, ids);
        return clientBillConfigMapper.update(updateClientConf, updateWrapper);
    }

//	private boolean isValidMerchantClient(String merCode, String platformCode, String clientCode) {
//		Integer count = clientService.lambdaQuery()
//				.eq(DsOnlineClient::getMerCode, merCode)
//				.eq(DsOnlineClient::getPlatformCode, platformCode)
//				.eq(DsOnlineClient::getOnlineClientCode, clientCode)
//				.count();
//		return count > 0;
//	}

    @Override
    public StoreBillConfig getBillConfigById(Long clientConfId) {
        StoreBillConfig billConfig = clientBillConfigMapper.getBillConfigById(clientConfId);
        if (billConfig == null) {
            throw ExceptionUtil.getWarnException(DsErrorType.ORDER_DATA_ERROR.getCode(), "网店下账配置信息不存在");
        }
        return billConfig;
    }

    @Override
    public PageDTO<ClientBillConfigRspDTO> getClientByPage(ClientBillConfigReqDTO req) throws InstantiationException, IllegalAccessException {
        IPage<StoreBillConfig> page = new Page<>(req.getCurrentPage(), req.getPageSize());
        // 分页查询
        lambdaQuery().eq(StoreBillConfig::getMerCode, req.getMerCode()).eq(StoreBillConfig::getPlatformCode, req.getPlatformCode()).eq(StoreBillConfig::getStoreCode, req.getStoreCode()).eq(StoreBillConfig::getClientCode, req.getClientCode()).orderByDesc(StoreBillConfig::getId).page(page);
        // 转换分页数据，除data之外的公共数据
        PageDTO<ClientBillConfigRspDTO> pageDTO = PageTool.buildPageDTOwithoutData(page);
        // 持久层获取的数据记录列表
        List<StoreBillConfig> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            pageDTO.setData(new ArrayList<>());
            return pageDTO;
        }
        List<ClientBillConfigRspDTO> resultList = BeanUtil.copyList(records, ClientBillConfigRspDTO.class);
        Map<Long, ClientBillConfigRspDTO> map = new HashMap<>();
        resultList.forEach(model -> map.put(model.getId(), model));
        //设置支付方式编码配置
        List<ErpPayMode> erpPayModeList = erpPayModeService.getErpPayCodeByClientConfId(map.keySet());
        erpPayModeList.forEach(erpPayMode -> {
            ClientBillConfigRspDTO dto = map.get(erpPayMode.getClientConfId());
            if (dto != null) {
                dto.addPayMode(erpPayMode);
            }
        });
        // 返回分页数据列表
        pageDTO.setData(resultList);
        return pageDTO;
    }

    @Override
    public ClientConfAndPayCodeResDto getClientConfigById(Long id) {
        ClientConfAndPayCodeResDto clientConfAndPayCodeResDto = new ClientConfAndPayCodeResDto();
        StoreBillConfig entity = getById(id);
        if (entity == null) {
            throw ExceptionUtil.getWarnException(ErrorType.RECORD_NOT_EXISTS);
        }

        StoreBillConfigInfo configInfo = StoreBillConfigInfo.buildConfigInfo(entity);
        configInfo.setBillErpSetting(entity.getBillErpSetting());
        configInfo.setStoreCode(entity.getStoreCode());
        configInfo.setConfType(entity.getConfType());
        clientConfAndPayCodeResDto.setClientBillConfig(configInfo);
        List<Long> ids = new ArrayList<>();
        ids.add(id);
        List<ErpPayMode> erpPayModeList = erpPayModeService.getErpPayCodeByClientConfId(ids);
        if (CollectionUtils.isEmpty(erpPayModeList)) {
            StoreQueryBase queryBase = new StoreQueryBase();
            queryBase.setMerCode(entity.getMerCode());
            queryBase.setPlatformCode(entity.getPlatformCode());
            queryBase.setClientCode(entity.getClientCode());
            queryBase.setStoreCode(entity.getStoreCode());
            erpPayModeService.initPayCodeConf(queryBase, id);
            erpPayModeList = erpPayModeService.getErpPayCodeByClientConfId(ids);
        }
        // 如果没有 配送费不下账、明细优惠不下账 则自动补充默认
        platformPayModeRelationService.buildBillMultiPayType(entity, clientConfAndPayCodeResDto, id);
        clientConfAndPayCodeResDto.setErpPayModeList(erpPayModeList);
        return clientConfAndPayCodeResDto;
    }

    @Override
    public ClientConfAndPayCodeResDto queryCurrentUse(StoreQueryBase queryBase) {
        StoreBillConfig storeBillConfig = getLatestBillConfig(queryBase);
        if (null == storeBillConfig) {
            log.info("网店下账配置信息不存在 [{}]", JSON.toJSONString(queryBase));
            return null;
        }
        StoreBillConfigInfo configInfo = StoreBillConfigInfo.buildConfigInfo(storeBillConfig);
        configInfo.setBillErpSetting(storeBillConfig.getBillErpSetting());
        configInfo.setStoreCode(storeBillConfig.getStoreCode());
        configInfo.setConfType(storeBillConfig.getConfType());
        ClientConfAndPayCodeResDto codeResDto = new ClientConfAndPayCodeResDto();
        List<Long> ids = new ArrayList<>();
        ids.add(storeBillConfig.getId());
        List<ErpPayMode> erpPayModeList = erpPayModeService.getErpPayCodeByClientConfId(ids);
        if (CollectionUtils.isEmpty(erpPayModeList)) {
            erpPayModeService.initPayCodeConf(queryBase, storeBillConfig.getId());
            erpPayModeList = erpPayModeService.getErpPayCodeByClientConfId(ids);
        }
        // 如果没有 配送费不下账、明细优惠不下账 则自动补充默认
        platformPayModeRelationService.buildBillMultiPayType(storeBillConfig, codeResDto, storeBillConfig.getId());
        codeResDto.setClientBillConfig(configInfo);
        codeResDto.setErpPayModeList(erpPayModeList);
        return codeResDto;
    }

    @Override
    public StoreBillConfigDTO selectByOrganizationCode(String merCode, String organizationCode) {
        StoreBillConfigDTO result = new StoreBillConfigDTO();
        List<StoreBillConfigDTO> configDTOList = clientBillConfigMapper.selectByOrganizationCode(merCode, organizationCode);
        if (CollectionUtils.isEmpty(configDTOList)) {
            log.info("找不到不按批号下账的下账配置 mercode {} organizationCode {}", merCode, organizationCode);
            return null;
        }
        Integer checkerType = configDTOList.stream().filter(c -> DsConstants.INTEGER_ONE.equals(c.getCheckerType())).collect(Collectors.toList()).size() > 0 ? DsConstants.INTEGER_ONE : DsConstants.INTEGER_ZERO;
        Integer frequencyType = configDTOList.stream().filter(c -> DsConstants.INTEGER_ONE.equals(c.getFrequencyType())).collect(Collectors.toList()).size() > 0 ? DsConstants.INTEGER_ONE : DsConstants.INTEGER_ZERO;
        result.setCheckerType(checkerType);
        result.setFrequencyType(frequencyType);
        return result;
    }

    @Override
    public int initStoreBillConfAuto(StoreBillConfigMode model, StoreBillInit billInit, DsOnlineStore defaultOnlineStore) {
        log.info("进入下账配置-自动构建-数据构建开始.....billInit:{}", billInit.toString());
        if (CollectionUtils.isEmpty(billInit.getStoreCodeList())) {
            return 0;
        }
        //查询门店信息
        List<DsOnlineStore> dsOnlineStore = dsOnlineStoreService.queryManyDsStoreStore(billInit);
        if (CollectionUtils.isEmpty(dsOnlineStore)) {
            return 0;
        }

        // 构建默认的下账配置信息
        List<StoreBillConfig> addList = new ArrayList<>();
        dsOnlineStore.forEach(onlineStore -> {
            StoreQueryBase queryBase = new StoreQueryBase();
            queryBase.setStoreCode(onlineStore.getOnlineStoreCode());
            queryBase.setClientCode(billInit.getClientCode());
            queryBase.setMerCode(billInit.getMerCode());
            queryBase.setPlatformCode(onlineStore.getPlatformCode());
            StoreBillConfig entity = new StoreBillConfig();
            switch (model) {
                case PLATFORM_DELIVERY:
                    entity = StoreBillConfigInfo.buildPlatformEntity(queryBase, null);
                    break;
                case MERCHANT_DELIVERY:
                    entity = StoreBillConfigInfo.buildMerchantEntity(queryBase, null);
                    break;
                case MICRO_SHOP_SPEC:
                    entity = StoreBillConfigInfo.buildMircoShopEntity(queryBase, null);
            }
            entity.setStoreName(onlineStore.getOnlineStoreName());
            entity.setPlatformName(onlineStore.getPlatformName());
            entity.setConfType(3);
            addList.add(entity);
        });
        if (CollectionUtils.isEmpty(addList)) {
            return 0;
        }
        // 先删除后新增
//        baseMapper.deleteBatchIds(addList.stream().map(StoreBillConfig::getId).collect(Collectors.toList()));
        //将已存在的配置置为历史配置
        LambdaUpdateWrapper<StoreBillConfig> updateWrapper = Wrappers.<StoreBillConfig>lambdaUpdate()
                .set(StoreBillConfig::getConfType, 2)
                .in(StoreBillConfig::getStoreCode, dsOnlineStore.stream().map(DsOnlineStore::getOnlineStoreCode).collect(Collectors.toList()))
                .eq(StoreBillConfig::getPlatformCode, billInit.getPlatformCode())
                .eq(StoreBillConfig::getClientCode, billInit.getClientCode());
        this.update(updateWrapper);
        int line = baseMapper.insertBatch(addList);
        if (line != 0) {
            erpPayModeService.initPayCodeConf(addList, 0L);
            // 处理平台支付
            platformPayModeRelationService.platformPayModeBuild(addList, null);
        }
        return line;
    }

}
