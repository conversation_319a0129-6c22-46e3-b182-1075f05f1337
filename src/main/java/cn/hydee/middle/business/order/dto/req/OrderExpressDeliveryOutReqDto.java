package cn.hydee.middle.business.order.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/23 10:30
 */
@Data
public class OrderExpressDeliveryOutReqDto extends OrderHandleReqDto {

    @ApiModelProperty(value = "配送员id")
    private String deliveryManId;

    @ApiModelProperty(value = "配送员名称")
    private String deliveryManName;

    @ApiModelProperty(value = "配送员电话")
    private String deliveryManPhone;

    @ApiModelProperty(value = "物流编码", required = true)
    @NotBlank(message = "物流编码不能为空")
    private String logisticsCompany;

    @ApiModelProperty(value = "物流名称", required = true)
    @NotBlank(message = "物流名称不能为空")
    private String logisticsName;

    @ApiModelProperty(value = "物流单号", required = true)
    @NotBlank(message = "物流单号不能为空")
    private String logisticsNo;

    @ApiModelProperty(value = "ERP支付编码")
    private String payCode;

}
