package cn.hydee.middle.business.order.batch.export.service.impl;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.batch.export.constant.ExportTypeConstant;
import cn.hydee.middle.business.order.batch.export.enums.TaskStatus;
import cn.hydee.middle.business.order.batch.export.processor.ExportTaskProcessor;
import cn.hydee.middle.business.order.batch.export.service.ExportTaskService;
import cn.hydee.middle.business.order.entity.ExportTask;
import cn.hydee.middle.business.order.feign.MiddleIdClient;
import cn.hydee.middle.business.order.mapper.ExportTaskMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

/**
 * 导出任务service
 *
 * <AUTHOR>
 * @date 2021/3/5 上午10:22
 */
@Service
@Slf4j
public class ExportTaskServiceImpl implements ExportTaskService {

    @Autowired
    private Map<String, ExportTaskProcessor> processorMap;

    @Autowired
    private ExportTaskMapper exportTaskMapper;

    @Autowired
    private MiddleIdClient middleIdClient;

    @Override
    public void execute() {
        List<ExportTask> exportTaskList = exportTaskMapper.getListByState(TaskStatus.WAIT.getCode());
        if (!CollectionUtils.isEmpty(exportTaskList)) {
            CountDownLatch latch = new CountDownLatch(exportTaskList.size());

            for (ExportTask task : exportTaskList) {
                ExportTaskProcessor processor = getProcessor(task.getExportType());
                if (processor == null) {
                    throw new UnsupportedOperationException("Unsupported export task type : " + task.getExportType());
                }
                processor.execute(task, latch);
            }

            try {
                latch.await();
            } catch (InterruptedException e) {
                log.error("countDownLatch await error", e);
            }
        }
        List<ExportTask> expiredExportTaskList = exportTaskMapper.getListByStateAndTime(TaskStatus.ING.getCode(), addHour(-5));
        if (!CollectionUtils.isEmpty(expiredExportTaskList)) {
            for (ExportTask task : expiredExportTaskList) {
                exportTaskMapper.updateStateAndMessage(task.getId(), TaskStatus.FAIL.getCode(), "导出超时，请重试");
            }
        }
    }

    @Override
    public void executeById(Long id) {
        ExportTask task = exportTaskMapper.selectById(id);
        if (task == null) {
            throw new UnsupportedOperationException("task is null,id: " + id);
        }
        execute(task);
    }

    @Override
    public Long executeCopyId(Long id) {
        ExportTask existTask = exportTaskMapper.selectById(id);
        if (existTask == null) {
            throw new UnsupportedOperationException("task is null,id: " + id);
        }
        ExportTask newTask = new ExportTask();
        BeanUtils.copyProperties(existTask,newTask);
        Long newId = middleIdClient.getId(1).get(0);
        newTask.setId(newId);
        newTask.setMerCode(existTask.getMerCode());
        newTask.setRequestJson(existTask.getRequestJson());
        newTask.setExportCount(existTask.getExportCount());
        newTask.setStatus(TaskStatus.WAIT.getCode());
        newTask.setExportType(existTask.getExportType());
        newTask.setIsDelete(DsConstants.INTEGER_ZERO);
        newTask.setCreateUserId(existTask.getCreateUserId());
        newTask.setCreateTime(new Date());
        newTask.setModifyTime(new Date());
        newTask.setCreateName(existTask.getCreateName());
        exportTaskMapper.insert(newTask);
        execute(newTask);
        return newId;
    }

    @Override
    public void execute(ExportTask task) {
        ExportTaskProcessor processor = getProcessor(task.getExportType());
        if (processor == null) {
            throw new UnsupportedOperationException("Unsupported export task type : " + task.getExportType());
        }
        processor.execute(task);
    }

    private ExportTaskProcessor getProcessor(String exportType) {
        return processorMap.get(ExportTypeConstant.SERVICE_PREFIX + exportType);
    }

    private Date addHour(int count) {
        Calendar c = Calendar.getInstance();
        //加减小时数
        c.add(Calendar.HOUR, count);
        return c.getTime();
    }
}
