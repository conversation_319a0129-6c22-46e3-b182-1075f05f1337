package cn.hydee.middle.business.order.refund.calc;

import cn.hydee.middle.business.order.entity.ErpRefundInfo;
import cn.hydee.middle.business.order.refund.context.RefundOrderContext;

/**
 * 计算退款下账金额服务
 * <AUTHOR>
 * @version 3.9.2.2
 * @date 2020/10./13
 */
public interface ICalculateRefundSinkAmountService {

    /**
     * 计算退款下账金额【纯计算，不保存数据库】
     * @param context 退款上下文信息
     * @return
     */
    ErpRefundInfo calcRefundSinkAmount(RefundOrderContext context);

    /**
     * 计算退款下账金额【纯计算，不保存数据库】
     * @param context 退款上下文信息
     * @return
     */
    ErpRefundInfo calcRefundSinkAmountDouDian(RefundOrderContext context);

    /**
     * 初始部分退款下账金额
     * @param context 退款信息上下文
     */
    void initRefundSinkAmount(RefundOrderContext context);

    /**
     * 刷新部分退款下账金额
     * @param context 退款信息上下文
     */
    void refreshRefundSinkAmount(RefundOrderContext context);

    /**
     * 计算退款下账金额
     * @param context 退款信息上下文
     * @return 未持久化对象
     */
    ErpRefundInfo calculateRefundAmountWithConfig(RefundOrderContext context);

    /**
     * 计算全部退款下账金额
     *
     * @param context 退款信息上下文
     * @param saveDB 是否保存到数据库
     * @return 退款下账金额信息
     */
    ErpRefundInfo calculateAllRefundAmount(RefundOrderContext context, boolean saveDB);

    /**
     * 计算剩余明细全部退款的下账金额
     * @param context 退款信息上线文
     * @return 退款下账金额信息
     */
    ErpRefundInfo calculateRemainingRefundAmount(RefundOrderContext context);

    /**
     * 计算明细下账金额
     * @param context 退款上下文信息
     */
    void calculateDetailSinkAmount(RefundOrderContext context);

    /**
     * 保存退款下账信息
     * @param erpRefundInfo 退款下账信息
     */
    void saveErpRefundInfo(ErpRefundInfo erpRefundInfo);


    /**
     * 计算明细下账金额
     * @param context 退款上下文信息
     */
    void calculateDetailSinkAmountV2(RefundOrderContext context);

}
