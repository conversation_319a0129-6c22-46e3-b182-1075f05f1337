package cn.hydee.middle.business.order.doris.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/06/17
 */
@Data
public class OrderReportStatisticsPageQueryReqDto extends OrderReportStatisticsQueryReqDto{

    @ApiModelProperty(value = "当前页，从第1页开始，不传默认为1")
    private int currentPage=1;
    @ApiModelProperty(value = "每页显示条数，不传默认20")
    private int pageSize=20;
    @ApiModelProperty(value = "排序字段，默认商家实收金额;字段参考返回对象名")
    private String sortField;
    @ApiModelProperty(value = "排序顺序：升序 asc 降序 desc，默认降序")
    private String sortSequence;

    private int getOffset() {
        return (currentPage - 1) * pageSize;
    }

}
