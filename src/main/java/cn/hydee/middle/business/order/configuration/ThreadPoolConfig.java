package cn.hydee.middle.business.order.configuration;

import cn.hippo4j.common.design.builder.ThreadFactoryBuilder;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;

import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/18 11:03
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "threadpool")
public class ThreadPoolConfig {
    public final static String ORDER_BUSINESS_ASYNC_THREAD_POOL = "orderBusinessAsyncThreadPool";
    public final static String ROUTE_RULE_CHECK_THREAD_POOL = "ruleCheckThreadPool";
    public final static String ROUTE_RULE_SCHEDULED_THREAD_POOL = "ruleScheduledThreadPool";
    // 延迟线程池
    public final static String SCHEDULED_THREAD_POOL = "scheduledThreadPool";

    private Integer logCoreSize;
    private Integer logMaxSize;
    private Long logKeepAliveTime;
    private Integer logCapacity;

    private Integer erpCoreSize;
    private Integer erpMaxSize;
    private Long erpKeepAliveTime;
    private Integer erpCapacity;

    private Integer ynOrderMoveCoreSize = 8;
    private Integer ynOrderMoveMaxSize = 32;
    private Long ynOrderMoveKeepAliveTime = 0L;
    private Integer ynOrderMoveCapacity = 128;

    private Integer storeAutoConfigCoreSize = 8;
    private Integer storeAutoConfigMaxSize = 48;
    private Long storeAutoConfigKeepAliveTime = 0L;
    private Integer storeAutoConfigCapacity = 128;

    private Integer queryCoreSize = 8;
    private Integer queryMaxSize = 48;
    private Long queryKeepAliveTime = 0L;
    private Integer queryCapacity = 128;

    private Integer asyncCoreSize = 8;
    private Integer asyncMaxSize = 48;
    private Long asyncKeepAliveTime = 0L;
    private Integer asyncCapacity = 128;

    @Bean("logThreadPool")
    public Executor logThreadPool() {
        ExecutorService executor = new ThreadPoolExecutor(logCoreSize, logMaxSize,
                logKeepAliveTime, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<Runnable>(logCapacity), new ThreadPoolExecutor.DiscardPolicy());
        return executor;
    }

    @Bean("erpThreadPool")
    public Executor erpThreadPool() {
        ExecutorService executor = new ThreadPoolExecutor(erpCoreSize, erpMaxSize,
                erpKeepAliveTime, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<Runnable>(erpCapacity), new ThreadPoolExecutor.DiscardPolicy());
        return executor;
    }

    @Bean("exceptionLogThreadPool")
    public Executor exceptionLogThreadPool() {
        ExecutorService executor = new ThreadPoolExecutor(logCoreSize, logMaxSize,
                logKeepAliveTime, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(logCapacity), new ThreadPoolExecutor.DiscardPolicy());

        return executor;
    }

    @Bean("ynOrderMoveThreadPool")
    public Executor ynOrderMoveThreadPool() {
        return new ThreadPoolExecutor(
                ynOrderMoveCoreSize,
                ynOrderMoveMaxSize,
                ynOrderMoveKeepAliveTime,
                TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(ynOrderMoveCapacity),
                new ThreadFactoryBuilder().prefix("yn-refund-order-sync").build()
        );
    }

    @Bean("assignmentThreadPool")
    public ThreadPoolExecutor assignmentThreadPool() {
        return new ThreadPoolExecutor(ynOrderMoveCoreSize, 20, ynOrderMoveCoreSize, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(ynOrderMoveCapacity), new CustomizableThreadFactory("assignment-thread-pool-"));
    }

    @Bean("storeAutoConfigThreadPool")
    public Executor storeAutoConfigThreadPool() {
        return new ThreadPoolExecutor(
                storeAutoConfigCoreSize,
                storeAutoConfigMaxSize,
                storeAutoConfigKeepAliveTime,
                TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(storeAutoConfigCapacity),
                new ThreadFactoryBuilder().prefix("store-auto-config-pool").build()
        );
    }

    /**
     * 查询并行线程池
     *
     * @return
     */
    @Bean("queryThreadPool")
    public Executor queryThreadPool() {
        return new ThreadPoolExecutor(
                queryCoreSize,
                queryMaxSize,
                queryKeepAliveTime,
                TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(queryCapacity),
                new ThreadFactoryBuilder().prefix("query-pool").build(), new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    /**
     * 异步线程池
     *
     * @return
     */
    @Bean(ORDER_BUSINESS_ASYNC_THREAD_POOL)
    public Executor asyncThreadPool() {
        return new ThreadPoolExecutor(
                asyncCoreSize,
                asyncMaxSize,
                asyncKeepAliveTime,
                TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(asyncCapacity),
                new ThreadFactoryBuilder().prefix("async-pool").build(), new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    @Bean(ROUTE_RULE_CHECK_THREAD_POOL)
    public ThreadPoolExecutor ruleCheckThreadPool() {
        return new ThreadPoolExecutor(
            1,
            5,
            asyncKeepAliveTime,
            TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(asyncCapacity),
            new ThreadFactoryBuilder().prefix("rule-pool").build(), new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    @Bean(ROUTE_RULE_SCHEDULED_THREAD_POOL)
    public ScheduledExecutorService ruleCheckSchedulerThreadPool() {
        return new ScheduledThreadPoolExecutor(
                3,
                new ThreadFactoryBuilder().prefix("rule-scheduled-pool").build(),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    @Bean("batchAccountDelayThreadPool")
    public ScheduledExecutorService batchAccountDelayThreadPool() {
        return new ScheduledThreadPoolExecutor(
            3,
            new ThreadFactoryBuilder().prefix("batch_account_delay_thread_pool").build(),
            new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    @Bean("bestDeliveryFeeThreadPool")
    public ThreadPoolExecutor bestDeliveryFeeThreadPool() {
        return new ThreadPoolExecutor(
            1,
            5,
            asyncKeepAliveTime,
            TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(asyncCapacity),
            new ThreadFactoryBuilder().prefix("best-delivery-fee-pool").build(), new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    @Bean(SCHEDULED_THREAD_POOL)
    public ScheduledExecutorService scheduledThreadPool() {
        return new ScheduledThreadPoolExecutor(
            10,
            new cn.hutool.core.thread.ThreadFactoryBuilder().setNamePrefix(SCHEDULED_THREAD_POOL)
                .build(),
            new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }
}
