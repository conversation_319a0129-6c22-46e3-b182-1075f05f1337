package cn.hydee.middle.business.order.batch.export.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/9/3 上午10:46
 */
@Data
public class ErpCheckStoreOrderLightDTO implements Serializable {

    private static final long serialVersionUID = 3110703493689375086L;
    @ColumnWidth(30)
    @ExcelProperty(value = "*平台订单号")
    private String thirdOrderNo;
    @ColumnWidth(30)
    @ExcelProperty(value = "*系统订单号")
    private String orderNo;
    @ColumnWidth(30)
    @ExcelProperty(value = "*oms下账金额")
    private BigDecimal totalAmountOms;
    @ColumnWidth(30)
    @ExcelProperty(value = "*差异原因")
    private String diffReason;
    @ColumnWidth(40)
    @ExcelProperty(value = "*ERP零售流水号")
    private String saleNo = "";
    @ColumnWidth(30)
    @ExcelProperty(value = "*erp零售金额")
    private String erpAmount = "";
    @ColumnWidth(30)
    @ExcelProperty(value = "备注")
    private String mark = "";
    @ColumnWidth(30)
    @ExcelProperty(value = "")
    private String _;
}
