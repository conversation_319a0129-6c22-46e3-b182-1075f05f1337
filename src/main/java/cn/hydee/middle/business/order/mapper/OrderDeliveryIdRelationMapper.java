package cn.hydee.middle.business.order.mapper;

import cn.hydee.middle.business.order.dto.DeliveryIdOrderNoRela;
import cn.hydee.middle.business.order.entity.OrderDeliveryIdRelation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 骑手配送ID与系统订单号的关联关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-09
 */
@Repository
public interface OrderDeliveryIdRelationMapper extends BaseMapper<OrderDeliveryIdRelation> {

    Long queryRecordByOrderNo(@Param("orderNo") Long orderNo);

    List<DeliveryIdOrderNoRela> qryIdOrderNoRelation(@Param("orderNoList") List<String> orderNoList);
}
