package cn.hydee.middle.business.order.service;

import cn.hydee.middle.business.order.dto.req.batch.StoreBillConfigInfo;
import cn.hydee.middle.business.order.dto.req.batch.StoreBillConfigBatchReqDto;
import cn.hydee.middle.business.order.dto.rsp.batch.StoreConfigRspDto;

/**
 * cn.hydee.middle.business.order.service
 * 不可理喻的部分 单独一个类处理
 * 单独放一个类来处理 批量更新下账配置 （一个配置要搞这么麻烦 不能理解  。。。。 批量我也就一条一条更新 顺便限下数量（单次只能更新200家））
 * <AUTHOR> Li
 * @version 1.0
 * @date 2020/9/1 10:18
 **/
public interface StoreBillBatchService {

    /**
     * 更新门店下账配置
     * @param userName
     * @param clientBillConfig
     * @return
     */
    Boolean createClientBillConf(String userName, StoreBillConfigInfo clientBillConfig);
    /**
     * 批量更新门店下账配置 具体处理
     * @param storeBillConfigBatchReqDto
     * @return
     */
    StoreConfigRspDto storeBillConfigBatch(StoreBillConfigBatchReqDto storeBillConfigBatchReqDto);
}
