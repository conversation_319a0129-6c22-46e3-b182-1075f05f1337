package cn.hydee.middle.business.order.route.application.representation;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
@ApiModel(description = "场景详情")
public class SceneDetailsRepresentationDto {

  @JsonSerialize(using = ToStringSerializer.class)
  @ApiModelProperty(value = "场景id")
  private Long id;

  @ApiModelProperty(value = "场景名称")
  private String sceneName;

  @ApiModelProperty(value = "场景类型")
  private String sceneType;

  @ApiModelProperty(value = "场景的规则id")
  private List<Long> ruleIds;

  @ApiModelProperty(value = "版本号")
  private Long version;
}
