package cn.hydee.middle.business.order.controller;

import cn.hydee.middle.business.order.entity.DsOnlineClient;
import cn.hydee.middle.business.order.service.baseinfo.DsOnlineClientService;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 线上网店前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-20
 */
@RestController
@RequestMapping("/${api.version}/ds/client")
@Api(tags = "线上网店相关接口")
public class OnlineClientController extends AbstractController {

    private final DsOnlineClientService onlineClientService;

    public OnlineClientController(DsOnlineClientService onlineClientService) {
        this.onlineClientService = onlineClientService;
    }

    @ApiOperation(value = "查询单条网店信息",notes = "查询单条网店信息")
    @GetMapping("/queryOnlineClientById/{onlineClientId}")
    public ResponseBase<DsOnlineClient> queryOnlineClientById(@PathVariable Long onlineClientId) {
        return generateSuccess(onlineClientService.queryOnlineClientById(onlineClientId));
    }

}
