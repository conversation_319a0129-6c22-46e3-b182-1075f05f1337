package cn.hydee.middle.business.order.dto.net;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/1/7 13:42
 */
@Data
public class CallRiderRspDto {
    /** 配送距离(单位：米) */
    private Double distance;
    /**	实际运费(单位：元)，运费减去优惠券费用*/
    private BigDecimal fee;
    /** 运费(单位：元)*/
    private BigDecimal deliverFee;
    /** 配送ID 创建订单生成*/
    private Long deliveryid;
    /** 配送平台生成ID */
    private String riderorderid;
}
