package cn.hydee.middle.business.order.dto.req.customer;

import cn.hydee.starter.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/7/22 11:06
 */
@Data
public class CustomerOrderNoRequest extends CustomerBaseUserRequest{
    @ApiModelProperty("订单号")
    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    @ApiModelProperty("平台编码")
    private String platformCode;



}
