
package cn.hydee.middle.business.order.service.rabbit.delayConsumer.orderPick;

import cn.hydee.middle.business.order.Enums.DeliveryTypeEnum;
import cn.hydee.middle.business.order.Enums.PlatformCodeEnum;
import cn.hydee.middle.business.order.dto.rsp.OnlineStoreInfoRspDto;
import cn.hydee.middle.business.order.entity.OrderDeliveryRecord;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.mapper.OrderDeliveryRecordMapper;
import cn.hydee.middle.business.order.mapper.OrderInfoMapper;
import cn.hydee.middle.business.order.service.rabbit.delayConsumer.base.BaseDelayMqMsg;
import cn.hydee.middle.business.order.service.rabbit.delayConsumer.base.IBusinessLogicHandel;
import cn.hydee.middle.business.order.v2.feign.hems.HemsCommonClient;
import cn.hydee.middle.business.order.v2.manager.base.BaseInfoManager;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.unified.model.HemsBaseData;
import cn.hydee.unified.model.order.OrderPickCompleteReq;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("orderPick2PlatformHandelService")
@Slf4j
public class OrderPick2PlatformHandelService implements IBusinessLogicHandel {


	@Autowired
	private HemsCommonClient hemsCommonClient;
	@Autowired
	private OrderInfoMapper orderInfoMapper;
	@Autowired
	private OrderDeliveryRecordMapper orderDeliveryRecordMapper;
	@Autowired
	private BaseInfoManager baseInfoManager;

	@Override
	public boolean businessLogicHandel(BaseDelayMqMsg message) {
		JSONObject params = JSONObject.parseObject(JSON.toJSONString(message.getData()));
		try {
			Long orderNo = params.getLong("orderNo");
			OrderInfo orderInfo = orderInfoMapper.selectBaseInfoByOrderNo(orderNo);
			OrderDeliveryRecord orderDeliveryRecord = orderDeliveryRecordMapper.selectByOrderNo(orderNo);
			OnlineStoreInfoRspDto storeInfo = baseInfoManager.getOnlineStoreInfo(orderInfo.getMerCode(),
					orderInfo.getThirdPlatformCode(), orderInfo.getClientCode(), orderInfo.getOnlineStoreCode());
			// 发送拣货完成通知
			HemsBaseData baseData = hemsCommonClient.constructHemsBaseData(orderInfo.getMerCode(), storeInfo.getPlatformCode(),
					storeInfo.getOnlineClientCode(), storeInfo.getSessionKey());
			OrderPickCompleteReq pickComplete = new OrderPickCompleteReq();
			pickComplete.setDeliveryType(orderDeliveryRecord.getDeliveryType());
			pickComplete.setOlOrderNo(orderInfo.getThirdOrderNo());
			pickComplete.setOlShopId(orderInfo.getOnlineStoreCode());
			pickComplete.setShopId(storeInfo.getOutShopId());
			// 美团订单且配送方式为美团平台配送且订单状态为待拣货、待配送
			// 进行拣货完成通知时，如果通知失败，过3分钟后再自动调用一次，调用4次都不成功则不再调用
			log.info("pick complete message start, olorderno:{},thirdPlatformCode:{},deliveryType:{}",
					orderInfo.getOrderNo(),orderInfo.getThirdPlatformCode(),orderDeliveryRecord.getDeliveryType());
			if ((PlatformCodeEnum.MEITUAN.getCode().equals(orderInfo.getThirdPlatformCode()) || PlatformCodeEnum.TY_O2O.getCode().equals(orderInfo.getThirdPlatformCode()))
					&& DeliveryTypeEnum.checkPlatformRider(orderDeliveryRecord.getDeliveryType())) {
				hemsCommonClient.pickCompleteMeiTuan(orderInfo.getOrderNo(), pickComplete , baseData);
			}else {
				// 其他平台  此处仅饿百的进来
				hemsCommonClient.pickCompleteWithRetry(orderNo,pickComplete, baseData);
			}
		}catch (WarnException e) {
			log.info("pickConfirm: pickCompleteNotify,orderNo:{} error:{}",params, e);
		}catch (Exception e) {
			log.error("pickConfirm: pickCompleteNotify,orderNo:{} error:{}",params, e);
		}
		return true;
	}

	@Override
	public void saveFailRecord(BaseDelayMqMsg message) {
	}

	@Override
	public void saveSuccessRecord(BaseDelayMqMsg message) {
	}
}
  
