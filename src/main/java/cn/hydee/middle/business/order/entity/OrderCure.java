package cn.hydee.middle.business.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/8/24
 */
@Data
public class OrderCure {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long orderNo;
    private Integer times;
    private Integer result;
    private Date createTime;
    private Date handleTime;
}
