package cn.hydee.middle.business.order.mapper;

import cn.hydee.middle.business.order.entity.OrderPayInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 支付信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-17
 */
@Repository
public interface OrderPayInfoMapper extends BaseMapper<OrderPayInfo> {
    OrderPayInfo selectByOrderNo(Long orderNo);
    OrderPayInfo selectByOrderNoAndOmsOrderNo(@Param("orderNo") Long orderNo,@Param("omsOrderNo")Long omsOrderNo);
    Integer countByOrderNo(Long orderNo);

    int updateByOrderNo(OrderPayInfo orderPayInfo);

    List<OrderPayInfo> selectByOrderNoList(@Param("orderNoList") List<Long> orderNoList);


    List<OrderPayInfo> selectByOmsOrderNoList(@Param("omsOrderNoList") List<Long> omsOrderNoList);

    OrderPayInfo getOrderPayInfoForB2C(@Param("orderNo") Long orderNo);

    OrderPayInfo selectByOmsOrderNo(@Param("omsOrderNo")Long omsOrderNo);
}
