package cn.hydee.middle.business.order.service.impl;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.dto.StringIntegerDto;
import cn.hydee.middle.business.order.dto.dutycasher.req.StoreDutyCasherQueryCommonDto;
import cn.hydee.middle.business.order.dto.dutycasher.req.StoreDutyCasherQueryReqDto;
import cn.hydee.middle.business.order.dto.dutycasher.req.StoreDutyCasherSaveOrUpdateReqDto;
import cn.hydee.middle.business.order.dto.dutycasher.rsp.QueryAuthInfoRspDto;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.SysEmployeeResDTO;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.entity.OrderStoreDefaultDutyCasher;
import cn.hydee.middle.business.order.entity.StoreBillConfig;
import cn.hydee.middle.business.order.mapper.OrderStoreDefaultDutyCasherMapper;
import cn.hydee.middle.business.order.mapper.StoreBillConfigMapper;
import cn.hydee.middle.business.order.service.OrderStoreDefaultDutyCasherService;
import cn.hydee.middle.business.order.v2.manager.base.BaseInfoManager;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 店铺拣货班次配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@Service
public class OrderStoreDefaultDutyCasherServiceImpl extends ServiceImpl<OrderStoreDefaultDutyCasherMapper, OrderStoreDefaultDutyCasher> implements OrderStoreDefaultDutyCasherService {
    
    @Autowired
    private OrderStoreDefaultDutyCasherMapper orderStoreDefaultDutyCasherMapper;
    @Autowired
    private StoreBillConfigMapper storeBillConfigMapper;
    @Autowired
    private BaseInfoManager baseInfoManager;

    @Override
    public OrderStoreDefaultDutyCasher queryInfo(StoreDutyCasherQueryReqDto reqDto) {
        List<OrderStoreDefaultDutyCasher> dataList = getOrderStoreDefaultDutyCashers(reqDto.getMerCode(),reqDto.getOrganizationCode());
        if (CollectionUtils.isEmpty(dataList)) {
            return OrderStoreDefaultDutyCasher.buildDefaultBean();
        }
        OrderStoreDefaultDutyCasher entity = dataList.get(0);
        return entity;
    }

    @Override
    public void addOrUpdateInfo(StoreDutyCasherSaveOrUpdateReqDto reqDto) {
        // 拣货实施员工编号
        SysEmployeeResDTO employeePickOperator = baseInfoManager.getEmployeeInfoForErpNoEx(reqDto.getPickOperatorId());
        OrderStoreDefaultDutyCasher entity = OrderStoreDefaultDutyCasher.buildDefaultBean(reqDto);
        entity.setPickOperatorCode(employeePickOperator.getEmpCode());
        entity.setPickOperatorName(employeePickOperator.getEmpName());
        saveUpdateEntity(entity);
    }

    @Override
    public QueryAuthInfoRspDto queryAuthInfo(StoreDutyCasherQueryCommonDto reqDto) {
        // 组织机构存在不按批号下账的线上门店数量
        return queryAuthInfo(reqDto.getMerCode(),reqDto.getOrganizationCode(),null);
    }

    @Override
    public QueryAuthInfoRspDto queryAuthInfo(String merCode, String organizationCode, String onlineStoreCode) {
        Integer orgStoreBatchNoStockCount = storeBillConfigMapper.selectOrgStoreBatchNoStockCount(merCode,organizationCode,onlineStoreCode);
        return QueryAuthInfoRspDto.buildBean(getOrderStoreDefaultDutyCashers(merCode,organizationCode),orgStoreBatchNoStockCount);
    }

    @Override
    public QueryAuthInfoRspDto queryAuthInfo(OrderInfo orderInfo){
        Integer orgStoreBatchNoStockCount = DsConstants.INTEGER_ZERO;
        StoreBillConfig storeBillConfig = storeBillConfigMapper.getBillConfigById(orderInfo.getClientConfId());
        // 配置了不按批号下账
        if(null != storeBillConfig && DsConstants.INTEGER_ZERO.equals(storeBillConfig.getIsBatchNoStock())){
            orgStoreBatchNoStockCount = DsConstants.INTEGER_ONE;
        }
        return QueryAuthInfoRspDto.buildBean(getOrderStoreDefaultDutyCashers(orderInfo.getMerCode(),orderInfo.getOrganizationCode()),orgStoreBatchNoStockCount);
    }

    @Override
    public Map<String,QueryAuthInfoRspDto> queryAuthInfo(String merCode, List<String> organizationCodeList) {
        // 配置数据
        List<StringIntegerDto> orgBatchNoStockCountList = storeBillConfigMapper.selectBatchNoStockCountByCodes(merCode,organizationCodeList);
        Map<String,Integer> orgBatchNoStockCountMap = orgBatchNoStockCountList.stream().collect(Collectors.toMap(StringIntegerDto::getProperty,StringIntegerDto::getValue,(s1,s2)->s1));
        // 开关数据
        QueryWrapper<OrderStoreDefaultDutyCasher> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OrderStoreDefaultDutyCasher::getMerCode, merCode)
                .in(OrderStoreDefaultDutyCasher::getOrganizationCode,organizationCodeList);
        List<OrderStoreDefaultDutyCasher> dataList = orderStoreDefaultDutyCasherMapper.selectList(queryWrapper);
        Map<String,List<OrderStoreDefaultDutyCasher>> orgOpenMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(dataList)){
            orgOpenMap = dataList.stream().collect(Collectors.groupingBy(OrderStoreDefaultDutyCasher::getOrganizationCode));
        }
        Map<String, List<OrderStoreDefaultDutyCasher>> finalOrgOpenMap = orgOpenMap;
        return organizationCodeList.stream().collect(Collectors.toMap(organizationCode -> organizationCode, organizationCode ->{
            Integer orgStoreBatchNoStockCount = Optional.ofNullable(orgBatchNoStockCountMap.get(organizationCode)).orElse(DsConstants.INTEGER_ZERO);
            List<OrderStoreDefaultDutyCasher> openList = Optional.ofNullable(finalOrgOpenMap.get(organizationCode)).orElse(Collections.emptyList());
            return QueryAuthInfoRspDto.buildBean(openList,orgStoreBatchNoStockCount);
        },(s1,s2)->s1));
    }

    private List<OrderStoreDefaultDutyCasher> getOrderStoreDefaultDutyCashers(String merCode,String organizationCode) {
        QueryWrapper<OrderStoreDefaultDutyCasher> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OrderStoreDefaultDutyCasher::getMerCode, merCode)
                .eq(OrderStoreDefaultDutyCasher::getOrganizationCode,organizationCode);
        List<OrderStoreDefaultDutyCasher> existDataList = orderStoreDefaultDutyCasherMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(existDataList)) {
            existDataList = Collections.emptyList();
        }
        return existDataList;
    }

    private void saveUpdateEntity(OrderStoreDefaultDutyCasher entity){
        List<OrderStoreDefaultDutyCasher> existDataList = getOrderStoreDefaultDutyCashers(entity.getMerCode(),entity.getOrganizationCode());
        Map<String, OrderStoreDefaultDutyCasher> uniqueKeyEntityMap = existDataList.stream().collect(Collectors.toMap(OrderStoreDefaultDutyCasher::convertUniqueKey, a -> a, (v1, v2) -> v1));
        if(uniqueKeyEntityMap.containsKey(entity.convertUniqueKey())){
            OrderStoreDefaultDutyCasher oldEntity = uniqueKeyEntityMap.get(entity.convertUniqueKey());
            entity.setId(oldEntity.getId());
            entity.setPickOperatorId(Optional.ofNullable(entity.getPickOperatorId()).orElse(oldEntity.getPickOperatorId()));
            entity.setPickOperatorCode(Optional.ofNullable(entity.getPickOperatorCode()).orElse(oldEntity.getPickOperatorCode()));
            entity.setPickOperatorName(Optional.ofNullable(entity.getPickOperatorName()).orElse(oldEntity.getPickOperatorName()));
            entity.setCashierSource(Optional.ofNullable(entity.getCashierSource()).orElse(oldEntity.getCashierSource()));
            entity.setCashierSourceName(Optional.ofNullable(entity.getCashierSourceName()).orElse(oldEntity.getCashierSourceName()));
            entity.setDutyId(Optional.ofNullable(entity.getDutyId()).orElse(oldEntity.getDutyId()));
            entity.setDutyName(Optional.ofNullable(entity.getDutyName()).orElse(oldEntity.getDutyName()));
            updateById(entity);
            return;
        }
        entity.setCreateTime(new Date());
        save(entity);
    }

}
