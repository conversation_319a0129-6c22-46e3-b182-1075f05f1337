package cn.hydee.middle.business.order.controller;

import cn.hydee.middle.business.order.thirdbill.dto.MtBillRefundBO;
import cn.hydee.middle.business.order.thirdbill.dto.MtOrderBO;
import cn.hydee.middle.business.order.thirdbill.service.IMeituanRefundService;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 录入美团退款金额
 * <AUTHOR>
 * @version 3.7.5
 * @date 2020/07/25
 */
@Api(tags = "录入美团退款金额")
@RestController
@RequestMapping("/${api.version}/mt/refund/input")
public class InputMeituanRefundController extends AbstractController {

    @Autowired
    private IMeituanRefundService refundService;

    @ApiOperation(value = "获取美团退款金额", notes = "获取美团退款金额")
    @GetMapping("")
    public ResponseBase<MtBillRefundBO> obtainInputValue(@ApiParam("订单号") @RequestParam("order_no") String orderNo,
                                                         @ApiParam("退款单号") @RequestParam("refund_no") String refundNo) {
        return refundService.obtainMtRefundValue(orderNo, refundNo);
    }

    @ApiOperation(value = "更新美团退款金额", notes = "更新美团退款金额")
    @PutMapping("")
    public ResponseBase<Integer> obtainInputValue(@RequestBody MtBillRefundBO billRefundBO) {
        int count = refundService.updateMtRefundValue(billRefundBO);
        return generateSuccess(count);
    }

    @ApiOperation(value = "批量获取美团退款金额", notes = "批量获取美团退款金额")
    @GetMapping("/order")
    public ResponseBase<MtOrderBO> obtainValueFromOrder(@ApiParam("订单号") @RequestParam("order_no") String orderNo) {
        return refundService.obtainMtRefundValueByOrder(orderNo);
    }

    @ApiOperation(value = "批量更新美团退款金额", notes = "批量更新美团退款金额")
    @PutMapping("/order")
    public ResponseBase<Integer> putValueFromOrder(@RequestBody MtOrderBO mtOrderBO) {
        int count = refundService.updateMtRefundValueByOrder(mtOrderBO);
        return generateSuccess(count);
    }
}
