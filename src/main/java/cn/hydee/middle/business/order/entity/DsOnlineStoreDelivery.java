package cn.hydee.middle.business.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * o2o线上门店与配送门店关联信息
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DsOnlineStoreDelivery implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商户编码
     */
    private String merCode;

    /**
     * 线上门店id
     */
    private Long onlineStoreId;

    /**
     * 配送门店id(主键)
     */
    private Long deliveryStoreId;

    /**
     * 配送方式（美团、蜂鸟、达达、员工自送、买家自提）
     */
    private String deliveryType;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 是否可以绑定门店（0-不可以 1-可以）
     */
    private Integer bindStore;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 末次修改时间
     */
    private Date modifyTime;



}
