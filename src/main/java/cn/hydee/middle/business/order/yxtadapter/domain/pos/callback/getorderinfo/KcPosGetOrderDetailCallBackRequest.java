package cn.hydee.middle.business.order.yxtadapter.domain.pos.callback.getorderinfo;

import lombok.Data;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR>
 * Date:  2023/9/6
 */
@Data
public class KcPosGetOrderDetailCallBackRequest implements java.io.Serializable{

    /**
     * 令牌字段
     */
    private String token;
    /**
     * 单据编号
     */
    private String ordercode;

    /**
     * 签名字段
     */
    private String sign;

}
