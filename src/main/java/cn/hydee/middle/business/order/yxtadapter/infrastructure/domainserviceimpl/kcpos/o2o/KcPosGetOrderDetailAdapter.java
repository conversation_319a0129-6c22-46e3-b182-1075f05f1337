package cn.hydee.middle.business.order.yxtadapter.infrastructure.domainserviceimpl.kcpos.o2o;

import static java.lang.Character.getType;

import cn.hydee.erp.model.order.OrderAccountReq;
import cn.hydee.middle.business.order.Enums.*;
import cn.hydee.middle.business.order.Enums.salebill.SaleBillSourceTypeEnum;
import cn.hydee.middle.business.order.autopick.enums.PickTypeEnum;
import cn.hydee.middle.business.order.domain.OrderDetailDomain;
import cn.hydee.middle.business.order.dto.AllEnterAccountDto;
import cn.hydee.middle.business.order.dto.MultiPayDataDto;
import cn.hydee.middle.business.order.dto.SaleBillSourceDto;
import cn.hydee.middle.business.order.dto.dutycasher.rsp.QueryAuthInfoRspDto;
import cn.hydee.middle.business.order.dto.req.AddOrderInfoReqDto;
import cn.hydee.middle.business.order.dto.req.OrderDutyCasherReq;
import cn.hydee.middle.business.order.dto.req.SaleOutBillParamDTO;
import cn.hydee.middle.business.order.dto.rsp.AccountEmpResDTO;
import cn.hydee.middle.business.order.dto.rsp.MultiPayType;
import cn.hydee.middle.business.order.dto.rsp.OrderInfoPageRsp;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.EmployeeResDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.SysEmployeeResDTO;
import cn.hydee.middle.business.order.entity.*;
import cn.hydee.middle.business.order.feign.MiddleBaseInfoClient;
import cn.hydee.middle.business.order.http.construct.ErpHttpReqStructUtil;
import cn.hydee.middle.business.order.mapper.OrderCouponInfoMapper;
import cn.hydee.middle.business.order.mapper.OrderInfoMapper;
import cn.hydee.middle.business.order.mapper.OrderPrescriptionMapper;
import cn.hydee.middle.business.order.mapper.RefundOrderMapper;
import cn.hydee.middle.business.order.module.orderdiff.handler.OrderDiffManager;
import cn.hydee.middle.business.order.service.*;
import cn.hydee.middle.business.order.service.async.HydeeEsSyncClientAsync;
import cn.hydee.middle.business.order.service.suport.RefundHandlerService;
import cn.hydee.middle.business.order.thirdbill.service.IMeituanRefundService;
import cn.hydee.middle.business.order.util.Const;
import cn.hydee.middle.business.order.util.DateUtil;
import cn.hydee.middle.business.order.util.ExceptionUtil;
import cn.hydee.middle.business.order.v2.manager.ErpBillInfoManager;
import cn.hydee.middle.business.order.v2.manager.base.*;
import cn.hydee.middle.business.order.yxtadapter.domain.pos.callback.getorderinfo.KcPosGetOrderDetailCallBackResponse;
import cn.hydee.middle.business.order.yxtadapter.domainservice.mdm.MiddleBaseInfoClientAdapter;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR>
 * Date:  2023/9/11
 */
@Slf4j
@Component
public class KcPosGetOrderDetailAdapter {

    @Autowired
    @Lazy
    private RefundHandlerService refundHandlerService;
    @Autowired
    private OrderDetailManager orderDetailManager;

    @Autowired
    private OrderInfoMapper orderInfoMapper;


    @Autowired
    private PartRefundManager partRefundManager;

    @Autowired
    private StoreBillConfigManager storeBillConfigManager;

    @Autowired
    private ErpBillInfoManager erpBillInfoManager;

    @Autowired
    private OrderCouponInfoMapper orderCouponInfoMapper;

    @Autowired
    private ErpPayModeService erpPayModeService;

    @Autowired
    @Lazy
    private OrderDiffManager orderDiffManager;
    @Autowired
    private RefundOrderMapper refundOrderMapper;
    @Autowired
    private OrderInfoManager orderInfoManager;

    @Autowired
    private OrderMultiPayInfoService orderMultiPayInfoService;

    @Autowired
    private OrderCancelBillRelationService orderCancelBillRelationService;

    @Autowired
    private OrderStoreDefaultDutyCasherService orderStoreDefaultDutyCasherService;


    @Autowired
    private OrderBasicManager orderBasicManager;

    @Autowired
    private MiddleBaseInfoClientAdapter middleBaseInfoClientAdapter;

    @Autowired
    private IMeituanRefundService meituanRefundService;
    @Autowired
    private OrderDutyCasherService orderDutyCasherService;

    @Autowired
    private OrderPrescriptionMapper orderPrescriptionMapper;

    @Value("${view.log.orderNo:}")
    private String viewLogOrderNo;

    @Transactional(rollbackFor = Exception.class)
    public MutablePair<KcPosGetOrderDetailCallBackResponse, OrderAccountReq> beforeToGetOrderDetailCallBack(OrderInfoPageRsp orderInfo, OrderAccountReq orderAccountReq, AllEnterAccountDto allEnterAccountDto) {


        if (PlatformCodeEnum.MEITUAN.getCode().equals(orderInfo.getThirdPlatformCode()) || PlatformCodeEnum.TY_O2O.getCode().equals(orderInfo.getThirdPlatformCode())) {
            //订单明细中有退款商品则不下账
            List<OrderDetail> orderDetailList = orderDetailManager.selectRefundDetailOrderByOrderNo(orderInfo.getOrderNo());
            if (orderDetailList != null && !orderDetailList.isEmpty()) {
                meituanRefundService.autoAssignMtRefundAmountFromRecordByOrder(orderInfo);
            }
        }

        if (ErpStateEnum.HAS_SALE.getCode().equals(orderInfo.getErpState())) {
            return MutablePair.of(KcPosGetOrderDetailCallBackResponse.fail(DsErrorType.HAS_SALE.getMsg()), orderAccountReq);
        }
        OrderPayInfo orderPayInfo = orderInfo.getOrderPayInfo();
        OrderDeliveryAddress orderDeliveryAddress = orderInfo.getOrderDeliveryAddress();
        //兼容B2C换货商品 过滤状态为 2 的明细
        List<OrderDetailDomain> orderDetailDomainList = Lists.newArrayList();
        if (DsConstants.B2C.equals(orderInfo.getServiceMode())){
            //B2C中Status为2是已换货
            orderDetailDomainList = orderDetailManager.selectDetailDomainListByOrderNo(orderInfo.getOrderNo())
                    .stream().filter(detail-> !OrderDetailStatusEnum.NOT_EXIST.getCode().equals(detail.getStatus())).collect(Collectors.toList());
        }else {
            orderDetailDomainList = orderDetailManager.selectDetailDomainListByOrderNo(orderInfo.getOrderNo());
        }

        //查询ERP支付方式
        List<Long> ids = new ArrayList<>();
        ids.add(orderInfo.getClientConfId());
        List<ErpPayMode> erpPayModeList = erpPayModeService.getErpPayCodeByClientConfId(ids);

        // 饿百 佣金 检验
        refundHandlerService.erpBillPartRefundCommissionCheckEBAI(orderInfo, orderPayInfo);
        // 订单明细中有退款商品则不下账
        List<OrderDetail> orderDetailList = orderDetailManager.selectRefundDetailOrderByOrderNo(orderInfo.getOrderNo());
        if (orderDetailList != null && !orderDetailList.isEmpty()) {
            String platformCode = orderInfo.getThirdPlatformCode();
            if (!PlatformCodeEnum.MEITUAN.getCode().equals(platformCode)
                    && !PlatformCodeEnum.TY_O2O.getCode().equals(platformCode)
                    && !PlatformCodeEnum.E_BAI.getCode().equals(platformCode)
                    && !PlatformCodeEnum.YD_JIA.getCode().equals(platformCode)) {
                // 下账状态更新为【取消下账】
                OrderInfo orderUpdate = new OrderInfo();
                orderUpdate.setOrderNo(orderInfo.getOrderNo());
                orderUpdate.setErpState(ErpStateEnum.CANCELED.getCode());
                orderInfoMapper.updateOrder(orderUpdate);
                HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, orderInfo.getMerCode(), orderInfo.getOrderNo(), orderInfo.getOrderState(),
                        ErpStateEnum.CANCELED.getCode(), OrderLogEnum.ENTER_ACCOUNT_UPDATE_STATE.getAction(), OrderLogEnum.getEnterAccountUpdateStateInfo(orderInfo.getErpState(), ErpStateEnum.CANCELED.getCode()), null);
                return MutablePair.of(KcPosGetOrderDetailCallBackResponse.fail("订单明细中有退款商品则不下账"), orderAccountReq);
            }
            // 下账拦截 饿百佣金未获取会拦截，会抛出异常
            refundHandlerService.checkEbaiCommissionFlag(orderInfo.getOrderNo(), orderInfo.getThirdPlatformCode(), null);

            RefundErrCodeEnum codeEnum = partRefundManager.partRefundAllSinkReCalculate(orderInfo, orderPayInfo, Collections.emptyList(), Boolean.FALSE);
            if (!RefundErrCodeEnum.OK.equals(codeEnum)) {
                throw ExceptionUtil.getWarnException(String.valueOf(codeEnum.getCode()), codeEnum.getDesc());
            }
            // 下单金额重新计算，重新获取订单详情
            orderDetailDomainList = orderDetailManager.selectDetailDomainListByOrderNo(orderInfo.getOrderNo());
        }
        //查询网店下账配置
        StoreBillConfig storeBillConfig = storeBillConfigManager.getBillConfigById(orderInfo.getClientConfId());

        //查询下账金额信息
        ErpBillInfo erpBillInfo = erpBillInfoManager.getErpBillInfoByOrderNo(orderInfo.getOrderNo());
        //查询优惠信息
        List<OrderCouponInfo> couponList = null;
        if (PlatformCodeEnum.YD_JIA.getCode().equals(orderInfo.getThirdPlatformCode())) {
            QueryWrapper<OrderCouponInfo> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                    .eq(OrderCouponInfo::getOrderNo, orderInfo.getOrderNo());
            couponList = orderCouponInfoMapper.selectList(queryWrapper);
        }
        String userName = "系统自动";
        SysEmployeeResDTO sysEmployeeResDTO = null;
        OrderDeliveryRecord orderDeliveryRecord = orderInfo.getOrderDeliveryRecord();
        // 零售下账
        SaleOutBillParamDTO param = new SaleOutBillParamDTO(orderInfo, orderDetailDomainList, orderPayInfo, orderDeliveryAddress, storeBillConfig, erpBillInfo, erpPayModeList, couponList, Boolean.TRUE, sysEmployeeResDTO
                , SaleBillSourceDto.buildBean(SaleBillSourceTypeEnum.AUTO_BILL_ORDER_RIDER_CONSUMER), orderDeliveryRecord);
        param.setUserId(userName);


        //补偿逻辑如果,商品下账金额为0 则直接修改订单状态为已取消.不再进行下账.
        BigDecimal billCommodityAmount = erpBillInfo.getBillCommodityAmount();
        if (billCommodityAmount.compareTo(BigDecimal.ZERO) == 0 && (orderInfo.getOrderState().intValue() == OrderStateEnum.CANCEL.getCode() || orderInfo.getOrderState().intValue() == OrderStateEnum.CLOSED.getCode())) {
            QueryWrapper<RefundOrder> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                    .eq(RefundOrder::getThirdOrderNo, orderInfo.getThirdOrderNo())
                    .eq(RefundOrder::getReCalculateOriginOrderFlag, DsConstants.INTEGER_ONE)
                    .nested(ew -> ew.eq(RefundOrder::getType, "1")
                            .or(wrapper -> wrapper.eq(RefundOrder::getLastApplyFlag, DsConstants.INTEGER_ONE)))
            ;
            List<RefundOrder> refundOrderList = refundOrderMapper.selectList(queryWrapper);
            if (!CollectionUtils.isEmpty(refundOrderList)) {
                AtomicBoolean b = new AtomicBoolean(false);
                refundOrderList.forEach(o -> {
                    if (Objects.equals(o.getType(), "1") || o.getLastApplyFlag() == 1) {
                        b.set(true);
                    }
                });
                if (b.get()) {
                    OrderInfo updateOrder = new OrderInfo();
                    updateOrder.setOrderNo(orderInfo.getOrderNo());
                    updateOrder.setErpState(ErpStateEnum.CANCELED.getCode());
                    orderInfoManager.updateOrder(updateOrder);
                    orderBasicManager.saveOrderInfoLog(orderInfo, orderInfo.getOrderState(), "-1", "Order 已取消,不进行下账", "", ErpStateEnum.CANCELED.getCode(), "system");

                }
            }
            return MutablePair.of(KcPosGetOrderDetailCallBackResponse.fail("商品下账金额为0"), orderAccountReq);
        }

        orderAccountReq = saleOutBillBefore(param, orderAccountReq, allEnterAccountDto);

        return MutablePair.of(KcPosGetOrderDetailCallBackResponse.success(null), orderAccountReq);
    }

    private OrderAccountReq saleOutBillBefore(SaleOutBillParamDTO param, OrderAccountReq accountReq, AllEnterAccountDto allEnterAccountDto) {
        // 【【订单金额下账复核】：下账时核对平台订单金额后再下账】 https://www.tapd.cn/********/prong/stories/view/11********001032886
        orderDiffManager.amountCompareAndReBuildParam(param);

        //包装参数信息
        OrderInfo orderInfo = param.getOrderInfo();
        List<OrderDetailDomain> orderDetailDomainList = param.getOrderDetailDomainList();
        OrderPayInfo orderPayInfo = param.getOrderPayInfo();
        OrderDeliveryAddress orderDeliveryAddress = param.getOrderDeliveryAddress();
        ErpBillInfo erpBillInfo = param.getErpBillInfo();
        StoreBillConfig storeBillConfig = param.getStoreBillConfig();
        List<ErpPayMode> erpPayModeList = param.getErpPayModeList();
        Boolean retryFlag = param.getRetryFlag();
        String merCode = orderInfo.getMerCode();
        List<OrderCouponInfo> couponList = param.getCouponList();
        //********改动，校验明细金额，会抛出异常  门店配置了需要按批号下账且存在批号为空抛异常

        accountReq = constructBillParam(merCode, orderInfo, orderDetailDomainList, orderPayInfo,
                orderDeliveryAddress, erpBillInfo, erpPayModeList, storeBillConfig, allEnterAccountDto, couponList, param.getOrderDeliveryRecord());
        // oms-tool切1.2
        return accountReq;
    }

    private OrderAccountReq constructBillParam(String merCode, OrderInfo orderInfo,
                                               List<OrderDetailDomain> orderDetailDomainList, OrderPayInfo orderPayInfo,
                                               OrderDeliveryAddress orderDeliveryAddress, ErpBillInfo erpBillInfo, List<ErpPayMode> erpPayModeList
            , StoreBillConfig storeBillConfig, AllEnterAccountDto allEnterAccountDto, List<OrderCouponInfo> couponList
            , OrderDeliveryRecord orderDeliveryRecord) {
        // 如果订单配送地址数据为空，则初始化一个对象，订单配送地址数据非必填
        if (orderDeliveryAddress == null) {
            orderDeliveryAddress = new OrderDeliveryAddress();
        }
        OrderAccountReq accountReq = new OrderAccountReq();
        //v3.9.2chufeng添加
        accountReq.setIsBatchNoStock(storeBillConfig.getIsBatchNoStock());
        //3.9.2结束
        // 系统订单号
        if (orderInfo.getCancelBillTimes() != null && orderInfo.getCancelBillTimes() > 0) {
            accountReq.setOrderNo(String.valueOf(orderInfo.getOrderNo() * DsConstants.ORDER_NO_MULTIPLIER + orderInfo.getCancelBillTimes()));
            // 【【取消下账】：取消下账功能调整】 https://www.tapd.cn/********/prong/stories/view/11********001028692
            // 取消下账订单号映射入库
            orderCancelBillRelationService.saveOrUpdateInfo(orderInfo.getOrderNo(), Long.valueOf(accountReq.getOrderNo()));
        } else {
            accountReq.setOrderNo(String.valueOf(orderInfo.getOrderNo()));
        }
        // 订单类型，0:o2o, 1 b2c
        accountReq.setOrderType(0);
        // 是否处方单 0 否 ，1是
        accountReq.setPrescriptionFlag(orderInfo.getPrescriptionFlag());
        // 订单类型, 0.实时订单、1.预约订单
        accountReq.setDeliveryTimeType(orderInfo.getDeliveryTimeType());
        // 配送方式：到店自提、骑手配送、快递配送等 TODO 错误的
        // 取配送单的配送平台名称，不存在时取配送方式
        if(ObjectUtils.isEmpty(orderDeliveryRecord)){
            //B2C订单配送方式
            orderDeliveryRecord = new OrderDeliveryRecord();
            orderDeliveryRecord.setDeliveryType(DeliveryTypeEnum.EXPRESS.getCode());
            orderDeliveryRecord.setDeliveryPlatName("快递配送");
        }
        String deliveryTypeName = !StringUtils.isEmpty(orderDeliveryRecord.getDeliveryPlatName()) ? orderDeliveryRecord.getDeliveryPlatName()
                : DeliveryTypeEnum.getByCode(orderDeliveryRecord.getDeliveryType());
        accountReq.setDeliveryTypeName(deliveryTypeName);
        // 订单状态
        accountReq.setOrderState(orderInfo.getOrderState());
        // 平台订单编号
        accountReq.setThirdOrderNo(orderInfo.getThirdOrderNo());
        // 三方平台编码
        accountReq.setThirdPlatCode(orderInfo.getThirdPlatformCode());
        // 三方平台名称
        String platName = null;
        if (!StringUtils.isEmpty(orderInfo.getThirdPlatformCode())) {
            PlatformCodeEnum byCode = PlatformCodeEnum.getByCode(orderInfo.getThirdPlatformCode());
            if(null!=byCode){
                platName = byCode.getType();
            }

        }
        accountReq.setThirdPlatName(platName);
        // 机构编码
        accountReq.setOrganizationCode(orderInfo.getOrganizationCode());
        // 买家姓名
        String buyerName = orderDeliveryAddress.getReceiverName();
        if (Strings.isEmpty(buyerName)) {
            buyerName = "自提单";
        }
        accountReq.setBuyerName(buyerName);
        // 收货人
        accountReq.setReceiverName(orderDeliveryAddress.getReceiverName());
        // 收货地址
        accountReq.setReveiverAddress(orderDeliveryAddress.getFullAddress());
        // 收货人电话
        accountReq.setReceiverPhone(orderDeliveryAddress.getReceiverTelephone());
        String payTypes = "线上支付";
        if (DsConstants.STRING_TWO.equals(orderPayInfo.getPayType())) {
            payTypes = "线下支付";
        }
        // 支付方式：线上支付 与 线下支付
        accountReq.setPayType(payTypes);
        // 买家实付金额
        accountReq.setBuyerActualAmount(orderPayInfo.getBuyerActualAmount());
        // 商品总额
        accountReq.setGoodsTotalAmount(orderPayInfo.getTotalAmount());
        // 商家配送费
        accountReq.setDeliveryFee(erpBillInfo.getMerchantDeliveryFee());
        // 平台配送费
        accountReq.setPlatformDeliveryFee(erpBillInfo.getPlatformDeliveryFee());
        // 商家包装费
        accountReq.setPackageFee(erpBillInfo.getMerchantPackFee());
        // 平台包装费
        accountReq.setPlatformPackFee(erpBillInfo.getPlatformPackFee());
        // merchantDiscount
        accountReq.setMerchantDiscount(erpBillInfo.getMerchantDiscount());
        // 平台优惠金额
        accountReq.setPlatformDiscount(erpBillInfo.getPlatformDiscount());
        // 平台收取佣金
        accountReq.setBrokerageAmount(erpBillInfo.getPlatBrokerageAmount());
        // 商品明细优惠金额
        accountReq.setDiscountAmount(erpBillInfo.getDetailDiscountAmount());
        // 拣货时传回的货位调整单号（零售出库时必填）
        accountReq.setAdjustNo(orderInfo.getErpAdjustNo());
        // 买家备注
        accountReq.setBuyerRemark(orderInfo.getBuyerRemark());
        // 下单时间
        accountReq.setCreated(DateUtil.parseDateToStr(orderInfo.getCreated(), DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS));
        // 当日流水号
        accountReq.setDayNum(orderInfo.getDayNum());
        // 接单时间
        accountReq.setAcceptTime(DateUtil.parseDateToStr(orderInfo.getAcceptTime(), DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS));
        // 会员卡号
        accountReq.setMemberNo(orderInfo.getMemberNo());
        List<OrderAccountReq.WareEntity> wareSaleOutList = new ArrayList<>();
        //优惠信息
        List<OrderAccountReq.CouponInfo> couponInfoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(couponList)) {
            for (OrderCouponInfo coupon : couponList) {
                OrderAccountReq.CouponInfo couponInfo = new OrderAccountReq.CouponInfo();
                couponInfo.setCouponId(coupon.getCouponId());
                couponInfo.setCouponContent(coupon.getCouponContent());
                couponInfo.setCouponName(coupon.getCouponName());
                couponInfo.setCouponScene(coupon.getCouponScene());
                couponInfo.setCouponCode(coupon.getCouponCode());
                couponInfo.setCouponPrice(coupon.getCouponPrice());
                couponInfoList.add(couponInfo);
            }
        }
        accountReq.setCouponList(couponInfoList);
        // 是否全自动下账
        QueryAuthInfoRspDto authInfo = orderStoreDefaultDutyCasherService.queryAuthInfo(orderInfo);
        OrderStoreDefaultDutyCasher orderStoreDefaultDutyCasher = authInfo.getData();
        boolean allAutoEnterAccountFlag = authInfo.needAllAutoEnterAccount();
        int indexFlag = 0;
        if (orderDetailDomainList != null) {
            // 全自动下账时
//            if(StringUtils.isNotBlank(viewLogOrderNo)&&String.valueOf(orderInfo.getOrderNo()).equals(viewLogOrderNo)){
//                log.info("wareListInDetail checking start orderDetailDomainList:{}", JSON.toJSONString(orderDetailDomainList));
//            }
            for (OrderDetailDomain orderDetail : orderDetailDomainList) {
                if (orderDetail.getOrderPickInfoList() != null&&!orderDetail.getOrderPickInfoList().isEmpty()) {
                    List<OrderAccountReq.WareEntity> wareListInDetail = new ArrayList<>(orderDetail.getOrderPickInfoList().size());
                    for (OrderPickInfo op : orderDetail.getOrderPickInfoList()) {
                        indexFlag = indexFlag + 1;
                        boolean needCheckBatchNoFlag = DsConstants.INTEGER_ONE.equals(storeBillConfig.getIsBatchNoStock()) && StringUtils.isEmpty(op.getCommodityBatchNo());
                        OrderAccountReq.WareEntity wareLingshou = buildWareEntity(orderInfo, orderDetail,
                                op.getCommodityBatchNo(), op.getCount(), needCheckBatchNoFlag);
                        wareListInDetail.add(wareLingshou);
                    }
                    ErpHttpReqStructUtil.balanceSalesAmountPrecision(orderDetail, wareListInDetail);
                    wareSaleOutList.addAll(wareListInDetail);
                }
            }
        }
//        if(StringUtils.isNotBlank(viewLogOrderNo)&&String.valueOf(orderInfo.getOrderNo()).equals(viewLogOrderNo)){
//            log.info("wareListInDetail checking end wareSaleOutList:{}", JSON.toJSONString(wareSaleOutList));
//        }
        if (wareSaleOutList.isEmpty()) {
            throw ExceptionUtil.getWarnException(DsErrorType.ERP_SALE_OUT_WARE_NULL);
        }
        // 订单商品详情信息
        accountReq.setWare(wareSaleOutList);
        // 订单总额
        accountReq.setTotalAmount(erpBillInfo.getOrderTotalAmount());
        // 商家实收金额
        accountReq.setMerchantActualReceive(erpBillInfo.getMerchantActualAmount());
        //2020-12-21 添加下账明细总和校验
        //商家实收 = billPrice *goodsCount + discountAmount + platformDiscount + platformPackFee
        // + platformDeliveryFee + brokerageAmount + merchantDiscount + packageFee + deliveryFee
        BigDecimal merchantReceive = BigDecimal.ZERO;
        for (OrderAccountReq.WareEntity wareEntity : wareSaleOutList) {
            merchantReceive = merchantReceive.add(wareEntity.getBillPrice().multiply(new BigDecimal(wareEntity.getGoodsCount())));
        }
//        merchantReceive = merchantReceive.add(accountReq.getDiscountAmount()).add(accountReq.getPlatformDiscount())
//                .add(accountReq.getPlatformPackFee()).add(accountReq.getPlatformDeliveryFee())
//                .add(accountReq.getBrokerageAmount()).add(accountReq.getMerchantDiscount())
//                .add(accountReq.getPackageFee()).add(accountReq.getDeliveryFee());
//        if (merchantReceive.compareTo(accountReq.getMerchantActualReceive()) != 0) {
//            throw ExceptionUtil.getWarnException(DsErrorType.ORDER_BILL_DETAIL_ERROR);
//        }
        // 拣货员
        if (!StringUtils.isEmpty(orderInfo.getPickOperatorId())) {
            accountReq.setUserId(orderInfo.getPickOperatorId());
        } else if (allAutoEnterAccountFlag) {
            accountReq.setUserId(orderStoreDefaultDutyCasher.getPickOperatorCode());
        } else {
            throw ExceptionUtil.getWarnException(DsErrorType.PICK_USER_NOT_EXIST_ERROR);
        }
        // 配送用户ID
        if (!StringUtils.isEmpty(orderInfo.getPickOperatorId())) {
            accountReq.setDeliveryUserId(orderInfo.getPickOperatorId());
        } else if (allAutoEnterAccountFlag) {
            accountReq.setDeliveryUserId(orderStoreDefaultDutyCasher.getPickOperatorCode());
        }
        // 支付编码（必填） 线上支付:支付方式编码统一为809080 货到付款：支付方式编码取订单支付的支付编码
        String payCode = DsConstants.PAY_ONLINE_CODE;
        if (DsConstants.STRING_ONE.equals(orderPayInfo.getPayType())) {
            if (!CollectionUtils.isEmpty(erpPayModeList)) {
                for (ErpPayMode erpPayMode : erpPayModeList) {
                    // 取线上支付编码
                    if (DsConstants.INTEGER_ONE.equals(erpPayMode.getPayType())) {
                        payCode = erpPayMode.getPayModeId();
                    }
                }
            }
        }
        if (DsConstants.STRING_TWO.equals(orderPayInfo.getPayType())) {
            if (StringUtils.isEmpty(orderPayInfo.getPayCode())) {
                throw ExceptionUtil.getWarnException(DsErrorType.ERP_PAY_CODE_IS_NULL);
            }
            payCode = orderPayInfo.getPayCode();
        }
        accountReq.setPayCode(payCode);
        // 读取班次和收银员
        OrderDutyCasherReq duty = orderDutyCasherService.getOrderOperateDuty(orderInfo.getOrderNo() + Const.DUTY_ORDER);
        if (duty != null) {
            // 机器自动拣货订单
            boolean robotAutoPickFlag = PickTypeEnum.ROBOT_AUTO_PICK.getCode().equals(orderInfo.getOrderPickType());
            // 开启了收银员，机器自动拣货未给，不下账
            if (robotAutoPickFlag && DsConstants.INTEGER_ONE.equals(storeBillConfig.getCheckerType()) && StringUtils.isEmpty(duty.getCashierSource())) {
                if (allAutoEnterAccountFlag) {
                    accountReq.setCashierSource(orderStoreDefaultDutyCasher.getCashierSource());
                } else {
                    throw ExceptionUtil.getWarnException(DsErrorType.ROBOT_AUTO_PICK_CASHIER_IS_NULL);
                }
            }
            // 开启了班次，机器自动拣货未给，不下账
            if (robotAutoPickFlag && DsConstants.INTEGER_ONE.equals(storeBillConfig.getFrequencyType()) && StringUtils.isEmpty(duty.getDutyId())) {
                if (allAutoEnterAccountFlag) {
                    accountReq.setDutyId(orderStoreDefaultDutyCasher.getDutyId());
                } else {
                    throw ExceptionUtil.getWarnException(DsErrorType.ROBOT_AUTO_PICK_CASHIER_IS_NULL);
                }
            }
            // 读取设置，设置不为空则配置
            accountReq.setDutyId(duty.getDutyId());
            accountReq.setCashierSource(duty.getCashierSource());
        } else if (allAutoEnterAccountFlag) {
            if (DsConstants.INTEGER_ONE.equals(storeBillConfig.getFrequencyType())) {
                accountReq.setDutyId(orderStoreDefaultDutyCasher.getDutyId());
            }
            if (DsConstants.INTEGER_ONE.equals(storeBillConfig.getCheckerType())) {
                accountReq.setCashierSource(orderStoreDefaultDutyCasher.getCashierSource());
            }
        } else {
            // 读取设置，设置为空，收银员取值（1.同拣货员、2.ERP中获取）
            if (!StringUtils.isEmpty(orderInfo.getPickOperatorId())) {
                EmployeeResDTO employee = employeeByCode(merCode, orderInfo.getPickOperatorId());
                accountReq.setCashierSource(null == employee ? orderInfo.getPickOperatorId() : employee.getEmpCode());
            } else {
                AccountEmpResDTO employee = employee(orderInfo.getPickerId());
                accountReq.setCashierSource(null == employee ? orderInfo.getPickerId() : employee.getEmpCode());
            }
        }
        //健康贝
        accountReq.setHealthNum(orderPayInfo.getHealthNum());
        accountReq.setHealthValue(orderPayInfo.getHealthValue());
        //添加处方信息
        if (Optional.of(orderInfo).isPresent()) {
            List<OrderPrescription> prescriptionList = orderPrescriptionMapper.selectListByOrderNo(orderInfo.getOrderNo());
            List<OrderAccountReq.OrderPrescription> list = new ArrayList<>();
            if (!CollectionUtils.isEmpty(prescriptionList)) {
                prescriptionList.forEach(prescription -> {
                    OrderAccountReq.OrderPrescription orderPrescription = new OrderAccountReq.OrderPrescription();
                    BeanUtils.copyProperties(prescription, orderPrescription);
                    orderPrescription.setLastPushTime(
                            DateUtil.parseDateToStr(prescription.getLastPushTime(), "yyyy-MM-dd HH:mm:ss")
                    );
                    orderPrescription.setCfPicUrl(prescription.getCfpicurl());
                    orderPrescription.setCfId(prescription.getId().toString());
                    list.add(orderPrescription);
                });
            }
            accountReq.setOrderPrescriptionList(list);
        }
        // 【*********】【新增】支持多支付方式下账  0-非医保订单,1-医保订单
        List<MultiPayType> payDataList = orderMultiPayInfoService.queryPayList2Account(orderInfo);
        MultiPayDataDto.multiPayHandle(orderInfo, accountReq, erpBillInfo, payDataList, orderPayInfo, storeBillConfig);
        // 医保订单标识
        accountReq.setMedicalInsurance(Optional.ofNullable(orderInfo.getMedicalInsurance()).orElse(DsConstants.INTEGER_ZERO));
        //0923 增加推荐人
        try {
            if (!StringUtils.isEmpty(orderInfo.getWscExtJson())) {
                AddOrderInfoReqDto.ExtJson extJson = new Gson().fromJson(orderInfo.getWscExtJson(), AddOrderInfoReqDto.ExtJson.class);
                accountReq.setRecommendCode(extJson.getRecommendCode());
                accountReq.setNeedSendHb(extJson.getNeedSendHb());
            }
        } catch (Exception e) {
            log.error("wsc extjson error,json:{}", orderInfo.getWscExtJson(), e);
        }
        //渠道标识
        List<String> sourceChannelList = Lists.newArrayList();
        if (SourceChannelType.JD_CHANNEL.getCode().equals(orderInfo.getSourceChannelType())) {
            sourceChannelList.add(SourceChannelType.JD_CHANNEL.getValue());
        }
        //渠道标识集合，逗号分割 JD_CHANNEL-京东渠道
        accountReq.setSourceChannel(Joiner.on(",").join(sourceChannelList));
        return accountReq;
    }


    private void buildAllAutoEnterAccountWareData(OrderInfo orderInfo, List<OrderDetailDomain> orderDetailDomainList, List<OrderAccountReq.WareEntity> wareSaleOutList
            , AllEnterAccountDto allEnterAccountDto) {
        AtomicReference<Integer> defaultBatchNoCount = new AtomicReference<>(DsConstants.INTEGER_ZERO);
        List<OrderPickInfo> orderPickInfoList = new ArrayList<>();
        orderDetailDomainList.forEach(orderDetail -> {
            List<OrderAccountReq.WareEntity> wareListInDetail = new ArrayList<>(
                    orderDetail.getOrderPickInfoList().size());
            if (CollectionUtils.isEmpty(orderDetail.getOrderPickInfoList())) {
                defaultBatchNoCount.getAndSet(defaultBatchNoCount.get() + 1);
                orderPickInfoList.add(buildOrderPickInfo(orderDetail, DsConstants.SYSTEM_DEFAULT_BATCH_NO, orderDetail.getPickCount()));
                OrderAccountReq.WareEntity wareLingshou = buildWareEntity(orderInfo, orderDetail, DsConstants.SYSTEM_DEFAULT_BATCH_NO, orderDetail.getPickCount(), Boolean.FALSE);
                wareListInDetail.add(wareLingshou);
            } else {
                orderDetail.getOrderPickInfoList().forEach(op -> {
                    orderPickInfoList.add(buildOrderPickInfo(orderDetail, op.getCommodityBatchNo(), op.getCount()));
                    OrderAccountReq.WareEntity wareLingshou = buildWareEntity(orderInfo, orderDetail, op.getCommodityBatchNo(), op.getCount(), Boolean.FALSE);
                    wareListInDetail.add(wareLingshou);
                });
            }
            ErpHttpReqStructUtil.balanceSalesAmountPrecision(orderDetail, wareListInDetail);
            wareSaleOutList.addAll(wareListInDetail);
        });
        if (DsConstants.INTEGER_ZERO.compareTo(defaultBatchNoCount.get()) < 0) {
            allEnterAccountDto.setBatchNoIsDefaultFlag(Boolean.TRUE);
            allEnterAccountDto.setPickInfoList(orderPickInfoList);
        }
    }

    private OrderPickInfo buildOrderPickInfo(OrderDetailDomain detailInfo, String batchNo, Integer goodsCount) {
        OrderPickInfo orderPickInfo = new OrderPickInfo();
        orderPickInfo.setErpCode(detailInfo.getErpCode());
        orderPickInfo.setOrderDetailId(detailInfo.getId());
        orderPickInfo.setCommodityBatchNo(batchNo);
        orderPickInfo.setCount(goodsCount);
        orderPickInfo.setPurchasePrice(detailInfo.getPrice());
        return orderPickInfo;
    }

    private OrderAccountReq.WareEntity buildWareEntity(OrderInfo orderInfo, OrderDetailDomain orderDetail, String batchNo, Integer goodsCount, boolean needCheckBatchNoFlag) {
        OrderAccountReq.WareEntity wareLingshou = new OrderAccountReq.WareEntity();
        // 商品ERP编码
        wareLingshou.setErpCode(orderDetail.getErpCode());
        // 商品名称
        wareLingshou.setGoodsName(orderDetail.getCommodityName());
        // 商品类型, 1.普通商品、2.赠品
        wareLingshou.setGoodsType(orderDetail.getGoodsType());
        // 生产批号
        if (needCheckBatchNoFlag) {
            log.info("constructBillParam 订单拣货详情批号为空,不进行下账，orderNo {} ", orderInfo.getOrderNo());
            throw ExceptionUtil.getWarnException(DsErrorType.HTTP_ERP_BATCHNO_EMPTY_ERROR.getCode(), "订单拣货详情批号为空");
        }
        wareLingshou.setBatchNo(batchNo);
        // 商品数量
        wareLingshou.setGoodsCount(goodsCount);
        // 商品单价
        wareLingshou.setPrice(orderDetail.getPrice());
        // 商品金额
        wareLingshou.setGoodsAmount(orderDetail.getPrice().multiply(new BigDecimal(goodsCount)));
        // 分摊金额
        wareLingshou.setShareAmount(orderDetail.getDiscountShare());
        // 下账金额
        wareLingshou.setBillAmount(orderDetail.getBillPrice().multiply(new BigDecimal(goodsCount)));
        // 下账单价
        wareLingshou.setBillPrice(orderDetail.getBillPrice());
        // 商品明细状态, 0 正常， 1缺货，2商品不存在， 10已换货，11已退货
        wareLingshou.setStatus(orderDetail.getStatus());
        // 拆零
        wareLingshou.setChailing(orderDetail.getChailing());
        return wareLingshou;
    }


    private EmployeeResDTO employeeByCode(String merCode, String code) {
        if (StringUtils.isEmpty(code)) {
            throw WarnException.builder().code(DsErrorType.USER_NOT_EXIST_ERROR.getCode())
                    .message(DsErrorType.USER_NOT_EXIST_ERROR.getMsg()).build();
        }
        ResponseBase<EmployeeResDTO> resp = middleBaseInfoClientAdapter.queryEmpInfoByCode(merCode, code);
        EmployeeResDTO emp = null;
        if (resp != null && DsErrorType.SUCCESS.getCode().equals(resp.getCode())) {
            emp = resp.getData();
        }
        return emp;
    }

    private AccountEmpResDTO employee(String id) {
        if (StringUtils.isEmpty(id)) {
            throw WarnException.builder().code(DsErrorType.USER_NOT_EXIST_ERROR.getCode())
                    .message(DsErrorType.USER_NOT_EXIST_ERROR.getMsg()).build();
        }
        ResponseBase<AccountEmpResDTO> resp = middleBaseInfoClientAdapter.queryEmployeeById(id);
        AccountEmpResDTO emp = null;
        if (resp != null && DsErrorType.SUCCESS.getCode().equals(resp.getCode())) {
            emp = resp.getData();
        }
        return emp;
    }
}
