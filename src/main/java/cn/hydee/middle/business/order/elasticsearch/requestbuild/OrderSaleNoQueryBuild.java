package cn.hydee.middle.business.order.elasticsearch.requestbuild;

import cn.hydee.middle.business.order.elasticsearch.config.EsUtils;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.util.CollectionUtils;

public class OrderSaleNoQueryBuild {

    public static SearchRequest assemblyRequest(String orderInfoIndex, String merCode, List<String> saleNos){
//        SearchRequest request = new SearchRequest(orderInfoIndex);
        SearchRequest request = EsUtils.searchIndex(orderInfoIndex);
        request.source(buildSearchSource(merCode,saleNos));
        return request;
    }

    private static SearchSourceBuilder buildSearchSource(String merCode,List<String> saleNos){
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(saleNos.size());
        searchSourceBuilder.from(0);
        searchSourceBuilder.query(buildQuery(merCode,saleNos));
        return searchSourceBuilder;
    }

    private static QueryBuilder buildQuery(String merCode,List<String> saleNos){
        BoolQueryBuilder builder = new BoolQueryBuilder();
        if(!StringUtils.isEmpty(merCode)){
            builder.filter(QueryBuilders.termQuery("merCode",merCode));
        }
        if(!CollectionUtils.isEmpty(saleNos)){
            String[] arr = new String[saleNos.size()];
            saleNos.toArray(arr);
            builder.filter(QueryBuilders.termsQuery("erpSaleNo",arr));
        }
        return builder;
    }

}
