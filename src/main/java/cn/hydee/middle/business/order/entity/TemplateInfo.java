package cn.hydee.middle.business.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 模板数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TemplateInfo implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @ApiModelProperty(value="商户编码")
    private String merCode;

    @ApiModelProperty(value="模板类型,1：评价回复，2：退款审核回复")
    private Integer templateType;

    @ApiModelProperty(value="模板子类型，预留字段")
    private Integer templateSecondType;

    @ApiModelProperty(value="模板名称")
    private String templateName;

    @ApiModelProperty(value="模板内容")
    private String templateContent;

    @ApiModelProperty(value="模板内容第三方格式")
    private String templateThirdContent;

    @ApiModelProperty(value="创建者")
    private String creator;

    @ApiModelProperty(value="创建时间")
    private Date createTime;

    @ApiModelProperty(value="修改者")
    private String reviser;

    @ApiModelProperty(value="修改时间")
    private Date modifyTime;

}
