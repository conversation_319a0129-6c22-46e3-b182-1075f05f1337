package cn.hydee.middle.business.order.mapper;

import cn.hydee.middle.business.order.account.check.base.dto.req.QuerySecondReqDto;
import cn.hydee.middle.business.order.account.check.base.dto.rsp.SummaryInfo;
import cn.hydee.middle.business.order.entity.AccountCheckResult;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 核对结果表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-06
 */
@Repository
public interface AccountCheckResultMapper extends BaseMapper<AccountCheckResult> {
    
    List<SummaryInfo> selectDataForSummary(@Param("param") QuerySecondReqDto reqDto);
    
    IPage<SummaryInfo> selectSummaryByPage(Page<SummaryInfo> page,@Param("param") QuerySecondReqDto reqDto,@Param("platformCodeList") List<String> platformCodeList);
    
    IPage<AccountCheckResult> selectByPage(Page<AccountCheckResult> page,@Param("param") QuerySecondReqDto reqDto);
        
    List<AccountCheckResult> selectAllData(@Param("param") QuerySecondReqDto reqDto);
}
