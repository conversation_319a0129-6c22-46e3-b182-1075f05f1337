package cn.hydee.middle.business.order.dto.order.over.sold;

import cn.hydee.starter.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/04/18
 */
@Data
public class OrderOverSoldReqDto extends PageBase {

    @ApiModelProperty(value = "商户编码",hidden = true)
    private String merCode;

    @ApiModelProperty(value = "平台下单时间（开始）")
    private Date createdStart;

    @ApiModelProperty(value = "平台下单时间（结束）")
    private Date createdEnd;

    @ApiModelProperty(value = "平台编码集合")
    private List<String> thirdPlatformCodeList;

    @ApiModelProperty(value = "门店编码")
    private String organizationCode;

    @ApiModelProperty(value = "门店编码" ,hidden = true)
    private List<String> organizationCodeList;

    @ApiModelProperty(value = "超卖原因类型集合")
    private List<Integer> reasonTypeList;

    @ApiModelProperty(value = "商品编码")
    private String erpCode;

}
