package cn.hydee.middle.business.order.yxtadapter.domain.pos.callback.getorderlist;

import lombok.Data;

import java.math.BigDecimal;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR>
 * Date:  2023/9/6
 */
@Data
public class KcPosOrderSimpleInfo implements java.io.Serializable {

    /**
     * 订单编号 系统单号
     */
    private String ORDERCODE;

    /**
     * 三方平台单号
     * */
    private String THIRDORDERCODE;

    /**
     * 门店编码
     */
    private String STORECODE;


    /**
     * 订单来源。1美团，2饿了么，3京东，4自营商城，5平安好医生
     */
    private String ORDERFROM;


    /**
     * 订单来源名称
     */
    private String ORDERFROMNAME;
    /**
     * 买家姓名
     */
    private String BUYERNAME;


    /**
     * 收货地址
     */
    private String RECEIVEADDRESS;
    /**
     * 付款金额
     */
    private BigDecimal PAYAMOUNT;
    /**
     * 订单类型。1O2O，2B2C,3服务类型，4.其他
     */
    private String ORDERTYPE;
    /**
     * 订单类型名称
     */
    private String ORDERTYPENAME;
    /**
     * 订单状态
     */
    private String ORDERSTATUS;
    /**
     * 订单状态名称
     */
    private String ORDERSTATUSNAME;
    /**
     * 下单时间
     */
    private String CREATETIME;
    /**
     * 门店接单时间
     */
    private String TAKEORDERTIME;
    /**
     * 付款方式（已废弃）字段保留
     */
    private String PAYTYPE;

    /**
     * 收货人电话
     */
    private String RECEIVEPHONE;
}
