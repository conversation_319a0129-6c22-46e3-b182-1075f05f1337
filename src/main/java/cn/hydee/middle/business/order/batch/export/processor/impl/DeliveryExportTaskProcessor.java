package cn.hydee.middle.business.order.batch.export.processor.impl;

import cn.hydee.middle.business.order.Enums.DeliveryStateEnum;
import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.Enums.DsErrorType;
import cn.hydee.middle.business.order.Enums.PlatformCodeEnum;
import cn.hydee.middle.business.order.batch.export.constant.ExportTypeConstant;
import cn.hydee.middle.business.order.batch.export.dto.DeliveryExportDTO;
import cn.hydee.middle.business.order.dto.DeliveryExportReqDto;
import cn.hydee.middle.business.order.dto.req.VerifyReqDto;
import cn.hydee.middle.business.order.dto.rsp.SearchDeliveryRspDto;
import cn.hydee.middle.business.order.dto.rsp.VerifyRspDto;
import cn.hydee.middle.business.order.entity.ExportTask;
import cn.hydee.middle.business.order.mapper.OrderDeliveryIdRelationMapper;
import cn.hydee.middle.business.order.service.OrderDeliveryService;
import cn.hydee.middle.business.order.service.VerifyService;
import cn.hydee.middle.business.order.v2.manager.ParentStoreManager;
import cn.hydee.starter.exception.WarnException;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service(ExportTypeConstant.SERVICE_PREFIX + ExportTypeConstant.DELIVERY_EXPORT)
@Slf4j
public class DeliveryExportTaskProcessor extends AbstractPageExportTaskProcessor<DeliveryExportDTO> {

    @Autowired
    private OrderDeliveryService orderDeliveryService;
    @Autowired
    private VerifyService verifyService;
    @Autowired
    private OrderDeliveryIdRelationMapper orderDeliveryIdRelationMapper;

    @Override
    public Class getExportClass() {
        return DeliveryExportDTO.class;
    }

    @Override
    protected List getExportDateList(ExportTask task) {
        return null;
    }

    @Override
    protected void initData(ExportTask task) {
        DeliveryExportReqDto reqDTO = JSONObject.parseObject(task.getRequestJson(), DeliveryExportReqDto.class);
//        log.info("[DeliveryExportProcessor] merCode:{}, organizationCode:{}.", reqDTO.getMerCode(), reqDTO.getOrganizationCode());
        if(reqDTO.getDeliveryState() !=null && reqDTO.getDeliveryState().getDeliveryStateList().isEmpty()){
            reqDTO.setDeliveryState(null);
        }
        VerifyRspDto verifyDto = verifyService.verifyTimeAndOrganization(VerifyReqDto.builder()
                .merCode(reqDTO.getMerCode())
                .organizationCode(reqDTO.getOrganizationCode())
                .userId(reqDTO.getUserId())
                .storeFlag(0)
                .verifyFlag(1)
                .beginTime(reqDTO.getBeginTime())
                .endTime(reqDTO.getEndTime())
                .build());
        task.setVerifyDto(verifyDto);
        if(null == reqDTO.getBeginTime() || null == reqDTO.getEndTime()){
            reqDTO.setBeginTime(null);
            reqDTO.setEndTime(null);
        }else{
            reqDTO.setBeginTime(verifyDto.getBeginTime());
            reqDTO.setEndTime(verifyDto.getEndTime());
        }

        List<String> platformCodeList = verifyService.verifyPlatformCodeAndGet(reqDTO.getMerCode(), reqDTO.getUserId(), Collections.singletonList(reqDTO.getPlatformCode()));
        task.setPlatformCodeList(platformCodeList);

        // deliveryId 查询单独处理
        if (!StringUtils.isEmpty(reqDTO.getDeliveryId())) {
            IPage<SearchDeliveryRspDto> searchDeliveryRspDtoIPage = orderDeliveryService.searchDeliveryByDeliveryId(reqDTO, verifyDto.getOrganizatioinList(), platformCodeList);
            if (searchDeliveryRspDtoIPage == null || CollectionUtils.isEmpty(searchDeliveryRspDtoIPage.getRecords())) {
                throw WarnException.builder().code(DsErrorType.EXCEL_ROW_NULL.getCode()).
                        tipMessage(DsErrorType.EXCEL_ROW_NULL.getMsg()).build();
            }
            List<DeliveryExportDTO> list = new ArrayList<>();
            list.add(transfer(searchDeliveryRspDtoIPage.getRecords().get(0)));
            task.setList(list);
        }
    }

    @Override
    protected int getExportDataSize(ExportTask task) {
        DeliveryExportReqDto reqDTO = JSONObject.parseObject(task.getRequestJson(), DeliveryExportReqDto.class);
        if(reqDTO.getDeliveryState() !=null && reqDTO.getDeliveryState().getDeliveryStateList().isEmpty()){
            reqDTO.setDeliveryState(null);
        }
        List<DeliveryExportDTO> list = task.getList();
        // deliveryId 查询单独处理
        if (!StringUtils.isEmpty(reqDTO.getDeliveryId())) {
            return list.size();
        }

        VerifyRspDto verifyDto = task.getVerifyDto();
        List<String> platformCodeList = task.getPlatformCodeList();
        int totalCount = orderDeliveryService.countSearchDeliveryAllPage(reqDTO,verifyDto.getOrganizatioinList(),platformCodeList);

        log.info("[DeliveryExportTaskProcessor]totalCount:{}", totalCount);

        return totalCount;
    }

    @Override
    protected List getExportDataList(ExportTask task, int currentPage, int pageSize) {
        DeliveryExportReqDto reqDTO = JSONObject.parseObject(task.getRequestJson(), DeliveryExportReqDto.class);
        if(reqDTO.getDeliveryState() !=null && reqDTO.getDeliveryState().getDeliveryStateList().isEmpty()){
            reqDTO.setDeliveryState(null);
        }
        List<DeliveryExportDTO> list = task.getList();

        // deliveryId 查询单独处理
        if (!StringUtils.isEmpty(reqDTO.getDeliveryId())) {
            return list;
        }

        List<DeliveryExportDTO> exportList = new ArrayList<>(pageSize);

        reqDTO.setCurrentPage(currentPage);
        reqDTO.setPageSize(pageSize);

//        log.info("[DeliveryExportTaskProcessor]prepare query data. ");

        VerifyRspDto verifyDto = task.getVerifyDto();
        List<String> platformCodeList = task.getPlatformCodeList();
        List<SearchDeliveryRspDto> deliveryRecords = orderDeliveryService.searchDelivery(reqDTO, verifyDto.getOrganizatioinList(), platformCodeList);

//        log.info("[DeliveryExportTaskProcessor] deliveryRecords currentPage:{}. size:{}", currentPage, deliveryRecords.size());

        if (!CollectionUtils.isEmpty(deliveryRecords)) {
            for (SearchDeliveryRspDto deliveryRecord : deliveryRecords) {
                exportList.add(transfer(deliveryRecord));
            }
        }

        if (CollectionUtils.isEmpty(exportList)) {
            throw WarnException.builder().code(DsErrorType.EXCEL_ROW_NULL.getCode()).
                    tipMessage(DsErrorType.EXCEL_ROW_NULL.getMsg()).build();
        }

        // 【【上海国药】【订单查询】：订单明细报表增加门店“上级机构”字段】 https://www.tapd.cn/61969829/prong/stories/view/1161969829001033404
        ParentStoreManager.convertDeliveryExport(reqDTO.getMerCode(),exportList);
        return exportList;
    }

    @Override
    public String getFileNamePrefix() {
        return "配送单导出列表";
    }

    @Override
    public CellWriteHandler getCellWriteHandler(Integer dataSize) {
        return new SimpleColumnWidthStyleStrategy(20);
    }

    /**
     * 数据转化为excel内显示数据格式
     *
     * @param dto 数据
     * @return excel内显示数据格式
     */
    private DeliveryExportDTO transfer(SearchDeliveryRspDto dto) {
        DeliveryExportDTO exportDTO = new DeliveryExportDTO();
        BeanUtils.copyProperties(dto, exportDTO);

        PlatformCodeEnum platformCodeEnum = PlatformCodeEnum.getByCode(dto.getThirdPlatformCode());
        exportDTO.setThirdPlatformName(null == platformCodeEnum ? "未知平台" : platformCodeEnum.getType());

        Long deliveryId = orderDeliveryIdRelationMapper.queryRecordByOrderNo(Long.valueOf(dto.getOrderNo()));
        exportDTO.setDeliveryId(deliveryId == null ? "" : String.valueOf(deliveryId));

        if (dto.getOrderDeliveryRecord() != null) {
            exportDTO.setRiderOrderNo(dto.getOrderDeliveryRecord().getRiderOrderNo());
            exportDTO.setDeliveryPlatForm(dto.getOrderDeliveryRecord().getDeliveryPlatName());
            String deliveryState = DeliveryStateEnum.getByCode(dto.getOrderDeliveryRecord().getState());
            exportDTO.setDeliveryState(deliveryState);
            exportDTO.setRiderName(dto.getOrderDeliveryRecord().getRiderName());
            exportDTO.setRiderPhoneNumber(dto.getOrderDeliveryRecord().getRiderPhone());
            exportDTO.setCreateTime(dto.getOrderDeliveryRecord().getCreateTime());
            if (DsConstants.INTEGER_FOUR.equals(dto.getOrderDeliveryRecord().getState())) {
                exportDTO.setCompleteTime(dto.getOrderDeliveryRecord().getModifyTime());
            }
        }

        if (dto.getOrderDeliveryAddress() != null) {
            exportDTO.setReceiverName(dto.getOrderDeliveryAddress().getReceiverName());
            exportDTO.setReceiverTelephone(dto.getOrderDeliveryAddress().getReceiverTelephone());
        }

        if (dto.getLockFlag() == 10) {
            exportDTO.setOrderState("取消中");
        } else if (dto.getLockFlag() == 20) {
            exportDTO.setOrderState("待退款");
        } else {
            switch (dto.getOrderState()) {
                case 10:
                    if (dto.getLockFlag() >= 30) {
                        exportDTO.setOrderState("异常-待接单");
                    } else if (dto.getLockFlag() == 0) {
                        exportDTO.setOrderState("待接单");
                    }
                    break;
                case 20:
                    if (dto.getLockFlag() >= 30) {
                        exportDTO.setOrderState("异常-待拣货");
                    } else if (dto.getLockFlag() == 0) {
                        exportDTO.setOrderState("待拣货");
                    }
                    break;
                case 30:
                    if (dto.getLockFlag() >= 30) {
                        exportDTO.setOrderState("异常-待配送");
                    } else if (dto.getLockFlag() == 0) {
                        exportDTO.setOrderState("待配送");
                    }
                    break;
                case 40:
                    if (dto.getLockFlag() >= 30) {
                        exportDTO.setOrderState("异常-配送中");
                    } else if (dto.getLockFlag() == 0) {
                        exportDTO.setOrderState("配送中");
                    }
                    break;
                case 100:
                    if (dto.getLockFlag() >= 30) {
                        exportDTO.setOrderState("异常-已完成");
                    } else if (dto.getLockFlag() == 0) {
                        exportDTO.setOrderState("已完成");
                    }
                    break;
                case 101:
                    if (dto.getLockFlag() >= 30) {
                        exportDTO.setOrderState("异常-已关闭");
                    } else if (dto.getLockFlag() == 0) {
                        exportDTO.setOrderState("已关闭");
                    }
                    break;
                case 102:
                    if (dto.getLockFlag() >= 30) {
                        exportDTO.setOrderState("异常-已取消");
                    } else if (dto.getLockFlag() == 0) {
                        exportDTO.setOrderState("已取消");
                    }
                    break;
                default:
                    exportDTO.setOrderState("未知状态");
                    break;
            }
        }
        return exportDTO;
    }
}
