package cn.hydee.middle.business.order.http.construct.base;

import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * cn.hydee.middle.business.order.http.construct
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/8/5 11:27
 **/
@Slf4j
public abstract class AbstractConstructInfo<R,T> {

    /**
     * 构建单个参数map
     * @param name
     * @param value
     * @return
     */
    protected abstract void constructSingleParam(Map<R,T> map ,R name, T value);


    /**
     * 过滤签名信息
     * @param srcMap
     * @return
     */
    protected abstract Map<R,T> filterSign(Map<R, T> srcMap);


    /**
     * 构建请求路径
     * @param baseUrl
     * @param name
     * @return
     */
    protected abstract String  constructUrl(String baseUrl,String name);




}
