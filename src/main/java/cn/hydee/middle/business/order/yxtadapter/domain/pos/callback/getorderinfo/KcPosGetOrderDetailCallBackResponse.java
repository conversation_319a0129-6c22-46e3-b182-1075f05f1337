package cn.hydee.middle.business.order.yxtadapter.domain.pos.callback.getorderinfo;

import lombok.Data;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR>
 * Date:  2023/9/6
 */
@Data
public class KcPosGetOrderDetailCallBackResponse implements java.io.Serializable {

    /**
     * 返回码
     */
    private String Code;


    /**
     * 异常信息
     */
    private String Msg;

    /**
     * 订单详细信息的JSON字符串
     */
    private KcPosOrderReturnValue ReturnValue;

    public static KcPosGetOrderDetailCallBackResponse fail(String failMsg) {
        KcPosGetOrderDetailCallBackResponse response = new KcPosGetOrderDetailCallBackResponse();
        response.setCode("1");
        response.setMsg(failMsg);
        response.setReturnValue(null);
        return response;
    }

    public static KcPosGetOrderDetailCallBackResponse success(KcPosOrderReturnValue returnValue){
        KcPosGetOrderDetailCallBackResponse response = new KcPosGetOrderDetailCallBackResponse();
        response.setCode("0");
        response.setMsg("");
        response.setReturnValue(returnValue);
        return response;
    }

    public boolean isSuccess() {
        return "0".equals(this.Code);
    }
}
