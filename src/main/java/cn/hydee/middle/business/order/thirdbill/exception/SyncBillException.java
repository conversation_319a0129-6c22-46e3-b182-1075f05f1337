package cn.hydee.middle.business.order.thirdbill.exception;

/**
 * 订单同步异常
 * <AUTHOR>
 * @version 3.7.5
 * @date 2020/07/23 11:44
 */
public class SyncBillException extends RuntimeException{
    private static final long serialVersionUID = 372179878609599942L;

    public SyncBillException() {
        super();
    }

    public SyncBillException(String message) {
        super(message);
    }

    public SyncBillException(String message, Throwable cause) {
        super(message, cause);
    }

    public SyncBillException(Throwable cause) {
        super(cause);
    }

    protected SyncBillException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
