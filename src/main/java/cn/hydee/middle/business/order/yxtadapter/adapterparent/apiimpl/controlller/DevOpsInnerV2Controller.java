package cn.hydee.middle.business.order.yxtadapter.adapterparent.apiimpl.controlller;

import cn.hydee.middle.business.order.Enums.DsErrorType;
import cn.hydee.middle.business.order.v2.manager.DevOpsInnerV2Manager;
import cn.hydee.middle.business.order.yxtadapter.domain.correctiontool.qry.DevOpsOrderInfoDto;
import cn.hydee.middle.business.order.yxtadapter.domain.correctiontool.qry.DevOpsOrderInfoReq;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by Intellij IDEA. 运维接口
 *
 * <AUTHOR> Date:  2023/9/25
 */
@RestController
@RequestMapping("/ds/inner/devOps/v2")
@Slf4j
@EqualsAndHashCode(callSuper = true)
@Api(tags = "运维接口v2")
public class DevOpsInnerV2Controller extends AbstractController {


  @Resource
  private DevOpsInnerV2Manager devOpsInnerV2Manager;

  @ApiOperation(value = "订单明细查询", notes = "订单明细查询")
  @PostMapping("/order/getOrderInfo")
  public ResponseBase<DevOpsOrderInfoDto> getOrderInfo(@RequestHeader("userId") String userId,
      @RequestHeader("merCode") String merCode, @RequestBody DevOpsOrderInfoReq req) {
    String orderNo = req.getOrderNo();
    if (StringUtils.isEmpty(userId) || StringUtils.isEmpty(merCode) || StringUtils.isEmpty(
        orderNo)) {
      return ResponseBase.error(DsErrorType.ERP_ORDER_ERROR.getCode(),
          DsErrorType.ERP_ORDER_ERROR.getMsg());
    }

    return devOpsInnerV2Manager.getOrderInfo(req);
  }


  @ApiOperation(value = "订单拣货信息修改", notes = "订单拣货信息修改")
  @PostMapping("/order/updateByPickTrace")
  public ResponseBase<Boolean> updateByPickTrace(@RequestHeader("userId") String userId,
      @RequestHeader("merCode") String merCode, @RequestBody DevOpsOrderInfoDto reqDto) {
    String orderNo = reqDto.getDevOpsOrderInfo().getOrderNo();
    if (StringUtils.isEmpty(userId) || StringUtils.isEmpty(merCode) || StringUtils.isEmpty(
        orderNo)) {
      return ResponseBase.error(DsErrorType.ERP_ORDER_ERROR.getCode(),
          DsErrorType.ERP_ORDER_ERROR.getMsg());
    }

    return devOpsInnerV2Manager.updateByPickTrace(reqDto);
  }


  @ApiOperation(value = "修改超出部分退款下账单", notes = "修改超出部分退款下账单")
  @PostMapping("/order/updateExceedRefundOrder")
  public ResponseBase<Boolean> updateExceedRefundOrder(@RequestHeader("userId") String userId,
      @RequestHeader("merCode") String merCode) {
    if (StringUtils.isEmpty(userId) || StringUtils.isEmpty(merCode)  ) {
      return ResponseBase.error(DsErrorType.ERP_ORDER_ERROR.getCode(),
          DsErrorType.ERP_ORDER_ERROR.getMsg());
    }

    return devOpsInnerV2Manager.updateExceedRefundOrder();
  }


}
