package cn.hydee.middle.business.order.service.rocket;

import cn.hydee.middle.business.order.Enums.RocketTagEnum;
import cn.hydee.middle.business.order.service.rocket.ordermessage.OrderCancelConsumerV2Service;
import cn.hydee.middle.business.order.service.rocket.ordermessage.OrderMessageConsumerV2Service;
import cn.hydee.middle.business.order.service.rocket.ordermessage.OrderRemindConsumerV2Service;
import cn.hydee.middle.business.order.service.rocket.ordermessage.RefundMessageConsumerV2Service;
import cn.hydee.middle.business.order.service.rocket.ordermessage.RiderMessageV2ConsumerService;
import cn.hydee.middle.business.order.storeautosync.consume.StoreAutoConfigConsumer;
import cn.hydee.middle.business.order.storeautosync.consume.StoreSyncConsumer;
import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.exception.MQClientException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class ConsumerStarter {

    @Value("${message-notify.data-topic}")
    private String dataTopic;

    @Value("${message-notify.client-topic}")
    private String clientTopic;

    @Value("${message-notify.inner-topic}")
    private String innerTopic;

    @Value("${message-notify.cost-price-topic}")
    private String costPriceTopic;

    @Value("${message-notify.pick-info-topic}")
    private String pickInfoTopic;

    @Value("${message-notify.cure-pick-info-topic}")
    private String curePickInfoTopic;

    @Value("${message-notify.receive-from-ws-topic}")
    private String receiveFromWsTopic;

    @Value("${message-notify.order-update-topic}")
    private String orderUpdateTopic;

    @Value("${message-notify.delay-task-topic}")
    private String delayTaskTopic;

    @Value("${message-notify.event-tracking-topic}")
    private String eventTrackingTopic;

    @Value("${message-notify.commodity-create-callback-topic}")
    private String commodityCreateCallbackTopic;

    @Value("${message-notify.commodity-exception-analyse-topic}")
    private String commodityExceptionAnalyseTopic;

    @Value("${message-notify.store-auto-config-topic}")
    private String storeAutoConfigTopic;

    @Value("${message-notify.store-info-change-topic}")
    private String storeInfoChangeTopic;

    @Value("${message-notify.prescription-approve-result-topic}")
    private String prescriptionApproveResultTopic;

    @Value("${message-notify.order-message-topic}")
    private String orderMessageTopic;

    @Value("${message-notify.order-cancel-message-topic}")
    private String orderCancelMessageTopic;

    @Value("${message-notify.order-refund-message-topic}")
    private String orderRefundMessageTopic;

    @Value("${message-notify.order-remind-message-topic}")
    private String orderRemindMessageTopic;

    @Value("${message-notify.rider-message-topic}")
    private String riderMessageTopic;

    @Value("${message-notify.store-async-message-topic}")
    private String storeAsyncMessageTopic;

    @Value("${message-notify.mt-delivery-fence-change-message-topic}")
    private String mtDeliveryFenceChangeTopic;

    @Autowired
    private NewOrderConsumerService newOrderConsumerService;
    @Autowired
    private SoundConsumerService soundConsumerService;
    @Autowired
    private ErpOrderConsumerService erpOrderConsumerService;
    @Autowired
    private PrescriptionConsumerService prescriptionConsumerService;
    @Autowired
    private ErpCommodityCostPriceConsumerService erpCommodityCostPriceConsumerService;
    @Autowired
    private ReceiveFromWsMessageService receiveFromWsMessageService;
    @Autowired
    private OrderUpdateConsumerService orderUpdateConsumerService;
    @Autowired
    private DelayTaskConsumerService delayTaskConsumerService;
    @Autowired
    private EventTrackingConsumerService eventTrackingConsumerService;
    @Autowired
    private ErpPickInfoConsumerService erpPickInfoConsumerService;
    @Autowired
    private ErpCurePickInfoConsumerService erpCurePickInfoConsumerService;
    @Autowired
    private CommodityCreateCallbackCosumerService commodityCreateCallbackCosumerService;
    @Autowired
    private CommodityExceptionAnalyseConsumerService commodityExceptionAnalyseConsumerService;
    @Autowired
    private StoreAutoConfigConsumer storeAutoConfigConsumer;
    @Autowired
    private StoreInfoChangeConsumer storeInfoChangeConsumer;
    @Autowired
    private PrescriptionApproveResultConsumerService prescriptionApproveResultConsumerService;
    @Autowired
    private OrderMessageConsumerV2Service orderMessageConsumerV2Service;
    @Autowired
    private OrderCancelConsumerV2Service orderCancelConsumerV2Service;
    @Autowired
    private OrderRemindConsumerV2Service orderRemindConsumerV2Service;
    @Autowired
    private RefundMessageConsumerV2Service refundMessageConsumerV2Service;
    @Autowired
    private RiderMessageV2ConsumerService riderMessageV2ConsumerService;
    @Autowired
    private StoreSyncConsumer storeSyncConsumer;
    @Autowired
    private MTDeliveryStoreFenceChangeConsumer mTDeliveryStoreFenceChangeConsumer;

    @PostConstruct
    public void startConsumer() {
        try {
            log.info("startConsumer topic=" + dataTopic);
            newOrderConsumerService.listener(dataTopic, RocketTagEnum.NEW_ORDER_MESSAGE, false);
            prescriptionConsumerService.listener(dataTopic, RocketTagEnum.PRESCRIPTION_RECEIVE_MESSAGE, false);

            log.info("startConsumer topic=" + clientTopic);
            soundConsumerService.listenerByMultiThread(clientTopic, RocketTagEnum.SOUND_MESSAGE, false);

            log.info("startConsumer topic=" + innerTopic);
            erpOrderConsumerService.listenerByMultiThread(innerTopic, RocketTagEnum.ERP_MEMBER_ORDER_MESSAGE, false);

            log.info("startConsumer topic=" + costPriceTopic);
            erpCommodityCostPriceConsumerService.listener(costPriceTopic, RocketTagEnum.ERP_COMMODITY_COST_PRICE_MESSAGE, false);

            receiveFromWsMessageService.listenerByMultiThread(receiveFromWsTopic,
                    RocketTagEnum.GROUP_BUSINESS_CLOUD_RECEIVE_FROM_WS_MESSAGE, false);

            orderUpdateConsumerService.listener(orderUpdateTopic,
                    RocketTagEnum.ORDER_UPDATE_MESSAGE, false);

            // 延时任务消息
            delayTaskConsumerService.listener(delayTaskTopic,
                    RocketTagEnum.GROUP_BUSINESS_CLOUD_DELAY_TASK_MESSAGE, false);
            //埋点事件消息
            eventTrackingConsumerService.listenerByMultiThread(eventTrackingTopic, RocketTagEnum.GROUP_BUSINESS_CLOUD_EVENT_TRACKING_MESSAGE, false);
            //异步创建商品回调处理
            commodityCreateCallbackCosumerService.listener(commodityCreateCallbackTopic, RocketTagEnum.COMMODITY_CREATE_CALLBACK, false);
            //异常商品分析
            commodityExceptionAnalyseConsumerService.listener(commodityExceptionAnalyseTopic, RocketTagEnum.COMMODITY_EXCEPTION_ANALYSE, false);

            log.info("startConsumer topic=" + pickInfoTopic);
            erpPickInfoConsumerService.listener(pickInfoTopic, RocketTagEnum.ERP_PICK_INFO_MESSAGE, false);
            log.info("startConsumer topic=" + curePickInfoTopic);
            erpCurePickInfoConsumerService.listener(curePickInfoTopic, RocketTagEnum.ERP_CURE_PICK_INFO_MESSAGE, false);
            storeAutoConfigConsumer.listenerByMultiThread(storeAutoConfigTopic, RocketTagEnum.GROUP_BUSINESS_STORE_AUTO_SYNC, false);
            storeInfoChangeConsumer.listener(storeInfoChangeTopic, RocketTagEnum.STORE_CHANGE_MESSAGE, false);

            prescriptionApproveResultConsumerService.listener(prescriptionApproveResultTopic, RocketTagEnum.PRESCRIPTION_APPROVE_RESULT_MESSAGE, false);

            orderMessageConsumerV2Service.listenerByConfig(orderMessageTopic, RocketTagEnum.GROUP_BUSINESS_CLOUD_O2O_ORDER_MESSAGE, false);
            orderCancelConsumerV2Service.listenerByConfig(orderCancelMessageTopic, RocketTagEnum.GROUP_BUSINESS_CLOUD_O2O_ORDER_CANCEL_MESSAGE, false);
            refundMessageConsumerV2Service.listenerByConfig(orderRefundMessageTopic, RocketTagEnum.GROUP_BUSINESS_CLOUD_O2O_ORDER_REFUND_MESSAGE, false);
            orderRemindConsumerV2Service.listenerByConfig(orderRemindMessageTopic, RocketTagEnum.GROUP_BUSINESS_CLOUD_O2O_ORDER_REMIND_MESSAGE, false);
            riderMessageV2ConsumerService.listenerByConfig(riderMessageTopic, RocketTagEnum.GROUP_BUSINESS_CLOUD_O2O_RIDER_MESSAGE, false);
            storeSyncConsumer.listenerByConfig(storeAsyncMessageTopic,RocketTagEnum.GROUP_BUSINESS_CLOUD_O2O_STORE_ASYNC_MESSAGE,false);
            mTDeliveryStoreFenceChangeConsumer.listener(mtDeliveryFenceChangeTopic,RocketTagEnum.GROUP_BUSINESS_CLOUD_O2O_MT_DELIVERY_FENCE_CHANGE_MESSAGE,false);
        } catch (MQClientException e) {
            log.error("消息Consumer启动失败", e);
        }
    }

}
