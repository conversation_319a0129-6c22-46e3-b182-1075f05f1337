package cn.hydee.middle.business.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 复杂换货后保存部分退款消息用于人工换货
 *
 * <AUTHOR>
 * @date 2021/3/18 上午10:21
 */
@Data
public class RefundMessageDetail implements Serializable {
    private static final long serialVersionUID = -5900834176471926744L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 退款单号，雪花算法
     */
    private Long refundNo;
    /**
     * 商品erp编码
     */
    private String erpCode;
    /**
     * 商品名称
     */
    private String commodityName;
    /**
     * 已退货数量
     */
    private Integer refundCount;
    /**
     * 已退货数量
     */
    private BigDecimal refundMoney;
    /**
     * 优惠券金额 单位:元
     */
    private BigDecimal couponAmount;

    /**
     * 活动优惠金额 单位:元
     */
    private BigDecimal activityDiscountAmount;

    /**
     * 退款消息主体字段，冗余在明细
     * 退款状态 1 等待处理中 2 同意退款申请 3 拒绝退款申请 5 已取消(包括用户主动取消申请)  0 其他状态
     */
    private String status;
    /**
     * 退款消息主体字段，冗余在明细
     * 退款金额(元)
     */
    private String money;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date modifyTime;
}
