package cn.hydee.middle.business.order.storeautosync.constants;

public enum StoreBillConfigMode {

    PLATFORM_DELIVERY("platform","平台配送下账方式"),

    MERCHANT_DELIVERY("merchant","商家配送下账方式"),

    MICRO_SHOP_SPEC("micro","微商城特殊下账方式");

    private final String code;
    private final String msg;

    StoreBillConfigMode(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static StoreBillConfigMode getStoreBillConfigMode(String code){
        for (StoreBillConfigMode storeBillConfigMode : StoreBillConfigMode.values()) {
            if (storeBillConfigMode.getCode().equals(code)) {
                return storeBillConfigMode;
            }
        }
        return null;
    }
}
