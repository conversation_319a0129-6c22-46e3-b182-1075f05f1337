package cn.hydee.middle.business.order.batch.export.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/3/9 上午10:04
 */
@Data
public class OrderLedgerExportDTO implements Serializable {

    private static final long serialVersionUID = -2259102917414537010L;

    @ExcelProperty(value = "系统订单号")
    private String orderNo;
    @ExcelProperty(value = "平台订单号")
    private String thirdOrderNo;
    @ExcelProperty(value = "ERP零售流水号")
    private String erpSaleNo;
    @ExcelProperty(value = "账单类型")
    private String ledgerType;
    @ExcelProperty(value = "平台")
    private String thirdPlatformName;
    @ExcelProperty(value = "门店编码")
    private String onlineStoreCode;
    @ExcelProperty(value = "门店名称")
    private String onlineStoreName;
    @ExcelProperty(value = "上级机构")
    private String parentOrganizationName;
    @ExcelProperty(value = "所属机构ID")
    private String organizationCode;
    @ExcelProperty(value = "所属机构名称")
    private String organizationName;
    @ExcelProperty(value = "订单状态")
    private String orderState;
    @ExcelProperty(value = "下账状态")
    private String erpState;
    @ExcelProperty(value = "商家实收金额")
    private BigDecimal merchantActualAmount;
    @ExcelProperty(value = "下账总金额")
    private BigDecimal billTotalAmount;
    @ExcelProperty(value = "下账商品金额")
    private BigDecimal billCommodityAmount;
    @ExcelProperty(value = "平台优惠金额")
    private BigDecimal platformDiscount;
    @ExcelProperty(value = "商家优惠金额")
    private BigDecimal merchantDiscount;
    @ExcelProperty(value = "平台收取佣金")
    private BigDecimal platBrokerageAmount;
    @ExcelProperty(value = "商品明细优惠金额")
    private BigDecimal detailDiscountAmount;
    @ExcelProperty(value = "商家配送费")
    private BigDecimal merchantDeliveryFee;
    @ExcelProperty(value = "商家包装费")
    private BigDecimal merchantPackFee;
    @ExcelProperty(value = "平台配送费")
    private BigDecimal platformDeliveryFee;
    @ExcelProperty(value = "平台包装费")
    private BigDecimal platformPackFee;
    @ExcelProperty(value = "下单时间", format = "yyyy-MM-dd HH:mm:ss")
    private Date created;
    @ExcelProperty(value = "下账时间", format = "yyyy-MM-dd HH:mm:ss")
    private Date billTime;
}
