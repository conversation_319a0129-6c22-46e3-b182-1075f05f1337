package cn.hydee.middle.business.order.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 分类或分组排序功能
 * <AUTHOR>
 * @version 1.0
 * @date 2019/10/28 10:30
 */
@Data
public class TypeSortReqDTO {
    @ApiModelProperty("分类或分组ID")
    @NotNull
    private String id;
    @ApiModelProperty("分类或分组排序值")
    private Integer sort;
    @ApiModelProperty("分类或分组商户编码")
    @NotNull
    private String merCode;
}
