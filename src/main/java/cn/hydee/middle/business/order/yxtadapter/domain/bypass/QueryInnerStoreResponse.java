package cn.hydee.middle.business.order.yxtadapter.domain.bypass;

import lombok.Data;

/**
 * <AUTHOR>
 * 查询切店列表
 */
@Data
public class QueryInnerStoreResponse {

    /**
     * 主键id
     */
    private Long id;
    /**
     *线下门店编码
     */
    private String organizationCode;
    /**
     * 线下门店名称
     */
    private String organizationName;

    /**
     * 是否开启新流程
     * 1:开启
     * 2:不开启
     */
    private String isOpenNew;

    /**
     *     POS_HD_H1(1, "海典H1"),
     *     POS_HD_H2(2, "海典H2"),
     *     POS_KC(3,"科传");
     * @see  cn.hydee.middle.business.order.Enums.PosModeEnum
     */
    private Integer posMode;

    /**
     *  pos请求路径，暂时只有H2配置， H2区分了区域门店
     * */
    private String posUrl;
}
