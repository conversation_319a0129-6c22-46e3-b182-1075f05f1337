package cn.hydee.middle.business.order.service;

import cn.hydee.middle.business.order.dto.req.AddOrderInfoReqDto;
import cn.hydee.middle.business.order.dto.rsp.SubscribePushParamResp;
import cn.hydee.middle.business.order.entity.OrderDeliveryRecord;
import cn.hydee.middle.business.order.entity.OrderInfo;

/**
 * cn.hydee.middle.business.order.service
 *
 * <AUTHOR> @version 1.0
 * @date 2020/8/4 11:24
 **/
public interface OrderDeliveryRecordService {

    /**
     * 获取配送记录
     * @param orderNo
     * @return
     */
    OrderDeliveryRecord selectByOrderNo(Long orderNo);
    /**
     * 修改订单异常
     */
    int updateStateException(OrderDeliveryRecord orderDeliveryRecord);

    /**
     * 修改配送信息
     */
    int updateDeliveryRecord(OrderDeliveryRecord orderDeliveryRecord);

    AddOrderInfoReqDto.OmniPackage setSelfVerifyCode(OrderDeliveryRecord orderDeliveryRecord, OrderInfo domain);

    void updateTCGDeliveryState(Long orderNo, String logisticStatus);

    Boolean getSubscribeInfo(SubscribePushParamResp params);

    void updateUploadFlag( Long orderNo);
}
