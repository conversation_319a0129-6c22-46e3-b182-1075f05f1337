package cn.hydee.middle.business.order.util;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.service.impl.base.BaseServiceInterface;
import cn.hydee.middle.business.order.util.sqlbuild.CustomSqlFactory;
import cn.hydee.middle.business.order.util.sqlbuild.sqlcheck.ICustomSqlCheck;
import cn.hydee.middle.business.order.util.sqlbuild.sqlformat.ICustomSqlFormat;
import cn.hydee.oms.utils.StringUtils;

/**
 * cn.hydee.middle.business.order.util
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/8/25 10:02
 **/
public class SqlExecuteUtil {

    private static final ICustomSqlFormat customSqlFormat = CustomSqlFactory.createCustomSqlFormat();

    private static final ICustomSqlCheck  customSqlCheck = CustomSqlFactory.createCustomSqlCheck();

    /**
     * 执行sql语句
     * @param sql
     * @param baseServiceInterface
     * @return
     */
    public static Integer executeUpdateSql(String sql, BaseServiceInterface baseServiceInterface){
        Integer num = DsConstants.INTEGER_ZERO;
        //为空则返回更新条数0条
        if(StringUtils.isEmpty(sql)){
            return num;
        }
        //验证sql语句 验证不通过则返回条数0条
        if(!customSqlCheck.sqlCheck(sql)){
            return num;
        }
        //格式化sql语句
        //更新
        baseServiceInterface.updateBatchSql(customSqlFormat.formatSql(sql));
        return num;
    }
}
