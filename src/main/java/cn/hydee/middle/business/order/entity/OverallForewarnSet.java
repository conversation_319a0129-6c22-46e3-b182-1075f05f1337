package cn.hydee.middle.business.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@TableName("overall_forewarn_set")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "overall_forewarn_set对象", description = "全局预警设置")
public class OverallForewarnSet implements Serializable {

  private static final long serialVersionUID = 1L;

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  @ApiModelProperty(value = "毛利率")
  private BigDecimal profit;

  @ApiModelProperty(value = "毛利额")
  private BigDecimal profitQuota;

  @ApiModelProperty(value = "服务类型：O2O、B2C")
  private String serviceType;

  @ApiModelProperty(value = "创建人")
  private String createBy;

  @ApiModelProperty(value = "创建时间")
  private Date createTime;

  @ApiModelProperty(value = "更新人")
  private String updateBy;

  @ApiModelProperty(value = "更新时间")
  private Date updateTime;
}
