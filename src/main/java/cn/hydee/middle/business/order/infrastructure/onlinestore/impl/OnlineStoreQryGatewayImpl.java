package cn.hydee.middle.business.order.infrastructure.onlinestore.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.GetOnlineStoreCommand;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.OnlineStoreAllDetailResDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.PageQueryOnlineStoreCommand;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.PageQueryOnlineStoreSyncConfigResDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.PageQueryOnlineStoreSimpleResDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.PageQuerySelfSupportOrderResDTO;
import cn.hydee.middle.business.order.dto.tag.resp.StoreTagListResp;
import cn.hydee.middle.business.order.entity.DsOnlineStore;
import cn.hydee.middle.business.order.entity.DsOnlineStoreConfig;
import cn.hydee.middle.business.order.entity.DsStoreOrderConfig;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.entity.tag.OnlineStoreTag;
import cn.hydee.middle.business.order.exception.OmsException;
import cn.hydee.middle.business.order.infrastructure.onlinestore.OnlineStoreQryGateway;
import cn.hydee.middle.business.order.infrastructure.onlinestore.impl.convert.OnlineStoreAllDetailConvert;
import cn.hydee.middle.business.order.infrastructure.onlinestore.impl.convert.PageQueryOnlineStoreSyncConfigConvert;
import cn.hydee.middle.business.order.infrastructure.onlinestore.impl.convert.PageQuerySimpleOnlineStoreConvert;
import cn.hydee.middle.business.order.infrastructure.selfsupportorder.impl.convert.PageQuerySelfSupportOrderConvert;
import cn.hydee.middle.business.order.mapper.DsOnlineStoreConfigRepo;
import cn.hydee.middle.business.order.mapper.DsOnlineStoreRepo;
import cn.hydee.middle.business.order.mapper.DsStoreOrderConfigRepo;
import cn.hydee.middle.business.order.mapper.tag.OnlineStoreTagMapper;
import cn.hydee.middle.business.order.util.ExceptionUtil;
import com.alibaba.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR> Date:  2024/6/14
 */
@Component
@Slf4j
public class OnlineStoreQryGatewayImpl implements OnlineStoreQryGateway {


  @Autowired
  private DsOnlineStoreRepo onlineStoreMapper;


  @Autowired
  private DsOnlineStoreConfigRepo onlineStoreConfigMapper;


  @Autowired
  private DsStoreOrderConfigRepo storeOrderConfigMapper;

  @Autowired
  private OnlineStoreTagMapper onlineStoreTagMapper;



  @Override
  public IPage<PageQueryOnlineStoreSimpleResDTO> pageQueryOnlineStore(
      PageQueryOnlineStoreCommand dto) {
    Page<DsOnlineStore> page = new Page<>(dto.getCurrentPage(),dto.getPageSize());

    IPage<DsOnlineStore> dsOnlineStoreIPage = onlineStoreMapper.pageQueryOnlineStore(page,dto);

    IPage<PageQueryOnlineStoreSimpleResDTO> pageInfo = new Page<>(dto.getCurrentPage(),
        dto.getPageSize());
    if (dsOnlineStoreIPage.getTotal() == 0) {
      return pageInfo;
    }
    List<DsOnlineStore> dsOnlineStoreList = dsOnlineStoreIPage.getRecords();
    List<PageQueryOnlineStoreSimpleResDTO> resList =PageQuerySimpleOnlineStoreConvert.convertToPageQueryList(
        dsOnlineStoreList);


    pageInfo.setTotal(dsOnlineStoreIPage.getTotal());
    pageInfo.setPages(dsOnlineStoreIPage.getPages());
    pageInfo.setRecords(resList);
    return pageInfo;
  }


  @Override
  public IPage<PageQueryOnlineStoreSyncConfigResDTO> pageQueryOnlineStoreSyncConfig(
      PageQueryOnlineStoreCommand dto) {

    Page<DsOnlineStore> page = new Page<>(dto.getCurrentPage(),dto.getPageSize());
    IPage<DsOnlineStore> storeListIPage = onlineStoreMapper.pageQueryOnlineStore(page,dto);

    IPage<PageQueryOnlineStoreSyncConfigResDTO> pageInfo = new Page<>(dto.getCurrentPage(),
        dto.getPageSize());

    if (storeListIPage.getTotal() == 0|| CollectionUtils.isEmpty(storeListIPage.getRecords())) {
      return pageInfo;
    }
    List<DsOnlineStore> storeList = storeListIPage.getRecords();
    List<Long> storeIds =  storeList.stream().map(DsOnlineStore::getId).distinct().collect(
        Collectors.toList());

    List<DsOnlineStoreConfig> storeConfigList = onlineStoreConfigMapper.listByOnlineStoreId(
        storeIds);

    List<StoreTagListResp> storeTagListResps = onlineStoreTagMapper.selectTagByStoreIds(storeIds);

    List<PageQueryOnlineStoreSyncConfigResDTO> resList = PageQueryOnlineStoreSyncConfigConvert.convertToPageQueryList(
        storeList, storeConfigList, storeTagListResps);

    pageInfo.setTotal(storeListIPage.getTotal());
    pageInfo.setPages(storeListIPage.getPages());
    pageInfo.setRecords(resList);
    return pageInfo;
  }

  @Override
  public OnlineStoreAllDetailResDTO getOnlineStoreAllDetailRes(GetOnlineStoreCommand dto) {

    DsOnlineStore onlineStore = onlineStoreMapper.getOnlineStore(dto.getMerCode(),
        dto.getPlatformCode(),
        dto.getOnlineStoreCode(), dto.getOnlineClientCode());
    if (null == onlineStore) {
      return null;
    }
    List<DsOnlineStoreConfig> storeConfigList = onlineStoreConfigMapper.listByOnlineStoreId(
        Lists.newArrayList(onlineStore.getId()));
    List<StoreTagListResp> storeTagListResps = onlineStoreTagMapper.selectTagByStoreIds(Lists.newArrayList(onlineStore.getId()));
    DsOnlineStoreConfig storeConfig = storeConfigList.stream().findFirst().orElse(null);
    OnlineStoreAllDetailResDTO result =  OnlineStoreAllDetailConvert.convertToOnlineStoreAllDetail(
        onlineStore, storeConfig, storeTagListResps);
    if(onlineStore.getServiceMode().equals(DsConstants.B2C)){
      QueryWrapper<DsStoreOrderConfig> qw = new QueryWrapper<>();
      qw.lambda().eq(DsStoreOrderConfig::getMerCode, result.getMerCode());
      qw.lambda().eq(DsStoreOrderConfig::getOutB2cClientId, result.getOnlineClientCode());
      List<DsStoreOrderConfig> storeOrderConfigList =  storeOrderConfigMapper.selectList(qw);
      if (!CollectionUtils.isEmpty(storeOrderConfigList)) {
        Long onlineStoreId = storeOrderConfigList.get(0).getOnlineStoreId();
        DsOnlineStore dsOnlineStore = onlineStoreMapper.selectById(onlineStoreId);
        result.setSourceStore(dsOnlineStore);
      }
    }

    return result;
  }

  @Override
  public OnlineStoreAllDetailResDTO getOnlineStoreAllDetailByO2OStore(GetOnlineStoreCommand dto) {
    DsOnlineStore onlineStore = onlineStoreMapper.getOnlineStore(dto.getMerCode(), dto.getPlatformCode(), dto.getOnlineStoreCode(), dto.getOnlineClientCode());
    if (null == onlineStore) {
//      log.warn("未找到店铺[{}-{}-{}]信息!", dto.getPlatformCode(), dto.getOnlineStoreCode(), dto.getOnlineClientCode());
      return null;
    }
    //查询O2O转B2C的配置
    QueryWrapper<DsStoreOrderConfig> qw = new QueryWrapper<>();
    qw.lambda().eq(DsStoreOrderConfig::getMerCode, onlineStore.getMerCode());
    qw.lambda().eq(DsStoreOrderConfig::getOnlineStoreId, onlineStore.getId());
    List<DsStoreOrderConfig> storeOrderConfigList = storeOrderConfigMapper.selectList(qw);
    if (CollUtil.isEmpty(storeOrderConfigList)) {
//      log.warn("该店铺[{}-{}-{}]缺失订单配置!", dto.getPlatformCode(), dto.getOnlineStoreCode(), dto.getOnlineClientCode());
      return null;
    }
    DsStoreOrderConfig dsStoreOrderConfig = storeOrderConfigList.get(0);
    if (!ObjectUtil.equal(dsStoreOrderConfig.getOpenB2c(), 1) || StrUtil.isBlank(dsStoreOrderConfig.getOutB2cClientId())) {
//      log.warn("该店铺[{}-{}-{}]未开启订单转B2C功能!", dto.getPlatformCode(), dto.getOnlineStoreCode(), dto.getOnlineClientCode());
      return null;
    }
    GetOnlineStoreCommand b2cStoreCommand = new GetOnlineStoreCommand();
    b2cStoreCommand.setMerCode(dto.getMerCode());
    b2cStoreCommand.setPlatformCode(dto.getPlatformCode());
    b2cStoreCommand.setOnlineStoreCode(dsStoreOrderConfig.getOutB2cClientId());
    b2cStoreCommand.setOnlineClientCode(dsStoreOrderConfig.getOutB2cClientId());
    b2cStoreCommand.setInvokeSource(dto.getInvokeSource());
    return getOnlineStoreAllDetailRes(b2cStoreCommand);
  }
}
