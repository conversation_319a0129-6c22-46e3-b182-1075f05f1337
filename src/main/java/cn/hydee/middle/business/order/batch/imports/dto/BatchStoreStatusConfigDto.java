package cn.hydee.middle.business.order.batch.imports.dto;

import cn.hydee.batch.annotation.EnableBatchImportTask;
import cn.hydee.batch.dto.BatchImportBaseDTO;
import cn.hydee.middle.business.order.batch.annotation.ValidateValue;
import cn.hydee.middle.business.order.batch.imports.constant.ImportConstant;
import cn.hydee.middle.business.order.batch.imports.process.BatchStoreStatusConfigHandleProcessor;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @className: BatchStoreStatusConfigDto
 * @author: liweixie
 * @packageName: cn.hydee.middle.business.order.batch.imports.dto
 * @description: 营业状态
 * @version: V1.1
 * date: 2023/6/9 上午10:36
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@EnableBatchImportTask(businessType = ImportConstant.BATCH_STORE_STATUS_CONFIG, taskName = "批量设置门店营业状态结果",
        processor = BatchStoreStatusConfigHandleProcessor.class, batchSize = 50, checkHead = false)
@HeadRowHeight(value = 50)
@ColumnWidth(30)
@ContentRowHeight(30)
public class BatchStoreStatusConfigDto extends BatchImportBaseDTO {
    @ExcelProperty(value = "*平台编码（必填）")
    @ValidateValue(message = "平台编码为空或者不合法")
    private String platformCode;

    @ExcelProperty(value = "平台名称（非必填）")
    private String platformName;

    @ExcelProperty(value = "*平台门店编码（必填）")
    @ValidateValue(message = "平台门店编码值为空或不合法")
    private String onlineStoreCode;

    @ExcelProperty(value = "平台门店名称（非必填）")
    private String onlineStoreName;


    @ExcelProperty(value = "*营业状态（必填）")
    @ValidateValue(message = "营业状态为空或不合法")
    private String status;

    @ExcelProperty(value = "处理结果")
    private String result = ImportConstant.FAIL;


}

