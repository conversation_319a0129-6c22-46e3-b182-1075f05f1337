package cn.hydee.middle.business.order.job;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.entity.RefundOrder;
import cn.hydee.middle.business.order.mapper.OrderInfoMapper;
import cn.hydee.middle.business.order.mapper.RefundOrderMapper;
import cn.hydee.middle.business.order.route.db.mysql.mapper.RouteAllotMapper;
import cn.hydee.middle.business.order.route.db.mysql.model.RouteAllot;
import cn.hydee.middle.business.order.route.domain.AllotDomainService;
import cn.hydee.middle.business.order.route.domain.enums.AllotStatusEnum;
import cn.hydee.middle.business.order.route.domain.enums.AllotTypeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@Component
@Slf4j
public class RouteAllotPushJobHandler{
    @Autowired
    private RouteAllotMapper routeAllotMapper;
    @Autowired
    private AllotDomainService allotDomainService;
    @Autowired
    private OrderInfoMapper orderInfoMapper;
    @Autowired
    private RefundOrderMapper refundOrderMapper;

    @XxlJob("RouteAllotPushJobHandler")
    public void execute() throws Exception {
        XxlJobHelper.log("订单中台 调拨单推送海典补偿任务 start......");
        try {
            LocalDateTime endDateTime = LocalDateTime.now();
            LocalDateTime startDateTime = endDateTime.plusDays(-1);
            String s = XxlJobHelper.getJobParam();
            JSONObject jsonObject = JSON.parseObject(s);
            if(jsonObject != null){
                Object endTime = jsonObject.get("endTime");
                Object startTime = jsonObject.get("startTime");
                if (!Objects.isNull(endTime)) {
                    endDateTime = LocalDateTimeUtil.parse(endTime.toString(), DatePattern.NORM_DATETIME_FORMATTER);
                }
                if (!Objects.isNull(startTime)) {
                    startDateTime = LocalDateTimeUtil.parse(startTime.toString(), DatePattern.NORM_DATETIME_FORMATTER);
                }
            }
            LambdaQueryWrapper<RouteAllot> queryWrapper = new QueryWrapper<RouteAllot>().lambda()
                    .eq(RouteAllot::getAllotStatus, AllotStatusEnum.WAIT.name())
                    .gt(RouteAllot::getCreateTime, startDateTime)
                    .lt(RouteAllot::getCreateTime, endDateTime);
            List<RouteAllot> routeAllots = routeAllotMapper.selectList(queryWrapper);
            if(routeAllots.isEmpty()) {
                return;
            }
            log.info("查询到总共：{}条销售调拨单数据", routeAllots.size());

            Map<String, List<RouteAllot>> routeAllotMap = routeAllots.stream().collect(Collectors.groupingBy(routeAllot ->
                    AllotTypeEnum.ORDER.name().equals(routeAllot.getOrderType()) ? AllotTypeEnum.ORDER.name() : AllotTypeEnum.REFUND.name()));
            String allotTypeOrder = jsonObject.get("allotTypeOrder").toString();
            String allotTypeRefund = jsonObject.get("allotTypeRefund").toString();
            if(allotTypeOrder.equals(DsConstants.STRING_ONE)){
                List<RouteAllot> routeAllotsOrders = routeAllotMap.get(AllotTypeEnum.ORDER.name());
                routeAllotsOrders.forEach(routeAllot -> {
                    log.info("调拨补偿机制推送正向调拨单：thirdOrderNo:{}, omsAllotNo:{}", routeAllot.getThirdOrderNo(), routeAllot.getOmsAllotNo());
                    allotDomainService.sendAllotToHd(routeAllot);
                });
            }
            if(allotTypeRefund.equals(DsConstants.STRING_ONE)){
                List<RouteAllot> routeAllotsRefunds = routeAllotMap.get(AllotTypeEnum.REFUND.name());
                if(!CollectionUtils.isEmpty(routeAllotsRefunds)){
                    List<String> thirdOrderNos = routeAllotsRefunds.stream().map(RouteAllot::getThirdOrderNo).collect(Collectors.toList());
                    List<String> thirdRefundNos = routeAllotsRefunds.stream().map(RouteAllot::getThirdRefundNo).collect(Collectors.toList());
                    List<OrderInfo> orderInfos = orderInfoMapper.selectList(new QueryWrapper<OrderInfo>().lambda().in(OrderInfo::getThirdOrderNo, thirdOrderNos));
                    Map<String, OrderInfo> orderInfoMap = orderInfos.stream().collect(Collectors.toMap(OrderInfo::getThirdOrderNo, item -> item, (a, b) -> a));
                    List<RefundOrder> refundOrders = refundOrderMapper.selectList(new QueryWrapper<RefundOrder>().lambda().in(RefundOrder::getThirdRefundNo, thirdRefundNos));
                    Map<String, RefundOrder> refundOrderMap = refundOrders.stream().collect(Collectors.toMap(RefundOrder::getThirdRefundNo, item -> item, (a, b) -> a));
                    routeAllotsRefunds.forEach(routeAllot -> {
                        log.info("调拨补偿机制推送逆向调拨单：thirdRefundNo:{}, omsAllotNo:{}", routeAllot.getThirdRefundNo(), routeAllot.getOmsAllotNo());
                        allotDomainService.sendHdCreateRefundAllot(orderInfoMap.get(routeAllot.getThirdOrderNo()), refundOrderMap.get(routeAllot.getThirdRefundNo()));
                    });
                }
            }
        } catch (Exception e) {
            XxlJobHelper.handleFail(e.getMessage());
        }
    }
}
