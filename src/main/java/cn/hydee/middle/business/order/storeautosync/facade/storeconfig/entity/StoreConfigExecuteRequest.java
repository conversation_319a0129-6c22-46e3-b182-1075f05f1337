package cn.hydee.middle.business.order.storeautosync.facade.storeconfig.entity;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class StoreConfigExecuteRequest {

    private List<String> handlerName;

    private String platformCode;

    private List<String> configStepIdList;

    private Integer status;

    private Integer startConfigStepId;
}
