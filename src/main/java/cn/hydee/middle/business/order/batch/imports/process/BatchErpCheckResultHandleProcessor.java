/*  
 * Project Name:hydee-business-order  
 * File Name:BatchExpressDeliveryProcessor.java  
 * Package Name:cn.hydee.middle.business.order.batch.imports.process  
 * Date:2020年10月17日下午2:39:45  
 * Copyright (c) 2020, Shanghai Hydee Software Corp., Ltd. All Rights Reserved.  
 *  
*/  
  
package cn.hydee.middle.business.order.batch.imports.process;

import cn.hydee.middle.business.order.account.check.base.dto.req.ErpCheckHandleReqDto;
import cn.hydee.middle.business.order.batch.imports.constant.ImportConstant;
import cn.hydee.middle.business.order.batch.imports.dto.BaseImportQueryDto;
import cn.hydee.middle.business.order.batch.imports.dto.BatchHandleErpCheckResultDto;
import cn.hydee.middle.business.order.batch.imports.service.AbstractImportFunc;
import cn.hydee.middle.business.order.service.ErpCheckResultServiceImpl;
import cn.hydee.starter.exception.WarnException;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service(ImportConstant.ERP_CHECK_HANDLE)
@Primary
@Scope("prototype")
@Slf4j
public class BatchErpCheckResultHandleProcessor extends AbstractImportFunc<BaseImportQueryDto,  BatchHandleErpCheckResultDto, BatchHandleErpCheckResultDto> {

	public final static String orderNoNull = "系统单号不能为空";
	public final static String thirdOrderNoNull = "平台单号不能为空";
	public final static String erpSaleNoNoNull = "零售流水不能为空";
	public final static String erpAmountNoNull = "零售金额不能为空";

	@Autowired
	private ErpCheckResultServiceImpl erpCheckResultServiceImpl;

	@Override
	protected void handleRecord(BatchHandleErpCheckResultDto record) {
		//循环处理
		if(null == record.getOrderNo()){
			record.setResult(ImportConstant.FAIL);
			record.setResult(orderNoNull);
			return ;
		}
		if(null == record.getThirdOrderNo()){
			record.setResult(ImportConstant.FAIL);
			record.setResult(thirdOrderNoNull);
			return ;
		}
		if(StringUtils.isEmpty(record.getErpAmount())){
			record.setResult(ImportConstant.FAIL);
			record.setErrorMsg(erpAmountNoNull);
			return ;
		}
		if(StringUtils.isEmpty(record.getErpSaleNo())){
			record.setResult(ImportConstant.FAIL);
			record.setErrorMsg(erpSaleNoNoNull);
			return ;
		}
		try{
			ErpCheckHandleReqDto param = new ErpCheckHandleReqDto();
			BeanUtils.copyProperties(record,param);
			param.setOrderNo(Long.parseLong(record.getOrderNo()));
			String result = erpCheckResultServiceImpl.handleErpCheckOrder(userId,merCode,param);
			if(!StringUtils.isEmpty(result)){
				record.setResult(ImportConstant.FAIL);
				record.setErrorMsg(result);
			}else{
				record.setResult(ImportConstant.SUCC);
			}
		}catch (WarnException e){
			record.setResult(e.getMessage());
			record.setErrorMsg(e.getMessage());
		}catch (Exception e2){
			log.error("handleErpCheckOrder error:,param:{},", JSONObject.toJSONString(record),e2);
			record.setResult(ImportConstant.FAIL);
			record.setErrorMsg(ImportConstant.FAIL);
		}
	}

}