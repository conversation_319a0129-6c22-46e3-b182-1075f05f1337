package cn.hydee.middle.business.order.service.impl;

import cn.hydee.middle.business.order.dto.OrderTabs;
import cn.hydee.middle.business.order.dto.req.OrderTabsSaveReqDto;
import cn.hydee.middle.business.order.dto.rsp.OrderTabsResDto;
import cn.hydee.middle.business.order.entity.OrderTabsConfig;
import cn.hydee.middle.business.order.mapper.OrderTabsConfigMapper;
import cn.hydee.middle.business.order.service.OrderTabsConfigService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class OrderTabsConfigServiceImpl extends ServiceImpl<OrderTabsConfigMapper, OrderTabsConfig> implements OrderTabsConfigService {

    @Autowired
    private OrderTabsConfigMapper orderTabsConfigMapper;

    @Override
    public boolean saveOrderTabsConfig(OrderTabsSaveReqDto dto) {
        //查询当前用户是否有保存自定义顺序的页签，没有则保存，有则更新
        OrderTabsConfig config = queryUserOrderTabsConfig(dto.getMerCode(),dto.getUserId());
        if(config != null){
            config.setUserTabs(JSON.toJSONString(dto.getTabs()));
            orderTabsConfigMapper.updateById(config);
        }else {
            config = new OrderTabsConfig();
            config.setMerCode(dto.getMerCode());
            config.setUserId(dto.getUserId());
            config.setUserTabs(JSON.toJSONString(dto.getTabs()));
            Date date = new Date();
            config.setCreateTime(date);
            config.setModifyTime(date);
            orderTabsConfigMapper.insert(config);
        }
        return true;
    }

    @Override
    public OrderTabsResDto queryOrderTabsConfig(String merCode, String userId) {
        OrderTabsConfig config = queryUserOrderTabsConfig(merCode,userId);
        //当前用户没有保存自定义顺序的页签，直接返回
        if(config == null){
            return null;
        }
        OrderTabsResDto resDto = new OrderTabsResDto();
        resDto.setMerCode(merCode);
        resDto.setTabs(JSONObject.parseArray(config.getUserTabs(), OrderTabs.class));

        return resDto;
    }

    /**
     * 查询当前用户的配置记录
     * @param userId
     * @param merCode
     * @return
     */
    private OrderTabsConfig queryUserOrderTabsConfig(String merCode,String userId){
        QueryWrapper<OrderTabsConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OrderTabsConfig::getMerCode,merCode)
                .eq(OrderTabsConfig::getUserId,userId)
                .orderByDesc(OrderTabsConfig::getModifyTime);
        queryWrapper.last(" limit 1 ");
        return orderTabsConfigMapper.selectOne(queryWrapper);
    }
}
