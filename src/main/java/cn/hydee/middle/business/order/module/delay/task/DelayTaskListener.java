package cn.hydee.middle.business.order.module.delay.task;

import cn.hydee.middle.business.order.service.rocket.MessageProducerService;
import cn.hydee.middle.business.order.util.DateUtil;
import cn.hydee.middle.business.order.util.RedisKeyUtil;
import cn.hydee.middle.business.order.util.redis.RedisLockUtil;
import cn.hydee.middle.business.order.util.redis.RedisZSetUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/07/14
 */
@Service
@Slf4j
public class DelayTaskListener {

    @Autowired
    private MessageProducerService producer;

    @Async("delayTaskProducerExecutor")
    public void listener(){
        // 尝试获取锁
        String key = RedisKeyUtil.REDIS_KEY_DELAY_TASK_LISTEN_KEY;
        boolean lockFlag = RedisLockUtil.getLockKey(key, String.valueOf(System.currentTimeMillis()),60);
        if(!lockFlag) {
            return;
        }
        try{
            delayTaskListen();
        }finally {
            RedisLockUtil.releaseLockKey(key);
        }
    }

    private void delayTaskListen() {
        long score = DateUtil.getNowTime();
        String key = RedisKeyUtil.getDelayTaskKey("one");
        // 查询
        Set<String> tasks =  RedisZSetUtil.rangeByScore(key,0,score);
        if(null == tasks || tasks.isEmpty()){
            return;
        }
        tasks.forEach(taskInfo ->{
            if(StringUtils.isEmpty(taskInfo)){
                return;
            }
            // 下发延迟消费mq
            try{
                producer.pushDelayTaskMessage(taskInfo);
            }catch (Exception e){
                log.info("delayTaskListener push to delay mq error,task info :{},cause:{}",taskInfo,e);
            }
        });
        // 删除
        RedisZSetUtil.removeRangeByScore(key,0,score);
    }

}
