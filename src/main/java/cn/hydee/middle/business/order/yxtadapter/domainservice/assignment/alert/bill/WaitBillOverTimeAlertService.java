package cn.hydee.middle.business.order.yxtadapter.domainservice.assignment.alert.bill;

import cn.hydee.middle.business.order.entity.OrderInfo;

import java.util.List;

public interface WaitBillOverTimeAlertService {

    /**
     * 获取overTime时间前的待下账订单
     * @param overTime
     */
    void pullOverTimeWaitBillRecord(Long overTime);

    void sendWaitBillAlert(List<OrderInfo> orderInfo, Long overTime);

}
