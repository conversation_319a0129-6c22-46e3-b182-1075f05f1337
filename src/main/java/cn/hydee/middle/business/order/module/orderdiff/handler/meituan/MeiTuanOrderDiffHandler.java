package cn.hydee.middle.business.order.module.orderdiff.handler.meituan;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.Enums.RefundErpStateEnum;
import cn.hydee.middle.business.order.Enums.RefundStateEnum;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.entity.OrderPayInfo;
import cn.hydee.middle.business.order.entity.RefundOrder;
import cn.hydee.middle.business.order.mapper.RefundOrderMapper;
import cn.hydee.middle.business.order.module.orderdiff.handler.AbstractOrderDiffHandler;
import cn.hydee.middle.business.order.v2.manager.base.PartRefundManager;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/12/09
 */
@Component("meiTuanOrderDiffHandler")
public class MeiTuanOrderDiffHandler extends AbstractOrderDiffHandler {

    private final PartRefundManager partRefundManager;
    private final RefundOrderMapper refundOrderMapper;

    public MeiTuanOrderDiffHandler(PartRefundManager partRefundManager, RefundOrderMapper refundOrderMapper) {
        this.partRefundManager = partRefundManager;
        this.refundOrderMapper = refundOrderMapper;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void reCalculateAmount(OrderInfo orderInfo, OrderPayInfo newOrderPayInfo) {
        // 美团 重新计算时，需要减去已完成的已重算的退款单金额
        QueryWrapper<RefundOrder> queryWrapper = new QueryWrapper<>();
        String[] queryFields = new String[]{"refund_no"};
        queryWrapper.select(queryFields).lambda()
                .eq(RefundOrder::getOrderNo, orderInfo.getOrderNo())
                .eq(RefundOrder::getState, RefundStateEnum.SUCCESS.getCode())
                .eq(RefundOrder::getErpState, RefundErpStateEnum.CANCELED.getCode())
                // 未下账的部分退款单，是否已重新计算原单财务数据等金额。0-未计算 1-已计算
                .eq(RefundOrder::getReCalculateOriginOrderFlag, DsConstants.INTEGER_ONE);
        List<RefundOrder> refundOrderList = refundOrderMapper.selectList(queryWrapper);
        List<Long> refundNoList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(refundOrderList)){
            refundNoList = refundOrderList.stream().map(RefundOrder::getRefundNo).collect(Collectors.toList());
        }
        partRefundManager.partRefundAllSinkReCalculate(orderInfo,newOrderPayInfo, refundNoList,true);
    }
}
