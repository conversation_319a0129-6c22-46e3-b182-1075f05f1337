package cn.hydee.middle.business.order.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/31
 */
@Data
public class StoreConfigQuery {
    @ApiModelProperty("商家编码")
    @NotBlank(message = "商家编码不可为空")
    private String merCode;
    @ApiModelProperty("平台编码")
    @NotBlank(message = "平台编码不可为空")
    private String platformCode;

    @ApiModelProperty("网店门店信息")
    @NotEmpty(message = "网店门店信息不可为空")
    private List<StoreBase> onlineStores;

    @Data
    public static class StoreBase {
        @ApiModelProperty("网店编码")
        private String clientCode;
        @ApiModelProperty("门店编码")
        private String onlineStoreCode;

        public String getClientStore() {
            return clientCode + onlineStoreCode;
        }
    }
}
