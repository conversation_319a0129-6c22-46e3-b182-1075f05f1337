/*  
 * Project Name:hydee-business-order  
 * File Name:CargoTransferStatusEnum.java  
 * Package Name:cn.hydee.middle.business.order.Enums.oborder  
 * Date:2020年9月29日下午4:11:26  
 * Copyright (c) 2020, Shanghai Hydee Software Corp., Ltd. All Rights Reserved.  
 *  
*/  
  
package cn.hydee.middle.business.order.Enums.oborder;  
/**  
 * ClassName:CargoTransferStatusEnum <br/>  
 * Function: 微商城预约单状态值：1-待提货 0-调货中. <br/> 
 * Date:     2020年9月29日 下午4:11:26 <br/>  
 * <AUTHOR>  
 */
public enum CargoTransferStatusEnum {
	
	/** 0-调货中*/
	GOODS_TRANSFER_ING(0, "调货中"),
	/** 1-待提货*/
	GOODS_WAIT_TAKE(1, "待提货"),
	;

    private Integer code;
    private String msg;

    CargoTransferStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
  
