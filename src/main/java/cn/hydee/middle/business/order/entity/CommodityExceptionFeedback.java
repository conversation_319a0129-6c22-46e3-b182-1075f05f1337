package cn.hydee.middle.business.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 异常商品原因反馈表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class CommodityExceptionFeedback implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 异常商品id
     */
    private Long commodityExceptionId;

    /**
     * 商户编码
     */
    private String merCode;

    /**
     * 原因
     */
    private String reason;

    /**
     * 反馈账号ID
     */
    private String userId;

    /**
     * 反馈账号名称
     */
    private String userName;

    /**
     * 反馈时间
     */
    private Date feedbackTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;


}
