package cn.hydee.middle.business.order.route.application.representation;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/27
 * @since 1.0
 */
@Data
public class ShopRefuseRepresentationDto {

    @ApiModelProperty(value = "接单门店能否拒绝 false-否 true-能")
    private Boolean refuseFlag;

    @ApiModelProperty(value = "自动拒绝接单时间范围")
    private Integer autoRefuseTimeRange;
}
