package cn.hydee.middle.business.order.dto.message;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/1/8 10:26
 */
@Data
public class NetRiderNotifyMessage extends NetNotifyBaseMessage{
    /** 平台类型
     * 0 骑手平台(取deliveryid,己方订单号)
     * 1 订单平台(取orderid,第三方订单号 */
    private String platformtype;

    /** 第三方平台订单号*/
    private String orderid;
    /** 配送ID */
    private String deliveryid;
    /** 骑手订单id */
    private String riderorderid;
    /**　线下对应状态 待接单＝1,待取货(已接单)＝2,配送中(已取货)＝3,
     * 已完成(已送达)＝4,已取消＝5, 已过期＝6,异常 = 7
     */
    private String offstatus;
    /** 订单状态 */
    private String orderstatus;
    /** 配送员姓名 **/
    private String drivername;
    /** 配送员电话 */
    private String driverphone;
    /** 配送地址 */
    private String driveraddress;
    /** 纬度 */
    private String latitude;
    /** 经度 */
    private String longitude;
    /** 取消来源 */
    private String cancelfrom;
    /** 取消原因 */
    private String cancelreason;
    /** 取消描述 */
    private String canceldetail;
    /** 错误信息 */
    private String errorinfo;

}
