/*  
 * Project Name:hydee-business-order  
 * File Name:ErpOnConnectionTimeoutListener.java  
 * Package Name:cn.hydee.middle.business.order.v2.feign.erp  
 * Date:2020年9月17日下午4:42:26  
 * Copyright (c) 2020, Shanghai Hydee Software Corp., Ltd. All Rights Reserved.  
 *  
*/  
  
package cn.hydee.middle.business.order.v2.feign.erp.exception;  
/**  
 * ClassName:ErpOnConnectionTimeoutListener <br/>  
 * Function: erp超时监听. <br/> 
 * Date:     2020年9月17日 下午4:42:26 <br/>  
 * <AUTHOR>  
 */
public interface ErpOnConnectionTimeoutListener {
	
	/**
	 * 
	 * onConnectionTimeout:超时监听. <br/>  
	 *  
	 * @param param  {@link OrderPickReceiveTimeOutExceptionParam}
	 * <AUTHOR>  
	 * @date 2020年9月18日 下午5:06:46
	 */
	void onConnectionTimeout(OrderPickReceiveTimeOutExceptionParam param);
}
  
