package cn.hydee.middle.business.order.dto.rsp.baseinfo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 店铺配置信息
 * <AUTHOR>
 */
@Data
public class DsStoreConfInfoResDTO {

    @ApiModelProperty(value = "是否全部配置所属机构")
    private Boolean allOrganizationConf;

    @ApiModelProperty(value = "未配置所属机构数量")
    private String notConfOrganizationNum;

    @ApiModelProperty(value = "是否有配送账号")
    private Boolean hasDeliveryStore;

}
