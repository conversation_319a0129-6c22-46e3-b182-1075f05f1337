package cn.hydee.middle.business.order.service.impl;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.dto.rsp.OssOrderFileRspDto;
import cn.hydee.middle.business.order.service.OssOrderFileHandlerService;
import cn.hydee.middle.business.order.service.export.ExportDataHandler;
import cn.hydee.middle.business.order.service.image.FileUpLoadService;
import cn.hydee.middle.business.order.service.image.FileUploadResult;
import cn.hydee.starter.util.DateUtil;
import cn.hydee.starter.util.UUIDUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.micrometer.core.lang.Nullable;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.util.List;
import java.util.UUID;

/**
 * cn.hydee.middle.business.order.service.impl
 *
 * <AUTHOR> Li
 * @version 1.0
 * @date 2020/5/9 16:32
 **/
@Service
@Slf4j
public class OssOrderFileHandlerServiceImpl implements OssOrderFileHandlerService {

    @Autowired
    private FileUpLoadService fileUploadService;

    @Value("${spring.application.name}")
    protected String appName;

    @Value("${domain.name:order}")
    protected String domainName;

    private static final String SLASH = "/";

    private static final String EXPORT = "export";

    @Override
    public <E,V,K> OssOrderFileRspDto ExportHandle(Integer exportSize,Class<?> cls, @Nullable String fileName,V service,K search, ExportDataHandler<E,V,K> exportDataHandler,String excelName) throws IOException {
        OssOrderFileRspDto ossOrderFileRspDto=new OssOrderFileRspDto().setFileKey("").setFileUrl("");
        //根据exportSize ,确定要写到多少个表中，分多少页从数据库获取数据 ，这里是利用mybatis-plus 插件默认每页最大为500

        Integer pageMaxSize = exportSize/DsConstants.SINGLE_TABLE_MAX_SIZE+DsConstants.INTEGER_ONE;
        Integer perPageMaxSize = exportSize/DsConstants.MYBATIS_PLUS_PAGE_SIZE+DsConstants.INTEGER_ONE;
        fileName = StringUtils.isEmpty(fileName)?"数据导出":fileName;
        File temp=null;
        FileOutputStream fileOutputStream=null;
        FileInputStream inputStream=null;
        //默认表序号
        Integer sheetcount= DsConstants.INTEGER_ZERO;
        ExcelWriter excelWriter = null;
        WriteSheet writeSheet = null;
        String[] platCodeFormName = null;
        String tempName = "temp"+UUID.randomUUID();
        try {
            temp=File.createTempFile(tempName,".xls");
            fileOutputStream = new FileOutputStream(temp,false);
            excelWriter = EasyExcel.write(fileOutputStream).build();
            for(int i = 0;i<pageMaxSize;i++){
                sheetcount = i;
                for(int j = 0;j<perPageMaxSize;j++){
                    List<E> dataList = exportDataHandler.getExportData(-1,new Page(j+1L,DsConstants.MYBATIS_PLUS_PAGE_SIZE), service,search);
                    if(dataList==null || dataList.isEmpty()){
                        i = pageMaxSize+1;
                        j = perPageMaxSize+1;
                        //处理末尾数据
                        dataList = exportDataHandler.getExportData(2,new Page(j+1L,DsConstants.MYBATIS_PLUS_PAGE_SIZE), service,search);
                        if(dataList.isEmpty()){
                            break;
                        }
                        writeSheet = EasyExcel.writerSheet(sheetcount,fileName+sheetcount).head(cls).build();
                        excelWriter.write(dataList, writeSheet);
                        continue;
                    }
                    writeSheet = EasyExcel.writerSheet(sheetcount,fileName).head(cls).build();
                    excelWriter.write(dataList, writeSheet);
                    dataList.clear();
                }
            }
            ossOrderFileRspDto.setCode(1);
            excelWriter.finish();
            inputStream=new FileInputStream(temp);
            ossOrderFileRspDto=store(inputStream,excelName);
            return ossOrderFileRspDto;
        } finally{
            if(null != fileOutputStream){
                try {
                    fileOutputStream.close();
                }catch (Exception e){
                    log.error(e.getMessage());
                }
            }
            if(null != inputStream){
                try {
                    inputStream.close();
                }catch (Exception e){
                    log.error(e.getMessage());
                }
            }
            if(temp.exists()){
                //todo 优化
                Files.delete(temp.toPath());
            }
        }
    }

    /**
     * 上传至os服务器
     * @param inputStream
     * @return
     */
    public OssOrderFileRspDto store(@Nullable FileInputStream inputStream,String excelName){
        //String fileName = new SimpleDateFormat("yyyy-MM-dd HH_mm_ss").format(new Date());
        String fileKey = domainName + SLASH
            + appName + SLASH
            + EXPORT + SLASH
            + DateUtil.format(new Date(),"yyyyMM") + SLASH
            + excelName  + "-" + DateUtil.getTodayString() + "-" + UUIDUtil.generateUuid()
            + ".xlsx";
        FileUploadResult fileUploadResult = fileUploadService.uploadFile(fileKey,inputStream);
        return new OssOrderFileRspDto().setFileKey(fileKey).setFileUrl(fileUploadResult.getName());
    }

}
