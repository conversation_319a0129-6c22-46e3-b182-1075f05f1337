package cn.hydee.middle.business.order.dto.erp.inner;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/23 17:04
 */
@Data
public class ErpWarePickInfo {
    /** 商品erp编码 */
    private String warecode;
    /** 商品数量 */
    private Integer wareqty;
    /** 商品批号 */
    private String makeno;
    /** 商品价格 */
    private BigDecimal netprice;

    public ErpWarePickInfo(String warecode, Integer wareqty){
        this.warecode = warecode;
        this.wareqty = wareqty;
    }
    /** 拣货 */
    public ErpWarePickInfo(String warecode, Integer wareqty, String makeno){
        this.warecode = warecode;
        this.wareqty = wareqty;
        this.makeno = makeno;
    }

    /** 零售下账 */
    public ErpWarePickInfo(String warecode, Integer wareqty, String makeno, BigDecimal netprice){
        this.warecode = warecode;
        this.wareqty = wareqty;
        this.makeno = makeno;
        this.netprice = netprice;
    }
}
