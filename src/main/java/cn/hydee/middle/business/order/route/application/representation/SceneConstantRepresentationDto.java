package cn.hydee.middle.business.order.route.application.representation;


import cn.hydee.middle.business.order.route.domain.dto.CodeData;
import cn.hydee.middle.business.order.route.domain.enums.SceneTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
@ApiModel(description = "场景常量数据")
public class SceneConstantRepresentationDto {

  @ApiModelProperty(value = "场景类型常量")
  List<CodeData> sceneTypeConstant = SceneTypeEnum.getSelect();

  public static SceneConstantRepresentationDto builder() {
    return new SceneConstantRepresentationDto();
  }
}
