package cn.hydee.middle.business.order.dto.dutycasher.rsp;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.entity.OrderStoreDefaultDutyCasher;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/07/11
 */
@Data
public class QueryAuthInfoRspDto {

    @ApiModelProperty(value = "线下门店是否有设置成不按批号下账的线上门店，true：有，false：没有")
    private boolean hasOnlineStoreBatchNoStockFlag;
    @ApiModelProperty(value = "是否有开启自动下账的线下门店，true：绿色，false：红色")
    private boolean hasOpenFlag;
    @ApiModelProperty(value = "开启自动下账的线下店数据")
    private OrderStoreDefaultDutyCasher data;

    public static QueryAuthInfoRspDto buildBean(List<OrderStoreDefaultDutyCasher> dataList,Integer orgStoreBatchNoStockCount){
        QueryAuthInfoRspDto dto = new QueryAuthInfoRspDto();
        if(DsConstants.INTEGER_ZERO.compareTo(orgStoreBatchNoStockCount) < 0){
            dto.setHasOnlineStoreBatchNoStockFlag(Boolean.TRUE);
        }else{
            dto.setHasOnlineStoreBatchNoStockFlag(Boolean.FALSE);
        }
        if(CollectionUtils.isEmpty(dataList)){
            dto.setHasOpenFlag(Boolean.FALSE);
            dto.setData(null);
        }else{
            dto.setData(dataList.get(0));
            if(DsConstants.INTEGER_ONE.equals(dto.getData().getOpenFlag())){
                dto.setHasOpenFlag(Boolean.TRUE);
            }else {
                dto.setHasOpenFlag(Boolean.FALSE);
            }
        }
        return dto;
    }

    public boolean needAllAutoEnterAccount(){
        return this.hasOnlineStoreBatchNoStockFlag && hasOpenFlag && null != data;
    }

    public static QueryAuthInfoRspDto buildFalseBean(){
        QueryAuthInfoRspDto dto = new QueryAuthInfoRspDto();
        dto.setHasOnlineStoreBatchNoStockFlag(false);
        dto.setHasOpenFlag(false);
        dto.setData(null);
        return dto;
    }
}
