package cn.hydee.middle.business.order.yxtadapter.domain.correctiontool.qry;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 修数工具返回类
 * Created by Intellij IDEA.
 *
 * <AUTHOR>
 * Date:  2024/1/3
 */
@Data
public class CorrectionToolQryResultDTO implements Serializable {

    private CorrectionToolOrderInfo orderInfo;

    private List<CorrectionToolOrderDetailInfo> orderDetailInfoList;

}
