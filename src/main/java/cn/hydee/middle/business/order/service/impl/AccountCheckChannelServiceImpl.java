package cn.hydee.middle.business.order.service.impl;

import cn.hydee.middle.business.order.entity.AccountCheckChannel;
import cn.hydee.middle.business.order.mapper.AccountCheckChannelMapper;
import cn.hydee.middle.business.order.service.AccountCheckChannelService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 渠道数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-06
 */
@Service
@Slf4j
public class AccountCheckChannelServiceImpl extends ServiceImpl<AccountCheckChannelMapper, AccountCheckChannel> implements AccountCheckChannelService {

    @Override
    public void saveUpdateBatch(List<AccountCheckChannel> dataList){
        try {
            // 去重
            dataList = dataList.stream().distinct().collect(Collectors.toList());
            List<String> queryList = dataList.stream().map(AccountCheckChannel::getMd5Code).collect(Collectors.toList());
            queryList = queryList.stream().distinct().collect(Collectors.toList());
            QueryWrapper<AccountCheckChannel> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(AccountCheckChannel::getMd5Code, queryList);
            List<AccountCheckChannel> existDataList = baseMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(existDataList)) {
                existDataList = Collections.emptyList();
            }
            Map<String, AccountCheckChannel> dataMap = existDataList.stream().collect(Collectors.toMap(AccountCheckChannel::getMd5Code, a -> a, (v1, v2) -> v1));
            List<AccountCheckChannel> updateDataList = dataList.stream().filter(data -> dataMap.containsKey(data.getMd5Code())).collect(Collectors.toList());
            List<AccountCheckChannel> insertDataList = dataList.stream().filter(data -> !dataMap.containsKey(data.getMd5Code())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(updateDataList)) {
                updateDataList.forEach(data -> {
                    data.setId(dataMap.get(data.getMd5Code()).getId());
                    data.setModifyTime(new Date());
                });
                updateBatchById(updateDataList);
            }
            if (!CollectionUtils.isEmpty(insertDataList)) {
                insertDataList.forEach(data -> {
                    data.setCreateTime(new Date());
                });
                saveBatch(insertDataList);
            }
        }catch (Exception e) {
            log.info("AccountCheckChannelServiceImpl Batch error,dataList:{},cause:{}",dataList,e);
        }
    }
}
