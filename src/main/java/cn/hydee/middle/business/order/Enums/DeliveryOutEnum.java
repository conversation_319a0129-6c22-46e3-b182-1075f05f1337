/*  
 * Project Name:hydee-business-order  
 * File Name:DeliveryOutEnum.java  
 * Package Name:cn.hydee.middle.business.order.Enums.v2  
 * Date:2020年8月19日下午3:45:15  
 * Copyright (c) 2020, Shanghai Hydee Software Corp., Ltd. All Rights Reserved.  
 *  
*/  
  
package cn.hydee.middle.business.order.Enums;  
/**  
 * ClassName:DeliveryOutEnum <br/>  
 * Function: 配送出库业务类型. <br/> 
 * Date:     2020年8月19日 下午3:45:15 <br/>  
 * <AUTHOR>  
 * @since    JDK 1.8  
 */
public enum DeliveryOutEnum {
	
	/** 0:自配送时配送出库 */
	DELIVERY_OUT(0, "自配送时配送出库"),
	/** 1:自配送时三方骑手配送出库*/
	THIRD_RIDER_DELIVERY_OUT(1, "自配送时三方骑手配送出库"),
	/** 2:快递配送时配送出库*/
	EXPRESS_DELIVERY_OUT(2, "快递配送时配送出库"),
	/** 2:平安平台配送出库*/
	DELIVERY_OUT_PING_AN_PLAT(2, "平安平台配送出库"),
	;

    private Integer code;
    private String msg;

    DeliveryOutEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
  
