package cn.hydee.middle.business.order.doris.dto.rsp;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.util.RedisKeyUtil;
import cn.hydee.middle.business.order.util.redis.RedisStringUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/12/28
 */
@Data
public class OrderPayQueryByOrgAllRspDto {

    @ApiModelProperty(value = "分页数据")
    private IPage<OrderPayQueryByOrgRspDto> pageData;

    @ApiModelProperty(value = "不需要排序数据")
    private OrderPayQueryByOrgNotSortRspDto notSortData;

    public static OrderPayQueryByOrgAllRspDto buildDefaultBean(){
        OrderPayQueryByOrgAllRspDto dto = new OrderPayQueryByOrgAllRspDto();
        dto.setNotSortData(OrderPayQueryByOrgNotSortRspDto.buildBean(DsConstants.STRING_ZERO.equals(RedisStringUtil.getValue(RedisKeyUtil.REDIS_KEY_DORIS_SORT_KEY))));
        dto.setPageData(new Page<>());
        return dto;
    }

    public static OrderPayQueryByOrgAllRspDto buildBean(IPage<OrderPayQueryByOrgRspDto> pageData,boolean openFlag){
        OrderPayQueryByOrgAllRspDto dto = new OrderPayQueryByOrgAllRspDto();
        dto.setNotSortData(OrderPayQueryByOrgNotSortRspDto.buildBean(openFlag));
        dto.setPageData(pageData);
        return dto;
    }
}
