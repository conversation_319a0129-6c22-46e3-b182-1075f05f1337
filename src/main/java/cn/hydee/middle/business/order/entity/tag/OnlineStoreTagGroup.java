package cn.hydee.middle.business.order.entity.tag;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 店铺标签组
 * @TableName online_store_tag_group
 */
@TableName(value ="online_store_tag_group")
@Data
public class OnlineStoreTagGroup implements Serializable {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 父类ID
     */
    private Long parentId;

    /**
     * 标签组名称
     */
    private String groupName;

    /**
     * 层级
     */
    private Integer groupLevel;

    /**
     * 创建人Id
     */
    private String creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人Id
     */
    private String modifierId;

    /**
     * 修改人名称
     */
    private String modifierName;

    /**
     * 修改时间
     */
    private Date modifyTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}