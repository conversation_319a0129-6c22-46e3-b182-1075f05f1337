package cn.hydee.middle.business.order.dto.req;

import cn.hydee.middle.business.order.dto.rsp.CommodityRspDto;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
 * cn.hydee.middle.business.order.dto.req
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/7/29 18:46
 **/
@Data
public class RecaptureDetailsReqDto {
    @JsonIgnore
    private String merCode;
    @JsonIgnore
    private String userId;
    @ApiModelProperty("三方订单号")
    private String thirdPlatformNo;
    @ApiModelProperty("平台编码")
    private String thirdPlatformCode;
    @ApiModelProperty("系统订单号")
    @NotNull
    private Long  orderNo;
    @ApiModelProperty("商品信息集合列表")
    private List<RecaptureCommodityReqDto> recaptureCommodityReqDtos;
}
