package cn.hydee.middle.business.order.entity;

import cn.hydee.middle.business.order.Enums.DsConstants;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 线下门店配置
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DsOfflineStoreConfig implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "商户编码")
    private String merCode;

    @ApiModelProperty(value = "线下门店编码")
    private String organizationCode;

    @ApiModelProperty(value = "按模板打印小票开关,0-关闭 1-开启 默认0-关闭")
    private Integer printFlag;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 末次修改时间
     */
    private Date modifyTime;


    public static DsOfflineStoreConfig buildDefaultBean(String merCode,String organizationCode) {
        DsOfflineStoreConfig dsOfflineStoreConfig = new DsOfflineStoreConfig();
        dsOfflineStoreConfig.setMerCode(merCode);
        dsOfflineStoreConfig.setOrganizationCode(organizationCode);
        dsOfflineStoreConfig.setPrintFlag(DsConstants.INTEGER_ZERO);
        return dsOfflineStoreConfig;
    }
}
