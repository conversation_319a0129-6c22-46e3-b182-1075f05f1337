package cn.hydee.middle.business.order.dto.order.refund.statistics;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/04/26
 */
@Data
public class CalculateRefundStatisticsDTO implements Serializable {

    private static final long serialVersionUID = 5678000774102744645L;

    @ApiModelProperty(value = "商户编码")
    private String merCode;

    @ApiModelProperty(value = "开始时间")
    @NotNull(message = "开始时间不能为空")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @NotNull(message = "结束时间不能为空")
    private Date endTime;
}
