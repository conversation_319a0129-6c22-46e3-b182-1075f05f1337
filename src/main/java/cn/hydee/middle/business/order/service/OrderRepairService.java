package cn.hydee.middle.business.order.service;

import cn.hydee.middle.business.order.dto.req.OrderRepairReqDto;
import cn.hydee.middle.business.order.eo.OrderRepairEo;
import cn.hydee.middle.business.order.yxtadapter.domain.pushordertool.command.PushOrderToolRequest;

import java.util.List;


public interface OrderRepairService {

    boolean validSupplier(String merCode,String platformCode,String clientCode);

    List<String> repair(OrderRepairReqDto orderRepairReqDto);

    List<String> repairByImport(OrderRepairReqDto orderRepairReqDto);


}
