package cn.hydee.middle.business.order.Enums.aftersale;

import cn.hydee.middle.business.order.dto.req.RefundReturnOperationHandleReq;
import cn.hydee.middle.business.order.refund.handle.agree.entity.RefundHandleParams;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RefundAuditType {

  //审核类型，1同意退款，2同意退货退款，3拒绝，4确认收货，5验货不通过/未收到货
  AGREE_ONLY_REFUND(1, "同意退款"),
  AGREE_RETURN_REFUND(2, "同意退货退款"),
  REFUSE(3, "拒绝"),
  CONFIRM_RECEIVE(4, "确认收货"),
  VERIFY_FAIL(5, "验货不通过/未收到货"),
  AGREE_CANCEL(6,"同意取消"),
  REFUSE_CANCEL(7,"拒绝取消");

  final Integer type;
  final String desc;

  public static RefundAuditType getByRefundHandleParam(RefundHandleParams handleParams) {
    RefundAuditType refundAuditType = null;
    if (handleParams.getType() == 1) {
      refundAuditType = RefundAuditType.AGREE_ONLY_REFUND;
    } else if (handleParams.getType() == 2) {
      refundAuditType = RefundAuditType.AGREE_RETURN_REFUND;
    } else {
      refundAuditType = RefundAuditType.REFUSE;
    }
    return refundAuditType;
  }

  public static RefundAuditType getByRefundReturnHandleParam(RefundReturnOperationHandleReq refundHandle) {
    return refundHandle.getType() == 1 ? RefundAuditType.CONFIRM_RECEIVE
            : RefundAuditType.VERIFY_FAIL;
  }

}
