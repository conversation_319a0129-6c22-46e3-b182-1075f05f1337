package cn.hydee.middle.business.order.service.impl;

import cn.hydee.middle.business.order.dto.req.OrderDutyCasherReq;
import cn.hydee.middle.business.order.entity.OrderDutyCasher;
import cn.hydee.middle.business.order.mapper.OrderDutyCasherMapper;
import cn.hydee.middle.business.order.service.OrderDutyCasherService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2020/8/20
 */
@Service
public class OrderDutyCasherServiceImpl implements OrderDutyCasherService {

    private final RedisTemplate<String, Object> redisTemplate;
    private final OrderDutyCasherMapper orderDutyCasherMapper;

    public OrderDutyCasherServiceImpl(RedisTemplate<String, Object> redisTemplate,
                                      OrderDutyCasherMapper orderDutyCasherMapper) {
        this.redisTemplate = redisTemplate;
        this.orderDutyCasherMapper = orderDutyCasherMapper;
    }

    @Override
    public boolean addOrderOperateDuty(OrderDutyCasherReq searchReq) {
        QueryWrapper<OrderDutyCasher> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OrderDutyCasher::getSearchKey, searchReq.generateKey());
        OrderDutyCasher orderDutyCasher = orderDutyCasherMapper.selectOne(queryWrapper);
        if (orderDutyCasher != null) {
            orderDutyCasher.setCashierSource(searchReq.getCashierSource());
            orderDutyCasher.setCashierSourceName(searchReq.getCashierSourceName());
            orderDutyCasher.setDutyId(searchReq.getDutyId());
            orderDutyCasher.setDutyName(searchReq.getDutyName());
            orderDutyCasherMapper.updateById(orderDutyCasher);
        } else {
            orderDutyCasher = new OrderDutyCasher();
            BeanUtils.copyProperties(searchReq, orderDutyCasher);
            orderDutyCasher.setSearchKey(searchReq.generateKey());
            Date now = new Date();
            orderDutyCasher.setCreateTime(now);
            orderDutyCasher.setModifyTime(now);
            orderDutyCasherMapper.insert(orderDutyCasher);
        }
        return true;
    }

    @Override
    public OrderDutyCasherReq getOrderOperateDuty(String orderNoType) {
        ValueOperations<String, Object> opsForValue = redisTemplate.opsForValue();
        HashMap<String, String> map = (HashMap<String, String>) opsForValue.get(orderNoType);
        if (CollectionUtils.isEmpty(map)) {
            return getFromDb(orderNoType);
        }
        return new OrderDutyCasherReq(map);
    }


    private OrderDutyCasherReq getFromDb(String searchKey) {
        QueryWrapper<OrderDutyCasher> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OrderDutyCasher::getSearchKey, searchKey);
        queryWrapper.last(" limit 1 ");
        OrderDutyCasher orderDutyCasher = orderDutyCasherMapper.selectOne(queryWrapper);
        if (orderDutyCasher != null) {
            OrderDutyCasherReq orderDutyCasherReq = new OrderDutyCasherReq();
            BeanUtils.copyProperties(orderDutyCasher, orderDutyCasherReq);
            return orderDutyCasherReq;
        }
        return null;
    }
}
