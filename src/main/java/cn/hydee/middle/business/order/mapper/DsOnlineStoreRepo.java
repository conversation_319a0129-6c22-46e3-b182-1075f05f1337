package cn.hydee.middle.business.order.mapper;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.batch.export.dto.DsOnlineStoreQueryExportDTO;
import cn.hydee.middle.business.order.comment.base.dto.resp.OnlineStoreWithAppInfoRespDto;
import cn.hydee.middle.business.order.comment.base.dto.resp.StoreManageExportRespDto;
import cn.hydee.middle.business.order.doris.dto.OrgStoreQueryDto;
import cn.hydee.middle.business.order.dto.DsOnlineStorePriscriptionDto;
import cn.hydee.middle.business.order.dto.DsOnlineStorePriscriptionQryDto;
import cn.hydee.middle.business.order.dto.MerOrganizationCode;
import cn.hydee.middle.business.order.dto.onlinestore.OnlineStorePrintTemplateDto;
import cn.hydee.middle.business.order.dto.req.baseinfo.DsOnlineStorePageReqDto;
import cn.hydee.middle.business.order.dto.req.baseinfo.DsOnlineStoreQueryDTO;
import cn.hydee.middle.business.order.dto.req.baseinfo.GetOnlineStoreListByPlatReqDto;
import cn.hydee.middle.business.order.dto.rsp.DsStoreConfDTO;
import cn.hydee.middle.business.order.dto.rsp.OrderStoreTemplateRspDto;
import cn.hydee.middle.business.order.dto.rsp.SearchOrderRspDto;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.DsOnlineStoreResDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.GetOnlineStoreByPlatResDto;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.PageQueryOnlineStoreCommand;
import cn.hydee.middle.business.order.dto.tag.req.DsOnlineStoreReq;
import cn.hydee.middle.business.order.dto.tag.req.StoreUnBindReq;
import cn.hydee.middle.business.order.dto.tag.resp.DsOnlineStoreRespDto;
import cn.hydee.middle.business.order.entity.DsOnlineStore;
import cn.hydee.middle.business.order.entity.DsStoreOrderConfig;
import cn.hydee.middle.business.order.entity.OrderInfo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * <p>
 * o2o平台门店信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-31
 */
@Repository
public interface DsOnlineStoreRepo extends BaseMapper<DsOnlineStore> {

    /**
     * 批量新增
     * @param list
     * @return
     */
    int insertBatch(List<DsOnlineStore> list);

    /**
     * 批量新增B2C的O2O线上门店
     * @param list
     * @return
     */
    int insertB2CStoreBatch(List<DsOnlineStore> list);


    /**
     * 批量修改
     * @param list 线上门店列表
     * @return int
     */
    int updateByBatch(List<DsOnlineStore> list);


    /**
     * 查询平台已绑定机构的线上门店列表
     * @param dto
     * @return
     */
    @DS(DsConstants.DB_ORDER_SLAVE)
    List<GetOnlineStoreByPlatResDto> getOnlineStoreListByPlatform(@Param("dto") GetOnlineStoreListByPlatReqDto dto);

    @DS(DsConstants.DB_ORDER_SLAVE)
    Integer countNoOrganization(@Param("dto") GetOnlineStoreListByPlatReqDto dto);

    @DS(DsConstants.DB_ORDER_SLAVE)
    Page<OnlineStoreWithAppInfoRespDto> getOnlineStoreWithAppInfo(@Param("page") Page<OnlineStoreWithAppInfoRespDto> pageBase, @Param("merCodeList") List<String> merCodeList, @Param("platformCodeList") List<String> platformCodeList,@Param("onlineStoreCodeList") List<String> onlineStoreCodeList);


    @DS(DsConstants.DB_ORDER_SLAVE)
    List<DsOnlineStoreResDTO> listOnlineStoreWithConfig(DsOnlineStoreQueryDTO dto);

    @DS(DsConstants.DB_ORDER_SLAVE)
    IPage<DsOnlineStore> pageQueryOnlineStore(Page<DsOnlineStore> page,@Param("param")PageQueryOnlineStoreCommand dto);


    @DS(DsConstants.DB_ORDER_SLAVE)
    Integer countOnlineStoreWithConfig(DsOnlineStoreQueryDTO dto);




    @DS(DsConstants.DB_ORDER_SLAVE)
    DsStoreOrderConfig getPrintTemplateId(@Param("param") OrderInfo orderInfo);

    @DS(DsConstants.DB_ORDER_SLAVE)
    List<OnlineStorePrintTemplateDto> getOnlineStorePrintTemplate(@Param("merCode") String merCode,@Param("orderList") List<OrderInfo> orderList);



    @DS(DsConstants.DB_ORDER_SLAVE)
    List<MerOrganizationCode> selectDistinctMerOrg(@Param("merCode") String merCode, @Param("organizationCodeList") List<String> organizationCodeList);

    @DS(DsConstants.DB_ORDER_SLAVE)
    List<DsOnlineStore> selectDistinctMerOrgPlatStore(@Param("merCode") String merCode, @Param("organizationCodeList") List<String> organizationCodeList);

    @DS(DsConstants.DB_ORDER_SLAVE)
    DsOnlineStore selectStore(@Param("merCode") String merCode, @Param("thirdPlatformCode") String thirdPlatformCode, @Param("onlineStoreCode") String onlineStoreCode, @Param("clientCode") String clientCode);

//    @DS(DsConstants.DB_ORDER_SLAVE)
//    DsOnlineStore selectOrg(@Param("merCode") String merCode, @Param("organizationCode") String organizationCode);


    @DS(DsConstants.DB_ORDER_SLAVE)
    DsOnlineStore getOnlineStore(@Param("merCode") String merCode, @Param("platformCode") String thirdPlatformCode, @Param("onlineStoreCode") String onlineStoreCode,@Param("onlineClientCode") String onlineClientCode);



    @DS(DsConstants.DB_ORDER_SLAVE)
    List<StoreManageExportRespDto> getStoreManageData(DsOnlineStoreQueryExportDTO reqDTO);

    @DS(DsConstants.DB_ORDER_SLAVE)
    List<DsOnlineStore> selectOrgList(@Param("param") OrgStoreQueryDto reqDto);

    @DS(DsConstants.DB_ORDER_SLAVE)
    List<DsOnlineStore> selectOrgStoreList(@Param("param") OrgStoreQueryDto reqDto);

    @DS(DsConstants.DB_ORDER_SLAVE)
    List<OrderStoreTemplateRspDto> getOrderConfigByList(@Param("list") List<SearchOrderRspDto> records);

//    @DS(DsConstants.DB_ORDER_SLAVE)
//    StoreContactInfoDto selectStoreOne(@Param("merCode") String merCode, @Param("thirdPlatformCode") String thirdPlatformCode, @Param("onlineStoreCode") String onlineStoreCode, @Param("clientCode") String clientCode);

    @DS(DsConstants.DB_ORDER_SLAVE)
    List<DsOnlineStorePriscriptionDto> selectPrescriptionConf(@Param("params") List<DsOnlineStorePriscriptionQryDto> dsOnlineStorePriscriptionQryDtoList);

    @DS(DsConstants.DB_ORDER_SLAVE)
    List<DsOnlineStore> selectDsOnlineStoreByPage(@Param("param")DsOnlineStorePageReqDto dto);



//    @DS(DsConstants.DB_ORDER_SLAVE)
//    DsOnlineStore selectOneDsOrderslave(@Param("merCode") String merCode, @Param("thirdPlatformCode") String thirdPlatformCode, @Param("onlineStoreCode") String onlineStoreCode, @Param("clientCode") String clientCode);

    @DS(DsConstants.DB_ORDER_SLAVE)
    List<DsStoreConfDTO> obtainB2CStoreByOrganizatioinList(@Param("orgizationList") List<String> orgizationList);
    /** 传入机构 获取对应渠道开启的门店
     * <AUTHOR>
     * @Description
     * @date 2024/4/23 9:31
//     */
//    @DS(DsConstants.DB_ORDER_SLAVE)
//    DsOnlineStore getOpenStatusByCode(@Param("param") DsSynOnlineStoreQueryDTO store);


    @DS(DsConstants.DB_ORDER_SLAVE)
    List<DsOnlineStore> listByOrganizationCodeList(@Param("organizationCodeList") List<String> organizationCodeList);

    @DS(DsConstants.DB_ORDER_SLAVE)
    IPage<DsOnlineStoreRespDto> selectOnlineStorePage(Page<DsOnlineStoreReq> reqPage, @Param("param") DsOnlineStoreReq dto);

    @DS(DsConstants.DB_ORDER_SLAVE)
    IPage<DsOnlineStoreRespDto> selectUnBindStorePage(Page<DsOnlineStoreReq> reqPage, @Param("param") StoreUnBindReq dto);

    @DS(DsConstants.DB_ORDER_SLAVE)
    List<Long> selectUnBindStoreId(@Param("param") StoreUnBindReq dto);

    //查询没有对应配送平台的门店数量
    @DS(DsConstants.DB_ORDER_SLAVE)
    int countNoneDeliveryStore(@Param("merCode") String merCode, @Param("deliveryPlatformCode") String deliveryPlatformCode);

    //查询没有对应配送平台的门店编码
    @DS(DsConstants.DB_ORDER_SLAVE)
    List<String> queryNoneDeliveryStore(@Param("merCode") String merCode, @Param("deliveryPlatformCode") String deliveryPlatformCode, @Param("currentPage") int currentPage, @Param("pageSize") int pageSize);

    @DS(DsConstants.DB_ORDER_SLAVE)
    List<DsOnlineStore> queryStoreBaseInfo(@Param("storeIds") List<Long> storeIds);

}
