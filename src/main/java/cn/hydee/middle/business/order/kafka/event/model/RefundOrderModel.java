package cn.hydee.middle.business.order.kafka.event.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 退单模型
 *
 * <AUTHOR> (moatkon)
 * @date 2024年06月17日 11:39
 * @email: <EMAIL>
 */
@Data
public class RefundOrderModel {

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  // 商家编码
  private String merCode;

  private String thirdPlatformCode;

  // 退单号
  private Long refundNo;
  /**
   * 三方平台退款ID
   */
  private String thirdRefundNo;


  //正向订单号
  private Long orderNo;

  /**
   * 第三方平台订单号
   */
  private String thirdOrderNo;


  // 退款完成时间
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date completeTime;

  // 退款单状态 RefundStateEnum
  private Integer refundStatus;

  // 会员卡号
  private String memberCard;

  // 会员卡号
  private Long memberId;

  /**
   * 线下门店编码
   */
  private String organizationCode;

  /**
   * 线上门店编码
   */
  private String onlineStoreCode;

  /**
   * 来源线下门店编码
   */
  private String sourceOrganizationCode;

  /**
   * 来源线上门店编码
   */
  private String sourceOnlineStoreCode;

  /**
   * 退款金额 consumer_refund 退买家的金额
   */
  private BigDecimal consumerRefund;

  /**
   * 退平台配送费
   */
  private BigDecimal platformRefundDeliveryFee;

  /**
   * 退商家配送费
   */
  private BigDecimal merchantRefundPostFee;

  @ApiModelProperty(value = "未下账的部分退款单，是否已重新计算原单财务数据等金额。0-未计算 1-已计算")
  @TableField(exist = false)
  private Integer reCalculateOriginOrderFlag;

  /**
   * 创建时间
   */
  @ApiModelProperty(value = "创建时间")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date createTime;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  @TableField(exist = false)
  private Date updateTime;


  // 商品明细
  @TableField(exist = false)
  private List<RefundDetailModel> detailList;
  
  /**
   * 退款单erp状态
   */
  @ApiModelProperty(value = "下账状态: 20  待下帐  100 已下账  120 已取消")
  @TableField(exist = false)
  private Integer erpState;



}
