package cn.hydee.middle.business.order.dto.the3;

import cn.hydee.middle.business.order.dto.rsp.baseinfo.The3DsStoreResDTO;
import cn.hydee.middle.business.order.mapper.DsOnlineClientRepo;
import java.util.List;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 对外API请求基本参数处理
 *
 * <AUTHOR>
 * @Description
 * @date 2024/1/17 15:49
 */
@Service
public class BaseParamForRequest {

    @Resource
    private DsOnlineClientRepo dsOnlineClientRepo;


    public void loadMerchantParam(final String merCode, final String platformCode, final String onlineStoreCode, String clientCode, final O2oBaseDTO requestParam) {
        List<The3DsStoreResDTO> dtoList = dsOnlineClientRepo.getStoreAccessByStoreCode(merCode, platformCode, onlineStoreCode, clientCode);
        The3DsStoreResDTO dto = dtoList.stream().findFirst().orElse(null);
        if (dto == null) {
            throw new RuntimeException("未找到对应的商户信息");
        }
        requestParam.setAppKey(dto.getAppid());
        requestParam.setAppSecret(dto.getAppSecret());
        requestParam.setOnlineClientOutCode(dto.getOnlineClientOutCode());
        requestParam.setPlaceShopCode(dto.getPlatformShopId());
        requestParam.setPlatformCode(platformCode);
    }
}
