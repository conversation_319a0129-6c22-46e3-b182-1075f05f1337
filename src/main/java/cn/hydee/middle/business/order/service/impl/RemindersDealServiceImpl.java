package cn.hydee.middle.business.order.service.impl;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.Enums.OrderUpdateCodeEnum;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.entity.OrderRemind;
import cn.hydee.middle.business.order.mapper.OrderInfoMapper;
import cn.hydee.middle.business.order.service.OrderInfoService;
import cn.hydee.middle.business.order.service.OrderRemindService;
import cn.hydee.middle.business.order.service.RemindersDealService;
import cn.hydee.middle.business.order.service.rocket.MessageProducerService;
import cn.hydee.middle.business.order.util.DateUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 催单处理逻辑处理
 * <AUTHOR>
 * @date 2020/8/21
 */
@Service
@Slf4j
public class RemindersDealServiceImpl implements RemindersDealService {

    @Autowired
    private OrderRemindService orderRemindService;
    @Autowired
    private OrderInfoService orderInfoService;
    @Autowired
    private MessageProducerService messageProducerService;

    @Value("${reminderOrderBeforeDay:10}")
    private Integer reminderOrderBeforeDay;
    @Resource
    private OrderInfoMapper orderInfoMapper;

    /**
     * 订单的催单消息1小时内未回复（催单消息只保留1小时），则清空消息，不再展示在催单列表
     */
    @Override
    public void remindersDeal() {
        Date dayBefore = DateUtil.getDayBefore(new Date(), reminderOrderBeforeDay);

        Integer pageNo = 1;
        Integer pageSize = 500;
        long totalPage = 1;
        boolean setTotalPage = false;
        do {
            Page<OrderInfo> page = new Page<>(pageNo, pageSize);
            List<OrderInfo> orderInfoList = orderInfoMapper.getRemindOrder(page, dayBefore);
            if (!setTotalPage) {
                totalPage = page.getPages();
                setTotalPage = true;
            }
            if (!CollectionUtils.isEmpty(orderInfoList)) {
                try {
                    remind(orderInfoList);
                } catch (Exception e) {
                    log.error("remind error,by page", e);
                }
            }
            pageNo = pageNo + 1;
        } while (pageNo <= totalPage);


    }

    private void remind(List<OrderInfo> orderInfoList) {
        for (OrderInfo orderInfo : orderInfoList){
            //2.根据订单号查询出订单最新的催单消息
            OrderRemind orderRemind = orderRemindService.selectLatestByOrderNo(orderInfo.getOrderNo());
            //如果催单消息已回复，则不做操作
            if(orderRemind == null || DsConstants.INTEGER_ONE.equals(orderRemind.getStatus())){
                continue;
            }
            //3.催单消息如果已经超过了一小时未回复，则修改orderInfo的催单标识为0
            try {
                //获取第三方的催单时间
                boolean result = DateUtil.judgmentDate(orderRemind.getCreateTime(),1);
                if(!result){
                    log.info("订单催单时间超过了一小时,orderNo:{}",orderInfo.getOrderNo());
                    OrderInfo updateOrder = new OrderInfo();
                    updateOrder.setOrderNo(orderInfo.getOrderNo());
                    updateOrder.setRemindFlag(DsConstants.INTEGER_ZERO);
                    orderInfoService.updateOrder(updateOrder);
                    //订单更新异步消费
                    messageProducerService.produceUpdateOrderMessage(Collections.singletonList(orderInfo.getOrderNo()), OrderUpdateCodeEnum.TASK_REMIND.getCode());
                }
            } catch (Exception e) {
                log.error("催单回复时间判断异常",e);
            }
        }
    }
}
