package cn.hydee.middle.business.order.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OrderPrescriptionDataQryRespDto {

    @ApiModelProperty(value = "商户编码")
    private String merCode;
    @ApiModelProperty(value = "平台编码")
    private String thirdPlatformCode;
    @ApiModelProperty(value = "网店编码")
    private String clientCode;
    @ApiModelProperty(value = "门店编码")
    private String onlineStoreCode;
    @ApiModelProperty(value = "平台订单号")
    private String thirdOrderNo;
    @ApiModelProperty(value = "系统订单号")
    private Long orderNo;
    @ApiModelProperty(value = "B2C系统订单号")
    private Long omsOrderNo;
    @ApiModelProperty(value = "服务模式：O2O,B2C")
    private String serviceMode;

    @ApiModelProperty(value = "接口中台秘钥")
    private String sessionKey;

}
