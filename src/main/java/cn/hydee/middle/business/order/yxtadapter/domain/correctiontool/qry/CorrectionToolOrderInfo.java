package cn.hydee.middle.business.order.yxtadapter.domain.correctiontool.qry;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR>
 * Date:  2024/1/4
 */
@Data
public class CorrectionToolOrderInfo implements Serializable {


    /**
     * 订单号，雪花算法
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @ApiModelProperty(value ="订单号")
    private Long orderNo;


    /**
     * 平台code
     */
    @ApiModelProperty(value ="三方平台编码")
    private String thirdPlatformCode;
    /**
     * 平台code
     */
    @ApiModelProperty(value ="三方平台名称")
    private String thirdPlatformName;
    /**
     * 第三方平台订单号
     */
    @ApiModelProperty(value ="第三方平台订单号")
    private String thirdOrderNo;


    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态:5待处理,10待接单,20待拣货,30待配送,40待收货,100已完成,102已取消,101已关闭")
    private Integer orderState;

    /**
     * erp状态
     */
    @ApiModelProperty(value="下账状态: 20 待锁库存 30 待下帐  100 已下账  110 已取消")
    private Integer erpState;

    /**
     * 下账总金额
     */
    @ApiModelProperty(value = "下账总金额")
    private BigDecimal billTotalAmount;


    /**
     * 下账商品金额
     */
    @ApiModelProperty(value = "下账商品金额")
    private BigDecimal billCommodityAmount;


    /**
     * 商家配送费
     */
    @ApiModelProperty(value = "商家配送费")
    private BigDecimal merchantDeliveryFee;

    /**
     * 线下门店编码
     */
    @ApiModelProperty(value ="线下门店编码")
    private String organizationCode;

    /**
     * 线下门店名称
     */
    @ApiModelProperty(value ="线下门店名称")
    private String organizationName;

    /**
     * 线下门店编码-来源
     */
    @ApiModelProperty(value = "线下门店编码")
    private String sourceOrganizationCode;

    /**
     * 线下门店名称-来源
     */
    @ApiModelProperty(value = "线下门店名称")
    private String sourceOrganizationName;

    /**
     *  订单下账失败原因
     */
    @ApiModelProperty(value ="下账失败原因")
    private String orderAccountFailReason;



    /**
     * 拣货实施者id
     */
    @ApiModelProperty(value = "拣货实施者id")
    private String pickOperatorId;

    @ApiModelProperty(value = "拣货实施者名")
    private String pickOperatorName;


}
