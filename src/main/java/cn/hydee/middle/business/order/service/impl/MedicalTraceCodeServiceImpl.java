package cn.hydee.middle.business.order.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.Enums.DsErrorType;
import cn.hydee.middle.business.order.Enums.PlatformCodeEnum;
import cn.hydee.middle.business.order.dto.MedicalTraceCodeBatchNoReqDto;
import cn.hydee.middle.business.order.dto.req.StoreErpBatchInfoReq;
import cn.hydee.middle.business.order.dto.req.StoreStockGetReqDTO;
import cn.hydee.middle.business.order.dto.req.tracecode.*;
import cn.hydee.middle.business.order.dto.req.tracecode.MedicalTraceCodeRecordReqDto.MedicalTraceCodeDetail;
import cn.hydee.middle.business.order.dto.rsp.*;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.CheckScanTraceCodeReqDto;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.CheckScanTraceCodeRespDto;
import cn.hydee.middle.business.order.dto.rsp.erpplu.ErpRespReturnDto;
import cn.hydee.middle.business.order.dto.rsp.erpplu.HanaSyncStockRespDTO;
import cn.hydee.middle.business.order.entity.*;
import cn.hydee.middle.business.order.feign.MiddleBaseInfoClient;
import cn.hydee.middle.business.order.feign.MiddleDataSyncClient;
import cn.hydee.middle.business.order.feign.MiddleMerchandiseClient;
import cn.hydee.middle.business.order.mapper.*;
import cn.hydee.middle.business.order.service.MedicalTraceCodeService;
import cn.hydee.middle.business.order.util.ExceptionUtil;
import cn.hydee.middle.business.order.util.SpringBeanUtils;
import cn.hydee.middle.business.order.yxtadapter.domain.dictionary.InnerStoreDictionary;
import cn.hydee.middle.business.order.yxtadapter.domain.pos.hdpos.O2OBatchNoRequest;
import cn.hydee.middle.business.order.yxtadapter.domain.pos.hdpos.O2OBatchNoResponse;
import cn.hydee.middle.business.order.yxtadapter.infrastructure.domainserviceimpl.hdpos.MedicalTraceCodeException;
import cn.hydee.middle.business.order.yxtadapter.infrastructure.feign.PosInterfaceClient;
import cn.hydee.starter.dto.ResponseBase;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Preconditions;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.lucene.search.MultiCollectorManager;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 * cn.hydee.middle.business.order.service.impl
 *
 * <AUTHOR> Li
 * @version 1.0
 * @date 2020/5/11 9:29
 **/
@Service
@Slf4j
public class MedicalTraceCodeServiceImpl extends ServiceImpl<MedicalTraceCodeMapper, MedicalTraceCode> implements MedicalTraceCodeService {
    @Autowired
    private DsOnlineStoreConfigRepo dsOnlineStoreConfigRepo;
    @Autowired
    private DsOnlineStoreRepo   dsOnlineStoreRepo;

    @Autowired
    private OrderInfoMapper orderInfoMapper;

    @Autowired
    private OrderDetailMapper orderDetailMapper;

    @Autowired
    private RefundOrderMapper refundOrderMapper;

    @Autowired
    private RefundDetailMapper refundDetailMapper;

    @Autowired
    private MiddleMerchandiseClient middleMerchandiseClient;

    @Autowired
    private MiddleDataSyncClient middleDataSyncClient;

    @Autowired
    private InnerStoreDictionaryMapper innerStoreDictionaryMapper;

    @Autowired
    private OrderPickInfoMapper orderPickInfoMapper;

    @Autowired
    private PosInterfaceClient posInterfaceClient;


    @Value("${zhuishuma.maxAvailableDay:30}")
    private Long maxAvailableDay;

    @Value("${order.isCheckBatchNoNew.checkNull:false}")
    private Boolean isCheckBatchNoNewCheckNull;

    @Value("${order.isCheckBatchNo:true}")
    private Boolean isCheckBatchNo;

    @Override
    public MedicalTraceCodeDTOCheckResDTO needRecord(MedicalTraceCodeCheckReqDto medicalTraceCodeCheckReqDto) {
        OrderInfo orderInfo = orderInfoMapper.selectOrderInfo(Long.parseLong(medicalTraceCodeCheckReqDto.getOrderNo()));
        DsOnlineStoreConfig storeConfig = dsOnlineStoreConfigRepo.getStoreConfig(orderInfo.getMerCode(), orderInfo.getThirdPlatformCode(),
                orderInfo.getClientCode(), orderInfo.getOnlineStoreCode());
        MedicalTraceCodeDTOCheckResDTO dto = new MedicalTraceCodeDTOCheckResDTO();
        //没有开启配置追溯码不需要配置
        if (storeConfig==null || Objects.isNull(storeConfig.getZhuiShuMa()) || DsConstants.INTEGER_ZERO.equals(storeConfig.getZhuiShuMa())) {
            return dto;
        }
        //订单明细为空不需要配置
        List<OrderDetail> orderDetailList = orderDetailMapper.selectAllListByOrderNo(Long.parseLong(medicalTraceCodeCheckReqDto.getOrderNo()));
        if (CollectionUtils.isEmpty(orderDetailList)) {
            return dto;
        }
        //如果是逆单，正单追溯码为空时不需要录入
        if (medicalTraceCodeCheckReqDto.getType().compareTo(DsConstants.INTEGER_ONE) == 0) {
            Integer count = getBaseMapper().queryOrderTraceCodeCount(Long.parseLong(medicalTraceCodeCheckReqDto.getOrderNo()));
            if (DsConstants.INTEGER_ZERO.compareTo(count) == 0) {
                return dto;
            }


            Integer refundCount = refundOrderMapper.selectCount(Wrappers.<RefundOrder>lambdaQuery()
                .eq(RefundOrder::getRefundNo, medicalTraceCodeCheckReqDto.getRefundNo())
                //全额退款
                .eq(RefundOrder::getType, DsConstants.STRING_ONE));
            //全额退款强制校验录入追溯码
            if(medicalTraceCodeCheckReqDto.isRefundAllMustCheck()){
                List<MedicalTraceCode> medicalTraceCodes = getBaseMapper().queryOrderTraceCode(Long.parseLong(medicalTraceCodeCheckReqDto.getOrderNo()));
                Map<Long, List<MedicalTraceCode>> medicalTraceCodeMap = medicalTraceCodes.stream().collect(Collectors.groupingBy(MedicalTraceCode::getOrderDetailId));
                List<Long> orderDetailIdList = medicalTraceCodes.stream() .map(MedicalTraceCode::getOrderDetailId).collect( Collectors.toList());
                List<OrderDetail> orderDetails = orderDetailMapper.selectList(Wrappers.<OrderDetail>lambdaQuery().in(OrderDetail::getId, orderDetailIdList));
                Map<String, Long> erpCodeMap =  orderDetails.stream().collect(Collectors.toMap(OrderDetail::getErpCode, OrderDetail::getId, (k1, k2) -> k1));
                Set<String> erpCodeList = erpCodeMap.keySet();

                List<RefundDetail> refundDetailList = refundDetailMapper.selectListByRefundNo( Long.parseLong(medicalTraceCodeCheckReqDto.getRefundNo()));
                List<GoodMedicalTraceCodeDTO> needRecords = new ArrayList<>();
                for (RefundDetail refundDetail : refundDetailList) {
                    if (erpCodeList.contains(refundDetail.getErpCode())) {
                        GoodMedicalTraceCodeDTO goodMedicalTraceCodeDTO = new GoodMedicalTraceCodeDTO();
                        goodMedicalTraceCodeDTO.setErpCode(refundDetail.getErpCode());
                        Long orderDetailId = erpCodeMap.get(refundDetail.getErpCode());
                        List<MedicalTraceCode> medicalTraces =  medicalTraceCodeMap.get(
                            orderDetailId);
                        goodMedicalTraceCodeDTO.setFlag(medicalTraces.get(0).getFlag());
                        needRecords.add(goodMedicalTraceCodeDTO);
                    }
                }
                dto.setNeedRecords(needRecords);
                return dto;
            }
            //全额退款不需要录入追溯码
            if (refundCount == 1) {
                return dto;
            }
        }

        Set<String> collect = orderDetailList.stream().filter(o-> o.getStatus().compareTo(DsConstants.INTEGER_ZERO) == 0).map(OrderDetail::getErpCode).collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(medicalTraceCodeCheckReqDto.getErpCodeList())) {
            collect.addAll(medicalTraceCodeCheckReqDto.getErpCodeList());
        }
        CheckScanTraceCodeReqDto checkScanTraceCodeReqDto = new CheckScanTraceCodeReqDto();
        checkScanTraceCodeReqDto.setErpCodeList(collect);
        checkScanTraceCodeReqDto.setOrderNo(medicalTraceCodeCheckReqDto.getOrderNo());
        checkScanTraceCodeReqDto.setStoreCode(orderInfo.getOrganizationCode());
        ResponseBase<CheckScanTraceCodeRespDto> res = middleMerchandiseClient.checkScanTraceCode(checkScanTraceCodeReqDto);
        CheckScanTraceCodeRespDto data = res.getData();
        if (null!=data&&!CollectionUtils.isEmpty(data.getErpCodeWithFlagList())) {
            List<GoodMedicalTraceCodeDTO> collect1 = data.getErpCodeWithFlagList().stream().filter(d -> d.getFlag() != null).collect(Collectors.toList());
            dto.setNeedRecords(collect1);
        }
        return dto;
    }

    @Override
    public MedicalTraceCodeDTOCheckResDTO needRecordByStore(MedicalTraceCodeStoreCheckReqDto req) {

        Preconditions.checkArgument(!StringUtils.isEmpty(req.getOnlineStoreCode()), "线上店铺编码不能为空");
        Preconditions.checkArgument( null!=req.getErpCodeList() && !req.getErpCodeList().isEmpty(), "商品编码列表不能为空");
        List<DsOnlineStore> dsOnlineStores = dsOnlineStoreRepo.selectList(new QueryWrapper<DsOnlineStore>().lambda()
            .eq(DsOnlineStore::getOrganizationCode, req.getOnlineStoreCode())
            .eq(DsOnlineStore::getPlatformCode, PlatformCodeEnum.MEITUAN.getCode()));
        if(null==dsOnlineStores || dsOnlineStores.isEmpty()){
            dsOnlineStores=dsOnlineStoreRepo.selectList(new QueryWrapper<DsOnlineStore>().lambda()
            .eq(DsOnlineStore::getOrganizationCode, req.getOnlineStoreCode()));
        }
        DsOnlineStore dsOnlineStore = dsOnlineStores.get(0);
        DsOnlineStoreConfig storeConfig = dsOnlineStoreConfigRepo.selectOne(new QueryWrapper<DsOnlineStoreConfig>().lambda()
                .eq(DsOnlineStoreConfig::getOnlineStoreId, dsOnlineStore.getId()));
        MedicalTraceCodeDTOCheckResDTO dto = new MedicalTraceCodeDTOCheckResDTO();
        //没有开启配置追溯码不需要配置
        if (Objects.isNull(storeConfig.getZhuiShuMa()) || DsConstants.INTEGER_ZERO.equals(storeConfig.getZhuiShuMa())) {
            return dto;
        }

        Set<String> collect = new HashSet<>(req.getErpCodeList());
        CheckScanTraceCodeReqDto checkScanTraceCodeReqDto = new CheckScanTraceCodeReqDto();
        checkScanTraceCodeReqDto.setStoreCode(req.getOnlineStoreCode());
        checkScanTraceCodeReqDto.setErpCodeList(collect);
        ResponseBase<CheckScanTraceCodeRespDto> res = middleMerchandiseClient.checkScanTraceCode(checkScanTraceCodeReqDto);

        CheckScanTraceCodeRespDto data = res.getData();
        if (null!=data&&!CollectionUtils.isEmpty(data.getErpCodeWithFlagList())) {
            List<GoodMedicalTraceCodeDTO> collect1 = data.getErpCodeWithFlagList().stream().filter(d -> d.getFlag() != null).collect(Collectors.toList());
            dto.setNeedRecords(collect1);
        }
        return dto;
    }

    @Transactional
    @Override
    public boolean recordMedicalTraceCode(MedicalTraceCodeRecordReqDto reqDto) {
        List<MedicalTraceCodeRecordReqDto.MedicalTraceCodeDetail> list = reqDto.getZhuiShuMaDetailList();
        if (CollectionUtils.isEmpty(list)) {
            return true;
        }
        Preconditions.checkArgument(0==reqDto.getType(), "仅支持拣货复核录入追溯码");
        for (MedicalTraceCodeDetail medicalTraceCodeDetail : list) {
            Preconditions.checkArgument(!StringUtils.isEmpty(medicalTraceCodeDetail.getEcode()), "追溯码不能为空");

        }
        for (MedicalTraceCodeDetail medicalTraceCodeDetail : list) {
            Preconditions.checkArgument(null!=medicalTraceCodeDetail.getOrderDetailId(), "订单明细ID不能为空");
            this.getBaseMapper().deleterByOrderDetailId(reqDto.getOrderNo(),medicalTraceCodeDetail.getOrderDetailId());
        }
        List<MedicalTraceCode> medicalTraceCodes = this.getBaseMapper()
            .queryOrderTraceCode(reqDto.getOrderNo());

        if(null!=medicalTraceCodes&&!medicalTraceCodes.isEmpty()){
            List<String> ecodeList = medicalTraceCodes.stream()
                .map(MedicalTraceCode::getEcode).collect(Collectors.toList());
            for (MedicalTraceCodeDetail medicalTraceCodeDetail : list) {
                if(medicalTraceCodeDetail.getEcode().equals("00000000000000000000")){
                    continue;
                }
                if(ecodeList.contains(medicalTraceCodeDetail.getEcode())){
                    throw ExceptionUtil.getWarnException(DsErrorType.ERP_SYS_ERROR.getCode(), "不支持录入重复追溯码");
                }

            }
        }

        List<MedicalTraceCode> collect = list.stream().map(d -> {
            MedicalTraceCode medicalTraceCode = new MedicalTraceCode();
            medicalTraceCode.setEcode(d.getEcode());
            medicalTraceCode.setCommodityBatchNo(d.getCommodityBatchNo());
            medicalTraceCode.setOrderDetailId(d.getOrderDetailId());
            medicalTraceCode.setRefundDetailId(d.getRefundDetailId());
            medicalTraceCode.setOrderNo(reqDto.getOrderNo());
            medicalTraceCode.setFlag(reqDto.getFlag());
            medicalTraceCode.setType(0);
            return medicalTraceCode;
        }).collect(Collectors.toList());
        this.saveBatch(collect);
        return true;
    }
    @Transactional
    @Override
    public boolean recordRefundMedicalTraceCode(AccountFailTraceCodeReqDto reqDto) {
        List<MedicalTraceCodeDetail> list=Lists.newArrayList();
        if(CollectionUtils.isEmpty(reqDto.getTraceCodeList())){
            return true;
        }
        reqDto.getTraceCodeList().forEach(o->list.addAll(o.getZhuiShuMaDetailList()));
        if(CollectionUtils.isEmpty(list)){
            return true;
        }
        MedicalTraceCodeRecordReqDto oneRecord =  reqDto.getTraceCodeList().get(0);
        Preconditions.checkArgument(1==oneRecord.getType(), "仅支持拣货复核录入追溯码");
        for (MedicalTraceCodeDetail medicalTraceCodeDetail : list) {
            Preconditions.checkArgument(!StringUtils.isEmpty(medicalTraceCodeDetail.getEcode()), "追溯码不能为空");

        }
        for (MedicalTraceCodeDetail medicalTraceCodeDetail : list) {
            Preconditions.checkArgument(null!=medicalTraceCodeDetail.getRefundDetailId(), "订单明细ID不能为空");
            this.getBaseMapper().deleterByRefundDetailId(oneRecord.getOrderNo(),medicalTraceCodeDetail.getRefundDetailId());
        }
        List<MedicalTraceCode> collect = list.stream().map(d -> {
            MedicalTraceCode medicalTraceCode = new MedicalTraceCode();
            medicalTraceCode.setEcode(d.getEcode());
            medicalTraceCode.setCommodityBatchNo(d.getCommodityBatchNo());
            medicalTraceCode.setOrderDetailId(d.getOrderDetailId());
            medicalTraceCode.setRefundDetailId(d.getRefundDetailId());
            medicalTraceCode.setOrderNo(oneRecord.getOrderNo());
            medicalTraceCode.setFlag(oneRecord.getFlag());
            medicalTraceCode.setType(1);
            return medicalTraceCode;
        }).collect(Collectors.toList());
        this.saveBatch(collect);
        return true;
    }


    @Override
    public List<MedicalTraceCode> queryTraceCodeByOrderErpCode(MedicalTraceCodeReqDto reqDto) {
        Preconditions.checkState(reqDto != null, "请求参数不能为空");
        Preconditions.checkState(reqDto.getOrderNo() != null, "请求参数不能为空");
        Preconditions.checkState(reqDto.getErpCode() != null, "请求参数不能为空");
        Preconditions.checkState(reqDto.getType() != null, "请求参数不能为空");
        List<MedicalTraceCode> results=Lists.newArrayList();
        if(0==reqDto.getType()){
            results = getBaseMapper().queryOrderTraceCode( reqDto.getOrderNo());
            if(null!=reqDto.getOrderDetailIds() ){
                results=results.stream().filter(medicalTraceCode -> reqDto.getOrderDetailIds().contains(medicalTraceCode.getOrderDetailId())).collect(Collectors.toList());
            }
            List<OrderDetail> orderDetails = orderDetailMapper.selectByOrderNoAndErpCode(
                reqDto.getOrderNo(), reqDto.getErpCode());
            List<Long> limitOrderDetailIdList = orderDetails.stream().map(OrderDetail::getId)
                .collect(Collectors.toList());
            results=results.stream().filter(o -> limitOrderDetailIdList.contains(o.getOrderDetailId())).collect(Collectors.toList());

            List<MedicalTraceCode> filterMedicalTraceCodes = getBaseMapper().queryRefundOrderTraceCode(reqDto.getOrderNo());
            if(null!=filterMedicalTraceCodes && !filterMedicalTraceCodes.isEmpty()){
                Set<String> collect = filterMedicalTraceCodes.stream().map(MedicalTraceCode::getEcode).collect(Collectors.toSet());
                results=results.stream().filter(medicalTraceCode -> !collect.contains(medicalTraceCode.getEcode())).collect(Collectors.toList());
            }

        }else {
            results = getBaseMapper().queryRefundOrderTraceCode(reqDto.getOrderNo());
            if(null!=reqDto.getRefundDetailId() ){
                results=results.stream().filter(medicalTraceCode -> reqDto.getRefundDetailId().equals(medicalTraceCode.getRefundDetailId())).collect(Collectors.toList());
            }
        }


        return results;

    }

    @Override
    public Boolean delete(MedicalTraceCodeIdReqDto medicalTraceCodeIdReqDto) {
        return this.remove(Wrappers.<MedicalTraceCode>lambdaUpdate().eq(MedicalTraceCode::getId, medicalTraceCodeIdReqDto.getId()));
    }

    @Override
    public MedicalTraceCodeBatchNoResDTO queryCommodityBatchNo(MedicalTraceCodeBatchCodeReqDto reqDto) {
        OrderInfo orderInfo = orderInfoMapper.selectOrderInfo(reqDto.getOrderNo());
        InnerStoreDictionary dictionary = innerStoreDictionaryMapper.selectOne(new QueryWrapper<InnerStoreDictionary>()
                .lambda().eq(InnerStoreDictionary::getOrganizationCode, orderInfo.getOrganizationCode()));
        O2OBatchNoRequest o2OBatchNoRequest = new O2OBatchNoRequest();
        o2OBatchNoRequest.setEcode(reqDto.getEcode());
        o2OBatchNoRequest.setErpCode(reqDto.getErpCode());
        o2OBatchNoRequest.setStoreCode(orderInfo.getOrganizationCode());
        ResponseBase<O2OBatchNoResponse> o2OBatchNoResponseResponseBase = posInterfaceClient.batchNo(o2OBatchNoRequest, dictionary);
        if (DsConstants.ERP_SUCCESS.equals(o2OBatchNoResponseResponseBase.getCode())) {
            O2OBatchNoResponse data = o2OBatchNoResponseResponseBase.getData();
            MedicalTraceCodeBatchNoResDTO medicalTraceCodeBatchNoResDTO = new MedicalTraceCodeBatchNoResDTO();
            medicalTraceCodeBatchNoResDTO.setEcode(reqDto.getEcode());
            medicalTraceCodeBatchNoResDTO.setCommodityBatchNo(data.getCommodityBatchNo());
            available(orderInfo.getOrganizationCode(), reqDto.getErpCode(), medicalTraceCodeBatchNoResDTO);

            return medicalTraceCodeBatchNoResDTO;
        }
        throw ExceptionUtil.getWarnException(DsErrorType.ERP_SYS_ERROR.getCode(), "追溯码查询批号错误");
    }

    @Override
    public MedicalTraceCodeBatchNoResDTO queryCommodityBatchNoByStoreCode(
        MedicalTraceCodeBatchCodeByStoreCodeReqDto reqDto) {
        InnerStoreDictionary dictionary = innerStoreDictionaryMapper.selectOne(new QueryWrapper<InnerStoreDictionary>()
            .lambda().eq(InnerStoreDictionary::getOrganizationCode, reqDto.getStoreCode()));
        O2OBatchNoRequest o2OBatchNoRequest = new O2OBatchNoRequest();
        o2OBatchNoRequest.setEcode(reqDto.getEcode());
        o2OBatchNoRequest.setErpCode(reqDto.getErpCode());
        o2OBatchNoRequest.setStoreCode(reqDto.getStoreCode());
        ResponseBase<O2OBatchNoResponse> o2OBatchNoResponseResponseBase = posInterfaceClient.batchNo(o2OBatchNoRequest, dictionary);
        if (DsConstants.ERP_SUCCESS.equals(o2OBatchNoResponseResponseBase.getCode())) {
            O2OBatchNoResponse data = o2OBatchNoResponseResponseBase.getData();
            MedicalTraceCodeBatchNoResDTO medicalTraceCodeBatchNoResDTO = new MedicalTraceCodeBatchNoResDTO();
            medicalTraceCodeBatchNoResDTO.setEcode(reqDto.getEcode());
            medicalTraceCodeBatchNoResDTO.setCommodityBatchNo(data.getCommodityBatchNo());
            available(reqDto.getStoreCode(), reqDto.getErpCode(), medicalTraceCodeBatchNoResDTO);

            return medicalTraceCodeBatchNoResDTO;
        }
        throw ExceptionUtil.getWarnException(DsErrorType.ERP_SYS_ERROR.getCode(), "追溯码查询批号错误");
    }

    @Override
    public MedicalTraceCodeOrderDetailIdResDTO queryOrderDetailIds(MedicalTraceCodeOrderDetailsReqDto medicalTraceCodeOrderDetailsReqDto) {
        List<OrderDetail> orderDetailList = orderDetailMapper.selectList(Wrappers.<OrderDetail>lambdaQuery()
                .select(OrderDetail::getId, OrderDetail::getRefundCount, OrderDetail::getCommodityCount)
                .eq(OrderDetail::getOrderNo, medicalTraceCodeOrderDetailsReqDto.getOrderNo())
                .eq(OrderDetail::getErpCode, medicalTraceCodeOrderDetailsReqDto.getErpCode()));
        MedicalTraceCodeOrderDetailIdResDTO medicalTraceCodeOrderDetailIdResDTO = new MedicalTraceCodeOrderDetailIdResDTO();
        List<Long> arr = new ArrayList<>();
        medicalTraceCodeOrderDetailIdResDTO.setOrderDetailIds(arr);
        for (OrderDetail orderDetail : orderDetailList) {
            Integer pickCount = orderDetail.getPickCount();
           while (pickCount >0) {
               arr.add(orderDetail.getId());
               pickCount--;
           }

        }
        log.info(" req :{} orderDetailIds:{}", JSON.toJSONString(medicalTraceCodeOrderDetailsReqDto),arr);
        return medicalTraceCodeOrderDetailIdResDTO;
    }

    @Override
    public List<MedicalTraceCodeRefundOrderDetailResDTO> queryRefundOrderDetails(MedicalTraceCodeRefundOrderDetailsReqDto medicalTraceCodeRefundOrderDetailsReqDto) {
        List<RefundDetail> refundDetails = refundDetailMapper.selectListByRefundNo(medicalTraceCodeRefundOrderDetailsReqDto.getRefundNo());
        if (CollectionUtils.isEmpty(refundDetails)) {
            return Collections.emptyList();
        }
        RefundOrder refundOrder = refundOrderMapper.selectByRefundNo(medicalTraceCodeRefundOrderDetailsReqDto.getRefundNo());
        List<OrderPickInfo> orderPickInfoListByDB = orderPickInfoMapper.selectPickInfoListByOrderNo(refundOrder.getOrderNo());
        List<MedicalTraceCode> refundTraceCodeList =  this.getBaseMapper().queryRefundOrderTraceCode(refundOrder.getOrderNo());
        Map<String, List<MedicalTraceCode>> refundTraceCodeMap = refundTraceCodeList.stream().collect(Collectors.groupingBy(o->o.getRefundDetailId().toString()));
        Map<String, List<OrderPickInfo>> orderPickInfoMap = orderPickInfoListByDB.stream().collect(Collectors.groupingBy(OrderPickInfo::getErpCode));

        List<MedicalTraceCodeRefundOrderDetailResDTO> resultList = new ArrayList<>();
        for (RefundDetail refundDetail : refundDetails) {
            MedicalTraceCodeRefundOrderDetailResDTO resDto = new MedicalTraceCodeRefundOrderDetailResDTO();
            resDto.setId(refundDetail.getId());
            resDto.setRefundNo(refundDetail.getRefundNo());
            resDto.setErpCode(refundDetail.getErpCode());
            resDto.setCommodityName(refundDetail.getCommodityName());
            resDto.setBillPrice(refundDetail.getBillPrice());
            resDto.setMedicalTraceCodeList(Lists.newArrayList());
            List<MedicalTraceCode> medicalTraceCodeList = refundTraceCodeMap.get(
                refundDetail.getId().toString());

            if(null!=medicalTraceCodeList&& !medicalTraceCodeList.isEmpty()){

                Integer refundCount = refundDetail.getRefundCount();
                for (int i = 0; i < refundCount; i++) {
                    MedicalTraceCodeRefundOrderDetailResDTO newResDto = new MedicalTraceCodeRefundOrderDetailResDTO();
                    BeanUtil.copyProperties(resDto, newResDto);
                    newResDto.setMedicalTraceCodeList(new ArrayList<>());
                    if(!medicalTraceCodeList.isEmpty()){
                        MedicalTraceCode medicalTraceCode = medicalTraceCodeList.get(0);
                        newResDto.getMedicalTraceCodeList().add(medicalTraceCode);
                        medicalTraceCodeList.remove(medicalTraceCode);
                        newResDto.setCommodityBatchNo(medicalTraceCode.getCommodityBatchNo());
                    }
                    newResDto.setRefundCount(1);
                    resultList.add(newResDto);
                }


            }else{
                List<OrderPickInfo> tempPickList = orderPickInfoMap.get(
                    refundDetail.getErpCode());
                if(tempPickList==null){
                    log.warn("追溯码查询退款明细 订单号：{} 退款erpCode:{}",refundOrder.getOrderNo(),refundDetail.getErpCode());
                }
                Integer allCountRemaining =refundDetail.getRefundCount();
                for (OrderPickInfo pickInfo : tempPickList) {
                    if(allCountRemaining<=0){
                        break;
                    }
                    MedicalTraceCodeRefundOrderDetailResDTO newResDto = new MedicalTraceCodeRefundOrderDetailResDTO();
                    BeanUtil.copyProperties(resDto, newResDto);
                    newResDto.setMedicalTraceCodeList(new ArrayList<>());
                    int nowTempGoodsCount;
                    int pickCount = pickInfo.getCount();
                    if (pickCount > allCountRemaining) {
                        nowTempGoodsCount = allCountRemaining;
                        allCountRemaining = 0;
                        pickInfo.setCount(pickCount - nowTempGoodsCount);
                    } else {
                        nowTempGoodsCount = pickCount;
                        allCountRemaining = allCountRemaining - pickCount;
                    }
                    newResDto.setRefundCount(nowTempGoodsCount);
                    newResDto.setCommodityBatchNo(pickInfo.getCommodityBatchNo());
                    resultList.add(newResDto);

                }


            }

          }
        return resultList;
    }



    @Override
    public List<String> queryBatchNoByErpCode(MedicalTraceCodeBatchNoReqDto dto) {
        StoreStockGetReqDTO storeStockGetReqDTO = StoreStockGetReqDTO.builder().erpCode(dto.getErpCode()).storeCode(dto.getStoreCode()).build();

        ErpRespReturnDto respReturnDto = middleDataSyncClient.getNoStockV2(storeStockGetReqDTO);
        List<String> resultList = Lists.newArrayList();
        if (ObjectUtil.isNotEmpty(respReturnDto)) {
            for (HanaSyncStockRespDTO resDto : respReturnDto.getData()) {
                if (ObjectUtil.isNotEmpty(resDto.getExpiryDays())) {
                    if (resDto.getExpiryDays() >= 30) {
                        resultList.add(resDto.getItemlotnum());
                    }
                    continue;
                }
                resultList.add(resDto.getItemlotnum());
            }
        }
        return resultList;
    }

    @Override
    public boolean recordCodeByAccountingFail(AccountFailTraceCodeReqDto reqDto) {
        log.info("正单下账失败追溯码 记录追溯码：{}", JSON.toJSONString(reqDto));

        Long orderNo = reqDto.getTraceCodeList().get(0).getOrderNo();
        OrderInfo orderInfo = orderInfoMapper.selectOrderInfo(orderNo);
        if (orderInfo == null) {
            throw ExceptionUtil.getWarnException(DsErrorType.PARAM_VALID_ERROR.getCode(), "订单不存在!");
        }

        // 校验需要拣货的数量是否和传参数量一致
//        List<MedicalTraceCode> traceCodeList = getBaseMapper().queryOrderTraceCode(orderNo);
//        if (CollUtil.isEmpty(traceCodeList)) {
//            throw ExceptionUtil.getWarnException(DsErrorType.PARAM_VALID_ERROR.getCode(), "订单不存在追溯码!");
//        }

        List<MedicalTraceCodeDetail> traceCodeList = Lists.newArrayList();
        reqDto.getTraceCodeList().stream().map(MedicalTraceCodeRecordReqDto::getZhuiShuMaDetailList).forEach(
                traceCodeList::addAll);

        Set<Long> orderDetailIds = traceCodeList.stream().map(
            MedicalTraceCodeDetail::getOrderDetailId).collect(Collectors.toSet());

        // 查询商品明细数量
        List<OrderDetail> orderDetails = orderDetailMapper.selectBatchIds(orderDetailIds);

        Integer pickCount = orderDetails.stream().map(OrderDetail::getPickCount).reduce(0, Integer::sum);

        List<MedicalTraceCodeRecordReqDto.MedicalTraceCodeDetail> detailList = Lists.newArrayList();

        reqDto.getTraceCodeList().forEach(o -> {
            o.getZhuiShuMaDetailList().forEach( x -> x.setFlag(o.getFlag()));
            detailList.addAll(o.getZhuiShuMaDetailList());
        });

        if (pickCount != detailList.size()) {
            throw ExceptionUtil.getWarnException(DsErrorType.PARAM_VALID_ERROR.getCode(), "录入的追溯码与需要拣货的追溯码数量不一致!");
        }

        getBaseMapper().delete(new LambdaQueryWrapper<MedicalTraceCode>().eq(MedicalTraceCode::getOrderNo,orderNo).eq(MedicalTraceCode::getType,DsConstants.INTEGER_ZERO));

        if (CollectionUtils.isEmpty(detailList)) {
            return true;
        }

        List<MedicalTraceCode> collect = detailList.stream().map(d -> {
            MedicalTraceCode medicalTraceCode = new MedicalTraceCode();
            medicalTraceCode.setEcode(d.getEcode());
            medicalTraceCode.setCommodityBatchNo(d.getCommodityBatchNo());
            medicalTraceCode.setOrderDetailId(d.getOrderDetailId());
            medicalTraceCode.setRefundDetailId(d.getRefundDetailId());
            medicalTraceCode.setOrderNo(orderNo);
            medicalTraceCode.setFlag(d.getFlag());
            medicalTraceCode.setType(0);
            return medicalTraceCode;
        }).collect(Collectors.toList());
        this.saveBatch(collect);
        return true;
    }

    @Override
    public boolean recordByRefundReview(AccountFailTraceCodeReqDto reqDto) {

        MedicalTraceCodeRecordReqDto medicalTraceCodeRecordReqDto = reqDto.getTraceCodeList()
            .get(0);
        Long orderNo = medicalTraceCodeRecordReqDto.getOrderNo();

        OrderInfo orderInfo = orderInfoMapper.selectOrderInfo(orderNo);
        DsOnlineStoreConfig storeConfig = dsOnlineStoreConfigRepo.getStoreConfig(orderInfo.getMerCode(), orderInfo.getThirdPlatformCode(),
            orderInfo.getClientCode(), orderInfo.getOnlineStoreCode());

        //没有开启配置追溯码不需要配置
        if (DsConstants.INTEGER_ZERO.equals(storeConfig.getZhuiShuMa())) {
            log.info("该门店未开启追溯码配置，不需要配置追溯码"+JSON.toJSONString(reqDto));
            return true;
        }
        List<MedicalTraceCodeRecordReqDto.MedicalTraceCodeDetail> detailList = Lists.newArrayList();
        reqDto.getTraceCodeList().forEach(o -> {
            o.getZhuiShuMaDetailList().forEach( x -> x.setFlag(o.getFlag()));
            detailList.addAll(o.getZhuiShuMaDetailList());
        });

        // 获取所有追溯码
        List<String> ecodes = detailList.stream().filter(Objects::nonNull).map(MedicalTraceCodeRecordReqDto.MedicalTraceCodeDetail::getEcode).collect(Collectors.toList());
        List<MedicalTraceCode> traceCodeList = getBaseMapper().queryRefundOrderTraceCode(orderNo);
        Map<String, MedicalTraceCode> traceCodeMap = traceCodeList.stream()
            .collect(Collectors.toMap(MedicalTraceCode::getEcode, o -> o));
        for (String ecode : ecodes) {
            if (traceCodeMap.containsKey(ecode)) {
                throw ExceptionUtil.getWarnException(DsErrorType.PARAM_VALID_ERROR.getCode(), "追溯码已存在!"+ecode);
            }
        }

        List<MedicalTraceCode> collect = detailList.stream().map(d -> {
            MedicalTraceCode medicalTraceCode = new MedicalTraceCode();
            medicalTraceCode.setEcode(d.getEcode());
            medicalTraceCode.setCommodityBatchNo(d.getCommodityBatchNo());
            medicalTraceCode.setOrderDetailId(d.getOrderDetailId());
            medicalTraceCode.setRefundDetailId(d.getRefundDetailId());
            medicalTraceCode.setOrderNo(medicalTraceCodeRecordReqDto.getOrderNo());
            medicalTraceCode.setFlag(medicalTraceCodeRecordReqDto.getFlag());
            medicalTraceCode.setType(medicalTraceCodeRecordReqDto.getType());
            return medicalTraceCode;
        }).collect(Collectors.toList());
        this.saveBatch(collect);
        return true;
    }

    @Override
    public MedicalTraceCodeDTOCheckResDTO needRecordByRefundAccountFail(MedicalTraceCodeCheckReqDto medicalTraceCodeCheckReqDto) {
        OrderInfo orderInfo = orderInfoMapper.selectOrderInfo(Long.parseLong(medicalTraceCodeCheckReqDto.getOrderNo()));
        DsOnlineStoreConfig storeConfig = dsOnlineStoreConfigRepo.getStoreConfig(orderInfo.getMerCode(), orderInfo.getThirdPlatformCode(),
                orderInfo.getClientCode(), orderInfo.getOnlineStoreCode());
        MedicalTraceCodeDTOCheckResDTO dto = new MedicalTraceCodeDTOCheckResDTO();
        //没有开启配置追溯码不需要配置
        if (DsConstants.INTEGER_ZERO.equals(storeConfig.getZhuiShuMa())) {
            return dto;
        }
        //订单明细为空不需要配置
        List<OrderDetail> orderDetailList = orderDetailMapper.selectAllListByOrderNo(Long.parseLong(medicalTraceCodeCheckReqDto.getOrderNo()));
        if (CollectionUtils.isEmpty(orderDetailList)) {
            return dto;
        }
        //如果是逆单，正单追溯码为空时不需要录入
        if (medicalTraceCodeCheckReqDto.getType().compareTo(DsConstants.INTEGER_ONE) == 0) {
            Integer count = getBaseMapper().queryOrderTraceCodeCount(Long.parseLong(medicalTraceCodeCheckReqDto.getOrderNo()));
            if (DsConstants.INTEGER_ZERO.compareTo(count) == 0) {
                return dto;
            }

            Integer refundCount = refundOrderMapper.selectCount(Wrappers.<RefundOrder>lambdaQuery()
                    .eq(RefundOrder::getRefundNo, medicalTraceCodeCheckReqDto.getRefundNo())
                    //全额退款
                    .eq(RefundOrder::getType, DsConstants.STRING_ONE));
            if (refundCount == 1) {
                return dto;
            }
        }
        //全额退款不需要录入追溯码

        Set<String> collect = orderDetailList.stream().map(OrderDetail::getErpCode).collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(medicalTraceCodeCheckReqDto.getErpCodeList())) {
            collect.addAll(medicalTraceCodeCheckReqDto.getErpCodeList());
        }
        CheckScanTraceCodeReqDto checkScanTraceCodeReqDto = new CheckScanTraceCodeReqDto();
        checkScanTraceCodeReqDto.setErpCodeList(collect);
        checkScanTraceCodeReqDto.setOrderNo(medicalTraceCodeCheckReqDto.getOrderNo());
        checkScanTraceCodeReqDto.setStoreCode(orderInfo.getOrganizationCode());
        ResponseBase<CheckScanTraceCodeRespDto> checkScanTraceCodeRespDtoResponseBase = middleMerchandiseClient.checkScanTraceCode(checkScanTraceCodeReqDto);
        CheckScanTraceCodeRespDto data = checkScanTraceCodeRespDtoResponseBase.getData();
        if (null!=data&&!CollectionUtils.isEmpty(data.getErpCodeWithFlagList())) {
            List<GoodMedicalTraceCodeDTO> collect1 = data.getErpCodeWithFlagList().stream().filter(d -> d.getFlag() != null).collect(Collectors.toList());
            dto.setNeedRecords(collect1);
        }
        return dto;
    }

    @Override
    public ResponseBase<Void> updateOrderPickInfoByRefund(String userId, String merCode, RefundUpdateTraceCodeReqDto requestParam) {
//        ResponseBase<Void> res = ResponseBase.success();
//        log.info("updateOrderPickInfoByRefund requestParam:{}", JSON.toJSONString(requestParam));
//        try {
//            ResponseBase<SysEmployeeResDTO> employeeByUserId = middleBaseInfoClient.getEmployeeByUserId(
//                    userId);
//            if (null == employeeByUserId || !employeeByUserId.checkSuccess()
//                    || null == employeeByUserId.getData()) {
//                return ResponseBase.error(ErrorType.PARA_ERROR.getCode(), "未找到用户信息,请与管理员确认.");
//            }
//            Long refundNo = Long.valueOf(requestParam.getRefundNo());
//            OrderInfo orderInfo = orderInfoMapper.selectOrderInfo(orderNo);
//            if (null == orderInfo) {
//                return ResponseBase.error(ErrorType.PARA_ERROR.getCode(),
//                        "未找到对应订单，请用系统订单号查询.");
//            }
//            if (orderInfo.getErpState() != ErpStateEnum.HAS_SALE_FAIL.getCode().intValue()) {
//                return ResponseBase.error(ErrorType.PARA_ERROR.getCode(), "仅支持下账失败的订单修改.");
//            }
//            for (CorrectionToolCmdOrderDetailInfo correctionToolCmdOrderDetailInfo : requestParam.getOrderDetailInfoList()) {
//                if (org.apache.commons.lang3.StringUtils.isBlank(correctionToolCmdOrderDetailInfo.getCommodityBatchNo())) {
//                    return ResponseBase.error(ErrorType.PARA_ERROR.getCode(),
//                            "未填写商品批号,请填写后,再次提交.");
//                }
//            }
//            List<OrderPickInfo> orderPickInfoList = orderPickInfoMapper.selectPickInfoListByOrderNo(
//                    orderNo);
//            OrderInfo updateOrderInfo = new OrderInfo();
//            updateOrderInfo.setOrderNo(orderInfo.getOrderNo());
//            updateOrderInfo.setErpState(ErpStateEnum.WAIT_SALE.getCode());
//            updateOrderInfo.setDataVersion(orderInfo.getDataVersion() + 1);
//
//            List<OrderPickInfoReqDto> insertOrderPickInfoList = com.google.common.collect.Lists.newArrayList();
//            for (CorrectionToolCmdOrderDetailInfo newOrderDetailInfo : requestParam.getOrderDetailInfoList()) {
//
//                OrderPickInfoReqDto orderPickInfo = new OrderPickInfoReqDto();
//                orderPickInfo.setOrderDetailId(newOrderDetailInfo.getId());
//                orderPickInfo.setErpCode(newOrderDetailInfo.getErpCode());
//                orderPickInfo.setCommodityBatchNo(newOrderDetailInfo.getCommodityBatchNo());
//                orderPickInfo.setCount(newOrderDetailInfo.getCommodityCount());
//                insertOrderPickInfoList.add(orderPickInfo);
//            }
//            if (openPickMerge) {
//                insertOrderPickInfoList = orderOperateManager.splitOrderPickInfoV2(insertOrderPickInfoList);
//            }
//
//            final List<OrderPickInfoReqDto> finalInsertOrderPickInfoList = insertOrderPickInfoList;
//            transactionTemplate.execute(status -> {
//                int updateOrderNum = orderInfoMapper.update(updateOrderInfo,
//                        new QueryWrapper<OrderInfo>().lambda()
//                                .eq(OrderInfo::getOrderNo, updateOrderInfo.getOrderNo())
//                                .eq(OrderInfo::getDataVersion, orderInfo.getDataVersion())
//                                .eq(OrderInfo::getLockFlag, OrderLockFlagEnum.NOT_LOCK.getCode())
//                                .eq(OrderInfo::getErpState, ErpStateEnum.HAS_SALE_FAIL.getCode()));
//                Preconditions.checkArgument(updateOrderNum > 0, "订单已被锁定，请稍后再试");
//                orderPickInfoMapper.deleteAllPickByOrderNo(orderNo);
//                for (OrderPickInfoReqDto orderPickInfo : finalInsertOrderPickInfoList) {
//                    OrderPickInfo pickInfo = new OrderPickInfo();
//                    BeanUtils.copyProperties(orderPickInfo, pickInfo);
//                    pickInfo.setIsValid(1);
//                    pickInfo.setCreateTime(new Date());
//                    int insertOrderPickInfoNum = orderPickInfoMapper.insert(pickInfo);
//                    Preconditions.checkArgument(insertOrderPickInfoNum > 0,
//                            "新增明细拣货信息失败，请稍后再试");
//                }
//                return true;
//            });
//
//            StringBuilder beforeStr = new StringBuilder();
//            beforeStr.append("修改订单明细，修改前：\n");
//            if (null != orderPickInfoList && !orderPickInfoList.isEmpty()) {
//                for (OrderPickInfo orderPickInfo : orderPickInfoList) {
//                    beforeStr.append("商品编码：").append(orderPickInfo.getErpCode()).append("，批次号：")
//                            .append(orderPickInfo.getCommodityBatchNo()).append("，商品数量：")
//                            .append(orderPickInfo.getCount()).append("；");
//                }
//            }
//            StringBuilder afterStr = new StringBuilder();
//            afterStr.append("修改订单明细，修改后：\n");
//            for (CorrectionToolCmdOrderDetailInfo detail : requestParam.getOrderDetailInfoList()) {
//                afterStr.append("商品编码：").append(detail.getErpCode()).append("，批次号：")
//                        .append(detail.getCommodityBatchNo()).append("，商品数量：")
//                        .append(detail.getCommodityCount()).append("；");
//            }
//            String changeMsg = String.format(OrderLogEnum.CORRECTION_TOOL_ORDER_PICK.getInfo(),
//                    requestParam.getOrderInfo().getOrderNo(), beforeStr, afterStr);
//            HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, orderInfo.getMerCode(),
//                    orderInfo.getOrderNo(), orderInfo.getOrderState(), ErpStateEnum.WAIT_SALE.getCode(),
//                    OrderLogEnum.CORRECTION_TOOL_ORDER_PICK.getAction(), changeMsg,
//                    employeeByUserId.getData());
//            //发送到HdPos重新下账-走异步
//            afreshHdPosAccounting(orderNo.toString());
//            return res;
//
//        } catch (Exception e) {
//            log.error("修改订单拣货信息异常，{}", requestParam.getOrderInfo().getOrderNo(), e);
//            return ResponseBase.error(DsErrorType.ERP_SYS_ERROR.getCode(),
//                    "修改订单拣货信息异常,请稍后再试");
//        }
        return null;
    }

    private void available(String storeCode,String erpCode, MedicalTraceCodeBatchNoResDTO medicalTraceCodeBatchNoResDTO) {
        StoreErpBatchInfoReq storeErpBatchInfoReq = new StoreErpBatchInfoReq();
        storeErpBatchInfoReq.setStoreCode(storeCode);
        storeErpBatchInfoReq.setErpCode(erpCode);
        storeErpBatchInfoReq.setLotCodeList(Arrays.asList(medicalTraceCodeBatchNoResDTO.getCommodityBatchNo()));
        if (!isCheckBatchNo) {
            medicalTraceCodeBatchNoResDTO.setAvailable(true);
            return;
        }
        ErpRespReturnDto noStockV3 = middleDataSyncClient.getNoStockV3(storeErpBatchInfoReq);
        List<HanaSyncStockRespDTO> data = noStockV3.getData();
        if (CollectionUtils.isEmpty(data)) {
            throw ExceptionUtil.getWarnException(DsErrorType.ERP_SYS_ERROR.getCode(), "追溯码未找到对应的批号,请检查追溯码或手动选择批号");
        }
        HanaSyncStockRespDTO hanaSyncStockRespDTO = data.get(0);
        if (!isCheckBatchNoNewCheckNull && null == hanaSyncStockRespDTO.getExpiryDays()) {
            medicalTraceCodeBatchNoResDTO.setAvailable(Boolean.TRUE);
        } else {
            medicalTraceCodeBatchNoResDTO.setAvailable(ObjectUtils.compare(hanaSyncStockRespDTO.getExpiryDays(), maxAvailableDay) >= 0);
        }
    }
}
