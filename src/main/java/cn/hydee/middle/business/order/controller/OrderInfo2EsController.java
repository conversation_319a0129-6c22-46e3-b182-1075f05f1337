package cn.hydee.middle.business.order.controller;

import cn.hydee.middle.business.order.service.OrderInfoEsService;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 线上订单查询es优化控制类
 * @Author: chufeng(2910)
 * @Date: 2021/10/21 14:13
 */
@RestController
@RequestMapping("/${api.version}/ds/orderInfoes")
@Api(tags = "订单与es交互接口")
@Slf4j
public class OrderInfo2EsController extends AbstractController {

    @Autowired
    private OrderInfoEsService orderInfoEsService;

    @ApiOperation(value = "定时任务同步数据到ES", notes = "定时任务同步数据到ES,每次同步2000条数据")
    @PostMapping("/syncOrder2Es")
    public ResponseBase<String> syncOrder2Es(){
        orderInfoEsService.syncOrderInfo2Es();
        return generateSuccess(null);
    }

    @ApiOperation(value = "创建index", notes = "创建index")
    @PostMapping("/createIndex")
    public ResponseBase<String> createIndex(@RequestParam("indexName") String indexName){
        orderInfoEsService.createIndex(indexName);
        return generateSuccess(null);
    }

}

