package cn.hydee.middle.business.order.yxtadapter.domain.pos.hdpos;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/12/21
 * @since 1.0
 */
@Data
public class PayTypeDto {

    @ApiModelProperty("支付编码-对应ERP支付编码")
    @NotNull(message="支付编码不为空")
    private String payCode;

    @ApiModelProperty("支付类型")
    @NotNull(message="支付类型不为空")
    private String payName;

    @ApiModelProperty("支付类型对应的金额")
    @NotNull(message="支付类型对应的金额不为空")
    private BigDecimal payPrice;


}
