package cn.hydee.middle.business.order.dto.rsp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 账户类
 * <AUTHOR>
 * @version 1.0
 * @date 2019/7/22 15:32
 */
@Data
public class AccountMainDTO {

    @ApiModelProperty(value = "唯一标识")
    private String id;

    @ApiModelProperty(value = "商户编码")
    private String merCode;

    @ApiModelProperty(value = "用户编码")
    private String userCode;

    @ApiModelProperty(value = "账户")
    private String account;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "头像")
    private String avatarPath;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "邮箱")
    private String mail;

    @ApiModelProperty(value = "类型")
    private Integer userType;

    private Integer admin;

    @ApiModelProperty(value = "状态")
    private Integer userStatus;
}