package cn.hydee.middle.business.order.dto.req.baseinfo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;


@Data
public class UpdateStoreStatusResDto {
    @ApiModelProperty("请求返回码，10000-成功，其他失败")
    private String code;

    @ApiModelProperty("请求返回信息")
    private String msg;

    @ApiModelProperty(value = "修改门店运营状态失败 错误消息集合；每一个平台记录一条，样式 商户编码:门店编码:平台编码:错误消息, merCode:stdCode:platCode:msg ; 没有失败时为空集合")
    private List<String> errorMsgs = new ArrayList<>();

    public void addErrorMsgs(String errorMsg){
        errorMsgs.add(errorMsg);
    }
}
