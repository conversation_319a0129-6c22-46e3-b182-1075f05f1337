package cn.hydee.middle.business.order.dto.template;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/08/25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PrintPropertyStyle {
    @ApiModelProperty(value = "是否条形码")
    private boolean showBarCode;
    @ApiModelProperty(value = "是否分割线")
    private boolean showDivider;
    @ApiModelProperty(value = "是否裁剪线")
    private boolean showCutting;
    @ApiModelProperty(value = "是否显示propertyName")
    private boolean showName;
    @ApiModelProperty(value = "是否被星包围")
    private boolean showStar;
    @ApiModelProperty(value = "是否加粗")
    private boolean fontBold;
    @ApiModelProperty(value = "是否平均")
    private boolean average;
    @ApiModelProperty(value = "字号可选项：12 14 18 24 32 默认：14")
    private Integer fontSize;
    @ApiModelProperty(value = "位置")
    private Integer position;
    @ApiModelProperty(value = "哪一行 一行显示多个时,数值相同")
    private Integer rowNumber;
    @ApiModelProperty(value = "间距 这个间距只是上下间距")
    private Integer padding;

    private static PrintPropertyStyle buildDefaultBean(){
        return PrintPropertyStyle.builder()
                .showBarCode(false)
                .showDivider(false)
                .showCutting(false)
                .showName(true)
                .showStar(false)
                .fontBold(false)
                .average(true)
                .fontSize(8)
                .position(0)
                .padding(2)
                .build();
    }

    public PrintPropertyStyle buildBeanShowStar(){
        this.setShowStar(true);
        return this;
    }

    public PrintPropertyStyle buildBeanFontBold(){
        this.setFontBold(true);
        return this;
    }

    public PrintPropertyStyle buildBeanNotAverage(){
        this.setAverage(false);
        return this;
    }

    public PrintPropertyStyle buildBeanFontSize(Integer fontSize){
        this.setFontSize(fontSize);
        return this;
    }

    public PrintPropertyStyle buildBeanPosition(Integer position){
        this.setPosition(position);
        return this;
    }

    public PrintPropertyStyle buildBeanPadding(Integer padding){
        this.setPadding(padding);
        return this;
    }

    public static PrintPropertyStyle buildBean(Integer rowNumber){
        PrintPropertyStyle prIntegerPropertyStyle = buildDefaultBean();
        prIntegerPropertyStyle.setRowNumber(rowNumber);
        return prIntegerPropertyStyle;
    }

    public static PrintPropertyStyle buildBeanShowBarCode(Integer rowNumber){
        PrintPropertyStyle prIntegerPropertyStyle = buildBean(rowNumber);
        prIntegerPropertyStyle.setShowBarCode(true);
        prIntegerPropertyStyle.setShowName(false);
        return prIntegerPropertyStyle;
    }

    public static PrintPropertyStyle buildBeanShowDivider(Integer rowNumber){
        PrintPropertyStyle prIntegerPropertyStyle = buildBean(rowNumber);
        prIntegerPropertyStyle.setShowDivider(true);
        prIntegerPropertyStyle.setShowName(false);
        return prIntegerPropertyStyle;
    }

    public static PrintPropertyStyle buildBeanShowCutting(Integer rowNumber){
        PrintPropertyStyle prIntegerPropertyStyle = buildBean(rowNumber);
        prIntegerPropertyStyle.setShowCutting(true);
        prIntegerPropertyStyle.setShowName(false);
        return prIntegerPropertyStyle;
    }

    public static PrintPropertyStyle buildBeanShowName(Integer rowNumber,boolean showName){
        PrintPropertyStyle prIntegerPropertyStyle = buildBean(rowNumber);
        prIntegerPropertyStyle.setShowName(showName);
        prIntegerPropertyStyle.setShowName(false);
        return prIntegerPropertyStyle;
    }

}
