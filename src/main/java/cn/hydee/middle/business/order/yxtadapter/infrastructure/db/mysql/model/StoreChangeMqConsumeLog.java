package cn.hydee.middle.business.order.yxtadapter.infrastructure.db.mysql.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
@TableName("store_change_mq_consume_log")
public class StoreChangeMqConsumeLog implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("message_id")
    private String messageId;

    @TableField("message_tags")
    private String messageTags;

    @TableField("message_topic")
    private String messageTopic;

    @TableField("store_code")
    private String storeCode;

    @TableField("message_body")
    private String messageBody;

    @TableField("exception_info")
    private String exceptionInfo;

    @TableField("create_time")
    private Date createTime;

    @TableField("modify_time")
    private Date modifyTime;


}
