/*  
 * Project Name:hydee-business-order  
 * File Name:YdjiaSrmDeliveryClient.java  
 * Package Name:cn.hydee.middle.business.order.feign  
 * Date:2020年10月22日上午10:26:12  
 * Copyright (c) 2020, Shanghai Hydee Software Corp., Ltd. All Rights Reserved.  
 *  
*/  
  
package cn.hydee.middle.business.order.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import cn.hydee.middle.business.order.dto.req.ob.cloud.shelf.CancelDeliverGoodsDto;
import cn.hydee.middle.business.order.dto.req.ob.cloud.shelf.RequestDeliverGoodsDto;
import cn.hydee.starter.dto.ResponseBase;

/**
 * 
 * 商品供货商接口
 *
 */
@FeignClient(value = "ydjia-srm-delivery")
public interface YdjiaSrmDeliveryClient {
	
    /**
     * 代发商品申请供货商发货
     * @param requestDeliverGoodsDto
     * @return Boolean
     */
    @PostMapping("/1.0/delivery/insert")
    ResponseBase<Boolean> requestDeliverGoods(@RequestBody RequestDeliverGoodsDto requestDeliverGoodsDto);
    
    /**
     * OMS取消供货商发货
     * @param cancelDeliverGoodsDto
     * @return Boolean
     */
    @PostMapping("/1.0/delivery/cancel")
    ResponseBase<Boolean> cancelDeliverGoods(@RequestBody CancelDeliverGoodsDto cancelDeliverGoodsDto);
}
  
