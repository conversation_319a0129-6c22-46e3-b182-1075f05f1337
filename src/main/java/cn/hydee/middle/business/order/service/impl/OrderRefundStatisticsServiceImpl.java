package cn.hydee.middle.business.order.service.impl;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.Enums.RefundErpStateEnum;
import cn.hydee.middle.business.order.account.check.batch.list.BatchListHandler;
import cn.hydee.middle.business.order.doris.dto.req.PushMessage2KafkaReqDto;
import cn.hydee.middle.business.order.doris.service.OrderDorisService;
import cn.hydee.middle.business.order.dto.order.refund.statistics.CalculateRefundStatisticsDTO;
import cn.hydee.middle.business.order.entity.OrderRefundStatistics;
import cn.hydee.middle.business.order.entity.RefundOrder;
import cn.hydee.middle.business.order.mapper.OrderRefundStatisticsMapper;
import cn.hydee.middle.business.order.mapper.RefundOrderMapper;
import cn.hydee.middle.business.order.service.OrderRefundBillStatisticsService;
import cn.hydee.middle.business.order.service.OrderRefundStatisticsService;
import cn.hydee.middle.business.order.v2.manager.CommodityHandlerManager;
import cn.hydee.starter.util.UUIDUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>
 * 订单退款预处理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-26
 */
@Service
@Slf4j
public class OrderRefundStatisticsServiceImpl extends ServiceImpl<OrderRefundStatisticsMapper, OrderRefundStatistics> implements OrderRefundStatisticsService {

    @Autowired
    private OrderRefundStatisticsMapper orderRefundStatisticsMapper;
    @Autowired
    private RefundOrderMapper refundOrderMapper;
    @Autowired
    private OrderRefundBillStatisticsService orderRefundBillStatisticsService;
    @Autowired
    private OrderDorisService orderDorisService;
    @Autowired
    private CommodityHandlerManager commodityHandlerManager;

    @Override
    @Async("orderPreStatisticsExecutor")
    public void calculateHistoryDataByTime(CalculateRefundStatisticsDTO dto){
        String jobId = UUIDUtil.generateUuid();
        log.info("[{}] hand calculateHistoryDataByTime start ===> param:{}",jobId,dto);
        QueryWrapper<RefundOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().ge(RefundOrder::getCreateTime,dto.getStartTime()).le(RefundOrder::getCreateTime,dto.getEndTime())
                .eq(RefundOrder::getErpState, RefundErpStateEnum.HAS_REFUND.getCode());
        List<RefundOrder> refundOrderList = refundOrderMapper.selectList(queryWrapper);
        if(CollectionUtils.isEmpty(refundOrderList)){
            log.info("[{}] hand calculateHistoryDataByTime start ===> no order data",jobId);
            return;
        }
        AtomicInteger calculateNum = new AtomicInteger();
        new BatchListHandler<RefundOrder>().batchResolve(refundOrderList, 1000, (items) -> {{
            calculateNum.addAndGet(statisticsHasSaleRefundOrderRefundAmount(items));
            calculateNum.addAndGet(orderRefundBillStatisticsService.calculateBillRefundOrderRefundAmountByOrderNo(
                    items.stream().map(RefundOrder::getOrderNo).collect(Collectors.toList())));
            items.forEach(refundOrder -> {
                commodityHandlerManager.saveCommodityAveragePriceAfterRefundBill(refundOrder.getOrderNo());
            });
            // 订单变动，同步变动到doris kafka
            orderDorisService.pushMessage2Kafka(PushMessage2KafkaReqDto.builder().orderNoList(
                    items.stream().map(RefundOrder::getOrderNo).collect(Collectors.toList())).build());
        }});
        log.info("[{}] hand calculateHistoryDataByTime end ===> calculateNum:{}",jobId,calculateNum);
    }

    @Override
    @Async("orderPreStatisticsExecutor")
    public void calculateHistoryMerCodeTime(CalculateRefundStatisticsDTO dto){
        String jobId = UUIDUtil.generateUuid();
        log.info("[{}] hand calculateHistoryMerCodeTime start ===> param:{}",jobId,dto);
        QueryWrapper<RefundOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().ge(RefundOrder::getCreateTime,dto.getStartTime()).le(RefundOrder::getCreateTime,dto.getEndTime())
                .eq(RefundOrder::getMerCode,dto.getMerCode())
                .eq(RefundOrder::getErpState, RefundErpStateEnum.HAS_REFUND.getCode());
        List<RefundOrder> refundOrderList = refundOrderMapper.selectList(queryWrapper);
        if(CollectionUtils.isEmpty(refundOrderList)){
            log.info("[{}] hand calculateHistoryMerCodeTime start ===> no order data",jobId);
            return;
        }
        AtomicInteger calculateNum = new AtomicInteger();
        new BatchListHandler<RefundOrder>().batchResolve(refundOrderList, 1000, (items) -> {{
            calculateNum.addAndGet(statisticsHasSaleRefundOrderRefundAmount(items));
            calculateNum.addAndGet(orderRefundBillStatisticsService.calculateBillRefundOrderRefundAmountByOrderNo(
                    items.stream().map(RefundOrder::getOrderNo).collect(Collectors.toList())));
            items.forEach(refundOrder -> {
                commodityHandlerManager.saveCommodityAveragePriceAfterRefundBill(refundOrder.getOrderNo());
            });
            // 订单变动，同步变动到doris kafka
            orderDorisService.pushMessage2Kafka(PushMessage2KafkaReqDto.builder().orderNoList(
                    items.stream().map(RefundOrder::getOrderNo).collect(Collectors.toList())).build());
        }});
        log.info("[{}] hand calculateHistoryMerCodeTime end ===> calculateNum:{}",jobId,calculateNum);
    }

    @Override
    public int calculateHasSaleRefundOrderRefundAmountByOrderNo(Long orderNo){
        int result = DsConstants.INTEGER_ZERO;
        try {
            QueryWrapper<RefundOrder> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(RefundOrder::getOrderNo,orderNo)
                    .eq(RefundOrder::getErpState, RefundErpStateEnum.HAS_REFUND.getCode());
            List<RefundOrder> refundOrderList = refundOrderMapper.selectList(queryWrapper);
            if(CollectionUtils.isEmpty(refundOrderList)){
                return DsConstants.INTEGER_ZERO;
            }
            result = statisticsHasSaleRefundOrderRefundAmount(refundOrderList);
            //【*********】【优化】按商品明细保存商品成本
            orderRefundBillStatisticsService.calculateBillRefundOrderRefundAmountByOrderNo(Collections.singletonList(orderNo));
            commodityHandlerManager.saveCommodityAveragePriceAfterRefundBill(orderNo);

            // 订单变动，同步变动到doris kafka
            orderDorisService.pushMessage2Kafka(PushMessage2KafkaReqDto.builder().orderNoList(Collections.singletonList(orderNo)).build());
        } catch (Exception e) {
            log.info("calculateHasSaleRefundOrderRefundAmountByOrderNo param:[{}],err:{}",orderNo,e);
        }
        return result;
    }

    /**
    * @Description: 统计订单已下账的退款单金额数据
    * @Param: [refundOrderList]
    * @return: int
    * @Author: syuson
    * @Date: 2021-4-26
    */
    public int statisticsHasSaleRefundOrderRefundAmount(List<RefundOrder> refundOrderList){
        // 统计已下账退款单财务数据
        if(CollectionUtils.isEmpty(refundOrderList)){
            return DsConstants.INTEGER_ZERO;
        }
        List<OrderRefundStatistics> dataList = refundOrderList.stream().collect(Collectors.toMap(RefundOrder::getOrderNo,r->r,(r1,r2)->calculateRefundOrder(r1,r2)))
                .values().stream().map(refundOrder -> {
                    OrderRefundStatistics orderRefundStatistics = new OrderRefundStatistics();
                    BeanUtils.copyProperties(refundOrder,orderRefundStatistics);
                    return orderRefundStatistics;
                }).collect(Collectors.toList());
        // 更新或录入数据
        this.saveUpdateBatch(dataList);
        return dataList.size();
    }

    /**
    * @Description: 多次退款金额汇总
    * @Param: [r1, r2]
    * @return: cn.hydee.middle.business.order.entity.RefundOrder
    * @Author: syuson
    * @Date: 2021-4-26
    */
    private RefundOrder calculateRefundOrder(RefundOrder r1, RefundOrder r2) {
        r1.setId(null);
        r1.setCreateTime(null);
        r1.setModifyTime(null);
        // 退款商品总金额
        r1.setTotalFoodAmount(convertSafeValue(r1.getTotalFoodAmount()).add(convertSafeValue(r2.getTotalFoodAmount())));
        // 退商家配送费
        r1.setMerchantRefundPostFee(convertSafeValue(r1.getMerchantRefundPostFee()).add(convertSafeValue(r2.getMerchantRefundPostFee())));
        // 退商家包装费
        r1.setMerchantRefundPackFee(convertSafeValue(r1.getMerchantRefundPackFee()).add(convertSafeValue(r2.getMerchantRefundPackFee())));
        // 退平台配送费
        r1.setPlatformRefundDeliveryFee(convertSafeValue(r1.getPlatformRefundDeliveryFee()).add(convertSafeValue(r2.getPlatformRefundDeliveryFee())));
        // 退平台包装费
        r1.setPlatformRefundPackFee(convertSafeValue(r1.getPlatformRefundPackFee()).add(convertSafeValue(r2.getPlatformRefundPackFee())));
        // 商家退款总金额
        r1.setShopRefund(convertSafeValue(r1.getShopRefund()).add(convertSafeValue(r2.getShopRefund())));
        // 退买家总金额
        r1.setConsumerRefund(convertSafeValue(r1.getConsumerRefund()).add(convertSafeValue(r2.getConsumerRefund())));
        // 退还商家优惠
        r1.setShopDiscountRefund(convertSafeValue(r1.getShopDiscountRefund()).add(convertSafeValue(r2.getShopDiscountRefund())));
        // 退还佣金
        r1.setFeeRefund(convertSafeValue(r1.getFeeRefund()).add(convertSafeValue(r2.getFeeRefund())));
        // 退商品明细优惠
        r1.setDetailDiscountAmount(convertSafeValue(r1.getDetailDiscountAmount()).add(convertSafeValue(r2.getDetailDiscountAmount())));
        return r1;
    }

    /**
    * @Description: 更新或插入数据
    * @Param: [dataList]
    * @return: void
    * @Author: syuson
    * @Date: 2021-4-26
    */
    private void saveUpdateBatch(List<OrderRefundStatistics> dataList){
        List<Long> orderNoList = dataList.stream().map(OrderRefundStatistics::getOrderNo).collect(Collectors.toList());
        QueryWrapper<OrderRefundStatistics> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(OrderRefundStatistics::getOrderNo,orderNoList);
        List<OrderRefundStatistics> existDataList = orderRefundStatisticsMapper.selectList(queryWrapper);
        if(CollectionUtils.isEmpty(existDataList)){
            existDataList = Collections.emptyList();
        }
        Map<Long,OrderRefundStatistics> orderNoOrderRefundStatisticsMap = existDataList.stream().collect(Collectors.toMap(OrderRefundStatistics::getOrderNo, a->a,(v1, v2)->v1));
        List<OrderRefundStatistics> updateDataList = dataList.stream().filter(data->orderNoOrderRefundStatisticsMap.containsKey(data.getOrderNo())).collect(Collectors.toList());
        List<OrderRefundStatistics> insertDataList = dataList.stream().filter(data->!orderNoOrderRefundStatisticsMap.containsKey(data.getOrderNo())).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(updateDataList)){
            updateDataList.forEach(data->{
                data.setId(orderNoOrderRefundStatisticsMap.get(data.getOrderNo()).getId());
                data.setModifyTime(new Date());
            });
            updateBatchById(updateDataList);
        }
        if(!CollectionUtils.isEmpty(insertDataList)){
            saveBatch(insertDataList);
        }
    }

    private BigDecimal convertSafeValue(BigDecimal oldValue){
        return Optional.ofNullable(oldValue).orElse(BigDecimal.ZERO);
    }
}
