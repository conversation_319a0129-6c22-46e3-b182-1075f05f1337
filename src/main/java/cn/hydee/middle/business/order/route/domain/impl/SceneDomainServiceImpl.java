package cn.hydee.middle.business.order.route.domain.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hydee.middle.business.order.route.application.representation.RuleRepresentationDto;
import cn.hydee.middle.business.order.route.application.representation.SceneDetailsRepresentationDto;
import cn.hydee.middle.business.order.route.application.representation.SceneRepresentationDto;
import cn.hydee.middle.business.order.route.application.representation.SceneSelectedRepresentationDto;
import cn.hydee.middle.business.order.route.db.mysql.model.RouteStrategy;
import cn.hydee.middle.business.order.route.domain.SceneAggregate;
import cn.hydee.middle.business.order.route.domain.SceneDomainService;
import cn.hydee.middle.business.order.route.domain.StrategyAggregate;
import cn.hydee.middle.business.order.route.domain.dto.SceneDomainQueryDto;
import cn.hydee.middle.business.order.route.domain.enums.SceneTypeEnum;
import cn.hydee.middle.business.order.route.domain.repository.SceneRepository;
import cn.hydee.middle.business.order.route.domain.repository.StrategyRepository;
import cn.hydee.middle.business.order.util.ExceptionUtil;
import cn.hydee.starter.dto.ErrorType;
import cn.hydee.starter.dto.PageBase;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/03/20 14:14
 **/
@Service
public class SceneDomainServiceImpl implements SceneDomainService {

  @Resource
  private SceneRepository sceneRepository;

  @Resource
  private StrategyRepository strategyRepository;

  @Override
  public IPage<SceneRepresentationDto> pageScene(SceneDomainQueryDto sceneDomainQueryDto) {
    IPage<SceneRepresentationDto> sceneRepresentationDtoPage = sceneRepository.pageScene(sceneDomainQueryDto);
    if(Objects.nonNull(sceneRepresentationDtoPage) && CollUtil.isNotEmpty(sceneRepresentationDtoPage.getRecords())){
      sceneRepresentationDtoPage.getRecords().forEach(e->{
        //规则数和规则类型赋值
        e.setSceneRuleNum(CollectionUtil.isEmpty(e.getRuleIds())?0:e.getRuleIds().size());
        e.setSceneTypeName(SceneTypeEnum.getSceneTypeMsg(e.getSceneType()));
      });
    }
    return sceneRepresentationDtoPage;
  }

  @Override
  public SceneDetailsRepresentationDto getSceneDetail(Long sceneId) {
    return sceneRepository.getSceneDetail(sceneId);
  }

  @Override
  public int saveScene(SceneAggregate sceneAggregate) {
    SceneAggregate.sceneInfoCheck(sceneAggregate);
    return sceneRepository.saveScene(sceneAggregate);
  }

  @Override
  public int updateScene(SceneAggregate sceneAggregate) {
    SceneAggregate.sceneInfoCheck(sceneAggregate);
    Long routeSceneId = sceneAggregate.getRouteSceneId().getSceneId();
    StrategyAggregate strategyAggregate = new StrategyAggregate();
    strategyAggregate.setSceneId(routeSceneId);
    List<RouteStrategy> routeStrategies = strategyRepository.listStrategy(strategyAggregate);
    if(CollectionUtil.isNotEmpty(routeStrategies)){
      throw ExceptionUtil.getWarnException(ErrorType.PARA_ERROR.getCode(),"该场景已被策略引用，不能编辑");
    }
    int row = sceneRepository.updateScene(sceneAggregate);
    if(row < 1){
      throw ExceptionUtil.getWarnException(ErrorType.PARA_ERROR.getCode(),"更新场景失败，请刷新后重新编辑保存");
    }
    return row;
  }

  @Override
  public int deleteScene(Long sceneId) {
    StrategyAggregate strategyAggregate = new StrategyAggregate();
    strategyAggregate.setSceneId(sceneId);
    List<RouteStrategy> routeStrategies = strategyRepository.listStrategy(strategyAggregate);
    if(CollectionUtil.isNotEmpty(routeStrategies)){
      throw ExceptionUtil.getWarnException(ErrorType.PARA_ERROR.getCode(),"该场景已被策略引用，不能删除");
    }
    return sceneRepository.deleteScene(sceneId);
  }

  @Override
  public List<RuleRepresentationDto> listRule() {
    return sceneRepository.listRule();
  }

  @Override
  public List<RuleRepresentationDto> listRuleBySceneId(Long sceneId) {
    List<RuleRepresentationDto> ruleDetails = sceneRepository.listRuleBySceneId(sceneId);
    if(CollectionUtil.isEmpty(ruleDetails)){
      throw ExceptionUtil.getWarnException(ErrorType.PARA_ERROR.getCode(),"该场景未绑定规则，请确认后重新选择");
    }
    return ruleDetails;
  }

  @Override
  public IPage<SceneSelectedRepresentationDto> pageSceneSelected(PageBase pageBase) {
    IPage<SceneSelectedRepresentationDto> sceneSelectedPage = sceneRepository.getSceneSelectedList(pageBase);
    if(Objects.nonNull(sceneSelectedPage) && CollectionUtil.isNotEmpty(sceneSelectedPage.getRecords())){
      sceneSelectedPage.getRecords().forEach(e->{
          e.setSceneTypeName(SceneTypeEnum.getSceneTypeMsg(e.getSceneType()));
      });
    }
    return sceneSelectedPage;
  }
}
