package cn.hydee.middle.business.order.yxtadapter.domainservice.assignment.impl;

import cn.hydee.middle.business.order.yxtadapter.domain.assignment.Assignment;
import cn.hydee.middle.business.order.yxtadapter.domainservice.assignment.gateway.AssignmentCmdGateway;
import cn.hydee.middle.business.order.yxtadapter.domainservice.assignment.gateway.AssignmentQryGateway;
import cn.hydee.middle.business.order.yxtadapter.domainservice.dubbo.request.AssignmentProcessedQry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR>
 * Date:  2023/9/11
 */
@Slf4j
@Component
public class RecoverIdleAssignmentDefaultTask {

    @Autowired
    private AssignmentQryGateway qryGateway;
    @Autowired
    private AssignmentCmdGateway cmdGateway;


    @Value("${assignment.recover.interval:5}")
    private int recoverIntervalMinute = 5;
    @Value("${assignment.recover.limitNum:1000}")
    private int recoverLimitNum = 1000;

    public void doJob(String param) {
        log.info("RecoverIdleAssignmentDefaultTask  start");
        AssignmentProcessedQry assignmentQry = new AssignmentProcessedQry(recoverIntervalMinute, recoverLimitNum);
        List<Assignment> assignments = qryGateway.qryProcessedList(assignmentQry);
        if (assignments == null || assignments.isEmpty()) {
            log.info("RecoverIdleAssignmentDefaultTask assignments is empty");
            return;
        }
        List<Long> ids = assignments.stream().map(Assignment::getId).collect(Collectors.toList());
        boolean b = cmdGateway.recoverIdleAssignmentList(ids);
        if (b) {
            log.info("RecoverIdleAssignmentDefaultTask recoverIdle ENQUEUE  assignment success size = {}", assignments.size());
        } else {
            log.info("RecoverIdleAssignmentDefaultTask recoverIdle  ENQUEUE  assignment fail");
        }

    }


}
