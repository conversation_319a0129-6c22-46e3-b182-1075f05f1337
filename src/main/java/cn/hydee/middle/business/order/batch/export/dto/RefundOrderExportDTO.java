package cn.hydee.middle.business.order.batch.export.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/5/18 上午10:52
 */
@Data
public class RefundOrderExportDTO implements Serializable {
    private static final long serialVersionUID = -3922057292046419008L;
    @ExcelProperty(value = "退款单号")
    private String refundNo;
    @ExcelProperty(value = "平台订单编号")
    private String thirdOrderNo;
    @ExcelProperty(value = "平台")
    private String thirdPlatformName;
    @ExcelProperty(value = "门店编码")
    private String onlineStoreCode;
    @ExcelProperty(value = "门店名称")
    private String onlineStoreName;
    @ExcelProperty(value = "机构编码")
    private String organizationCode;
    @ExcelProperty(value = "所属机构")
    private String organizationName;
    @ExcelProperty(value = "退款类型")
    private String type;
    @ExcelProperty(value = "下账类型")
    private String billType;
    @ExcelProperty(value = "退款原因")
    private String reason;
    @ExcelProperty(value = "问题描述")
    private String desc;
    @ExcelProperty(value = "退买家总金额")
    private BigDecimal consumerRefund;
    @ExcelProperty(value = "商家退款总金额")
    private BigDecimal shopRefund;
    @ExcelProperty(value = "下账商家退款总金额")
    private BigDecimal refundMerchantTotal;
    @ExcelProperty(value = "状态")
    private String state;
    @ExcelProperty(value = "下账状态")
    private String erpState;
    @ExcelProperty(value = "申请时间", format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @ExcelProperty(value = "退款完成时间", format = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;

}
