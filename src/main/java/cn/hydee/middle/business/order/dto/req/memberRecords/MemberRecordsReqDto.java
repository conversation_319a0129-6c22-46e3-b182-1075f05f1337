/*  
 * Project Name:hydee-business-order  
 * File Name:MemberRecordsReqDto.java  
 * Package Name:cn.hydee.middle.business.order.dto.req.memberRecords  
 * Date:2020年9月16日下午2:49:20  
 * Copyright (c) 2020, Shanghai Hydee Software Corp., Ltd. All Rights Reserved.  
 *  
*/  
  
package cn.hydee.middle.business.order.dto.req.memberRecords;

import java.util.List;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnore;

import cn.hydee.starter.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**  
 * ClassName:MemberRecordsReqDto <br/>  
 * Function: 微商城会员消费-请求. <br/> 
 * Date:     2020年9月16日 下午2:49:20 <br/>  
 * <AUTHOR>  
 */
@Data
@EqualsAndHashCode(callSuper=false)
public class MemberRecordsReqDto extends PageBase {

	@ApiModelProperty(value = "商户编码",required = true)
	@NotNull(message = "商户编码不能为空")
	private String merCode;
	
	@ApiModelProperty(value = "会员号",required = true)
	@NotNull(message = "会员号不能为空")
	private String memberNo;
	
	@ApiModelProperty(value = "开始时间，例如 2020-09-16 00:00:00",required = true)
	@NotNull(message = "开始时间不能为空")
	private String beginTime;
	
	@ApiModelProperty(value = "结束时间，例如 2020-09-17 00:00:00",required = true)
	@NotNull(message = "结束时间不能为空")
	private String endTime;
	
	@ApiModelProperty(value = "订单状态(空数组查所有)，5:待审方，10:待接单，20：待拣货，30：待配送，40：配送中，100：已完成，102：已取消，101：已关闭")
	private List<Integer> states;
	
	@JsonIgnore
	private Integer stateComplete;
}
  
