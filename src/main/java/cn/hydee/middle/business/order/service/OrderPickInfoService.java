package cn.hydee.middle.business.order.service;

import cn.hydee.middle.business.order.entity.OrderPickInfo;

import java.util.List;

/**
 * @Author: syuson
 * @Date: 2021-7-16
 */
public interface OrderPickInfoService {


    List<OrderPickInfo> selectListByDetailIdList(List<Long> detailIdList);

    Integer selectPickInfoByOrderNo(Long orderNo);

    void batchSave(List<OrderPickInfo> pickInfoList);


    int deleteBatchIds(List<Long> ids);

    /**
     * 查询所有拣货信息
     * @param detailIdList
     * @return
     */
    List<OrderPickInfo> selectListByDetailIdListExIsValid(List<Long> detailIdList);

    void saveIfNotEmptyOrderPickInfo(Long orderNo,List<OrderPickInfo> pickInfoList);
}
