package cn.hydee.middle.business.order.yxtadapter.domain.createordertool.command;

import cn.hydee.middle.business.order.Enums.DeliveryPlatformEnum;
import cn.hydee.middle.business.order.Enums.DeliveryStateEnum;
import cn.hydee.middle.business.order.Enums.DeliveryTypeEnum;
import cn.hydee.middle.business.order.entity.OrderDeliveryRecord;
import cn.hydee.middle.business.order.yxtadapter.constant.RepairOrderTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * 手工订单门店信息类
 * <AUTHOR>
 * @Date 2024-04-03 11:25
 * @Version 1.0
 **/
@Data
public class OrderCommonDto implements Serializable {
    /**
     * 平台code  必传
     */
    @ApiModelProperty(value = "平台code")
    private String thirdPlatformCode;

    /**
     * 平台名称  必传
     */
    @ApiModelProperty(value = "平台code")
    private String thirdPlatformName;

    /**
     * 第三方平台订单号  可选
     */
    @ApiModelProperty(value = "第三方平台订单号")
    private String thirdOrderNo;

    /**
     * 门店编码
     */
    @ApiModelProperty(value = "门店编码")
    private String onlineStoreCode;

    /**
     * 门店名称
     */
    @ApiModelProperty(value = "门店名称")
    private String onlineStoreName;

    /**
     * 下单门店编码 必传
     */
    @ApiModelProperty(value = "下单门店编码")
    private String sourceOrganizationCode;

    /**
     * 下单门店名称 必传
     */
    @ApiModelProperty(value = "下单门店名称")
    private String sourceOrganizationName;

    /**
     * 发货门店编码  必传
     */
    @ApiModelProperty(value = "发货门店编码")
    private String organizationCode;

    /**
     * 发货门店名称  必传
     */
    @ApiModelProperty(value = "发货门店名称")
    private String organizationName;

    /**
     * 修补文本记录  必传
     */
    @ApiModelProperty(value = "修补文本记录")
    private String repairText;

    /**
     * 配送方式
     */
    @ApiModelProperty(value = "配送方式")
    private String deliveryType;

    /**
     * 配送方式
     */
    @ApiModelProperty(value = "配送方式描述")
    private String deliveryDesc;


    /**
     * 拣货操作者id
     */
    @ApiModelProperty(value = "拣货实施者id")
    private String pickOperatorId;

    /**
     * 拣货操作者名
     */
    @ApiModelProperty(value = "拣货实施者名")
    private String pickOperatorName;



    public OrderDeliveryRecord createOrderDeliveryRecord(Long orderNo,OrderToolDto req) {
        Date createDate=new Date();
        OrderDeliveryRecord deliveryRecord = new OrderDeliveryRecord();
        deliveryRecord.setOrderNo(orderNo);
        deliveryRecord.setState(DeliveryStateEnum.COMPLETED.getCode());
        deliveryRecord.setRiderOrderNo("");
        deliveryRecord.setRiderPlatform("");
        deliveryRecord.setDeliveryType(this.getDeliveryType());
        deliveryRecord.setDeliveryPlatName(DeliveryPlatformEnum.getByCode(this.getDeliveryType()));
        deliveryRecord.setDeliveryClientCode(this.getOrganizationCode());
        deliveryRecord.setDeliveryStoreCode(this.getOrganizationCode());
        deliveryRecord.setDeliveryTip(BigDecimal.ZERO);
        deliveryRecord.setRiderName("系统补单");
        deliveryRecord.setRiderPhone("");
        deliveryRecord.setRiderStaffCode("");
        deliveryRecord.setRiderAddress("系统补单");
        deliveryRecord.setReceiverName("系统补单");
        deliveryRecord.setLatitude("");
        deliveryRecord.setLongitude("");
        deliveryRecord.setCallTime(createDate);
        deliveryRecord.setAcceptTime(createDate);
        deliveryRecord.setPickTime(createDate);
        deliveryRecord.setActualDeliveryFee(BigDecimal.ZERO);
        deliveryRecord.setDeliveryFeeTotal(BigDecimal.ZERO);
        deliveryRecord.setCancelFlag(0);
        return deliveryRecord;
    }
}
