package cn.hydee.middle.business.order.service.impl;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.Enums.OrderDetailStatusEnum;
import cn.hydee.middle.business.order.dto.message.NetRefundNotifyMessage;
import cn.hydee.middle.business.order.dto.req.AddOrderDetailReqDto;
import cn.hydee.middle.business.order.dto.req.AddOrderInfoReqDto;
import cn.hydee.middle.business.order.dto.rsp.AssembleCommodityRelateDTO;
import cn.hydee.middle.business.order.dto.rsp.CommodityRspDto;
import cn.hydee.middle.business.order.dto.rsp.ThirdPlatCodeNameRsp;
import cn.hydee.middle.business.order.entity.OrderAssembleCommodityRelation;
import cn.hydee.middle.business.order.entity.OrderDetail;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.mapper.OrderAssembleCommodityRelationMapper;
import cn.hydee.middle.business.order.mapper.OrderDetailMapper;
import cn.hydee.middle.business.order.service.OrderAssembleCommodityRelationService;
import cn.hydee.middle.business.order.service.suport.OrderBasicService;
import cn.hydee.middle.business.order.v2.manager.base.OrderBasicManager;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/1/12 上午9:12
 */
@Service
@Slf4j
public class OrderAssembleCommodityRelationServiceImpl implements OrderAssembleCommodityRelationService {

    @Autowired
    private OrderAssembleCommodityRelationMapper orderAssembleCommodityRelationMapper;
    @Autowired
    private OrderBasicManager orderBasicManager;
    @Autowired
    private OrderBasicService orderBasicService;
    @Autowired
    private OrderDetailMapper orderDetailMapper;

    /** 调用商品中台 获取商品是否为组合商品 进行拆分 对金额进行重算
     *
     * A=(B*2)+(C*3)
     *
     *
     *
     * 商品金额=(B单价price *  B数量number)+(C单价price *  C数量number)
     *
     *
     * B的单价=A单价* （(B单价price *  B数量number)/商品金额  小数位6向下 ）
     *
     *
     * C的单价=A单价-B的单价
     * <AUTHOR>
     * @Description
     * @date 2024/5/9 10:34
     */
    @Override
    public List<CommodityRspDto> checkOrder(AddOrderInfoReqDto addOrderInfoReqDto, List<CommodityRspDto> commodityRspDtoList) {
        try {
            List<CommodityRspDto> splitCommodityRspDtoList = new ArrayList<>();
            List<AddOrderDetailReqDto> spiltOrderDetailList = new ArrayList<>();
            List<OrderAssembleCommodityRelation> relationList = new ArrayList<>();
            Set<String> relationErpCodeSet = new HashSet<>();

            List<AddOrderDetailReqDto> orderDetailList = addOrderInfoReqDto.getOrderdetaillist();
            Map<String, CommodityRspDto> commodityRspDtoMap = commodityRspDtoList.stream().collect(Collectors.toMap(CommodityRspDto::getErpCode, commodity -> commodity));
            //是否拆单过
            boolean split = false;
            for (AddOrderDetailReqDto detailReqDto : orderDetailList) {
                CommodityRspDto commodityRspDto = commodityRspDtoMap.get(detailReqDto.getOuter_iid());
                if (commodityRspDto != null) {
                    //组合商品需要拆分
                    if (DsConstants.INTEGER_TWO.equals(commodityRspDto.getCommodityType()) && !CollectionUtils.isEmpty(commodityRspDto.getItems())) {
                        List<AssembleCommodityRelateDTO> items = commodityRspDto.getItems();
                        log.info("checkOrder commodityRspDto info. detailReqDto:{}. items:{}", JSON.toJSONString(detailReqDto),  JSON.toJSONString(items));
                        split = true;
                        //计算组合商品明细总价值（分母）
                        BigDecimal denominator = BigDecimal.ZERO;
                        for (AssembleCommodityRelateDTO assembleCommodityRelateDTO : items) {
                            denominator = denominator.add(assembleCommodityRelateDTO.getPrice().multiply(new BigDecimal(assembleCommodityRelateDTO.getNumber())));
                        }

                        BigDecimal lastOnePrice = BigDecimal.ZERO;
                        if (StringUtils.isNotEmpty(detailReqDto.getPrice())) {
                            lastOnePrice = new BigDecimal(detailReqDto.getPrice());
                        }
                        BigDecimal lastOneOriginPrice = BigDecimal.ZERO;
                        if (Objects.nonNull(detailReqDto.getOriginPrice())) {
                            lastOneOriginPrice = detailReqDto.getOriginPrice();
                        }

                        BigDecimal lastOneTotalFee = BigDecimal.ZERO;
                        if (StringUtils.isNotEmpty(detailReqDto.getTotal_fee())) {
                            lastOneTotalFee = new BigDecimal(detailReqDto.getTotal_fee());
                        }

                        BigDecimal lastOnePayment = BigDecimal.ZERO;
                        if (StringUtils.isNotEmpty(detailReqDto.getPayment())) {
                            lastOnePayment = new BigDecimal(detailReqDto.getPayment());
                        }

                        BigDecimal lastOneDiscountFee = BigDecimal.ZERO;
                        if (StringUtils.isNotEmpty(detailReqDto.getDiscount_fee())) {
                            lastOneDiscountFee = new BigDecimal(detailReqDto.getDiscount_fee());
                        }

                        BigDecimal lastOneAdjustFee = BigDecimal.ZERO;
                        if (StringUtils.isNotEmpty(detailReqDto.getAdjust_fee())) {
                            lastOneAdjustFee = new BigDecimal(detailReqDto.getAdjust_fee());
                        }

                        BigDecimal lastOneMerchantDiscount = BigDecimal.ZERO;
                        if (Objects.nonNull(detailReqDto.getMerchantDiscount())) {
                            lastOneMerchantDiscount = detailReqDto.getMerchantDiscount();
                        }

                        for (int i = 0; i < items.size(); i++) {
                            AssembleCommodityRelateDTO assembleCommodityRelateDTO = items.get(i);
                            //构建新的commodityRspDto
                            CommodityRspDto splitCommodityRspDto = constructCommodityRspDto(assembleCommodityRelateDTO);
                            if (!commodityRspDtoMap.containsKey(splitCommodityRspDto.getErpCode())) {
                                splitCommodityRspDtoList.add(splitCommodityRspDto);
                                //防止出现重复数据加入splitCommodityRspDtoList
                                commodityRspDtoMap.put(splitCommodityRspDto.getErpCode(), splitCommodityRspDto);
                            }

                            //构建新的detailReqDto
                            AddOrderDetailReqDto spiltDetailReqDto = new AddOrderDetailReqDto();
                            spiltDetailReqDto.setOlorderno(addOrderInfoReqDto.getOlorderno());
                            spiltDetailReqDto.setClientid(detailReqDto.getClientid());
                            spiltDetailReqDto.setOid(detailReqDto.getOid());

                            //计算分子以及分摊比例
                            BigDecimal numerator = assembleCommodityRelateDTO.getPrice().multiply(new BigDecimal(assembleCommodityRelateDTO.getNumber()));
                            // 尽可能使金额接近真实值规避金额负数，调整计算精度到6位小数
                            BigDecimal rate = numerator.divide(denominator, 2, RoundingMode.HALF_DOWN);

                            //最后一个item使用减法计算出来，避免因四舍五入造成总金额有差异
                            if (i == items.size() - 1) {
                                spiltDetailReqDto.setPrice(lastOnePrice.divide(new BigDecimal(assembleCommodityRelateDTO.getNumber()), 2, RoundingMode.HALF_UP).toPlainString());
                                spiltDetailReqDto.setOriginPrice(lastOneOriginPrice.divide(new BigDecimal(assembleCommodityRelateDTO.getNumber()), 2, RoundingMode.HALF_UP));
                                spiltDetailReqDto.setTotal_fee(lastOneTotalFee.toString());
                                spiltDetailReqDto.setPayment(lastOnePayment.toString());
                                spiltDetailReqDto.setDiscount_fee(lastOneDiscountFee.toString());
                                spiltDetailReqDto.setAdjust_fee(lastOneAdjustFee.toString());
                                spiltDetailReqDto.setMerchantDiscount(lastOneMerchantDiscount);
                            } else {
                                BigDecimal itemPrice = BigDecimal.ZERO;
                                if (detailReqDto.getPrice() != null) {
                                    itemPrice = new BigDecimal(detailReqDto.getPrice())
                                            .multiply(rate)
                                            .setScale(2, RoundingMode.HALF_UP);
                                }
                                spiltDetailReqDto.setPrice(itemPrice.divide(new BigDecimal(assembleCommodityRelateDTO.getNumber()), 2, RoundingMode.HALF_UP).toPlainString());
                                lastOnePrice = lastOnePrice.subtract(itemPrice);

                                //  商品实际单价(客户实际支付的该商品的单价)
                                BigDecimal itemOriginPrice = BigDecimal.ZERO;
                                if (detailReqDto.getOriginPrice() != null) {
                                    itemOriginPrice = detailReqDto.getOriginPrice()
                                            .multiply(rate)
                                            .setScale(2, RoundingMode.HALF_UP);
                                }
                                spiltDetailReqDto.setOriginPrice(itemOriginPrice.divide(new BigDecimal(assembleCommodityRelateDTO.getNumber()), 2, RoundingMode.HALF_UP));
                                lastOneOriginPrice = lastOneOriginPrice.subtract(itemOriginPrice);


                                BigDecimal itemTotalFee = BigDecimal.ZERO;
                                if (detailReqDto.getTotal_fee() != null) {
                                    itemTotalFee = new BigDecimal(detailReqDto.getTotal_fee())
                                            .multiply(rate)
                                            .setScale(2, RoundingMode.HALF_UP);
                                }
                                spiltDetailReqDto.setTotal_fee(itemTotalFee.toString());
                                lastOneTotalFee = lastOneTotalFee.subtract(itemTotalFee);

                                BigDecimal itemPayment = BigDecimal.ZERO;
                                if (detailReqDto.getPayment() != null) {
                                    itemPayment = new BigDecimal(detailReqDto.getPayment())
                                            .multiply(rate)
                                            .setScale(2, RoundingMode.HALF_UP);
                                }
                                spiltDetailReqDto.setPayment(itemPayment.toString());
                                lastOnePayment = lastOnePayment.subtract(itemPayment);

                                BigDecimal itemDiscountFee = BigDecimal.ZERO;
                                if (detailReqDto.getDiscount_fee() != null) {
                                    itemDiscountFee = new BigDecimal(detailReqDto.getDiscount_fee())
                                            .multiply(rate)
                                            .setScale(2, RoundingMode.HALF_UP);
                                }
                                spiltDetailReqDto.setDiscount_fee(itemDiscountFee.toString());
                                lastOneDiscountFee = lastOneDiscountFee.subtract(itemDiscountFee);

                                BigDecimal itemAdjustFee = BigDecimal.ZERO;
                                if (detailReqDto.getAdjust_fee() != null) {
                                    itemAdjustFee = new BigDecimal(detailReqDto.getAdjust_fee())
                                            .multiply(rate)
                                            .setScale(2, RoundingMode.HALF_UP);
                                }
                                spiltDetailReqDto.setAdjust_fee(itemAdjustFee.toString());
                                lastOneAdjustFee = lastOneAdjustFee.subtract(itemAdjustFee);

                                BigDecimal itemMerchantDiscount = BigDecimal.ZERO;
                                if (detailReqDto.getAdjust_fee() != null) {
                                    itemMerchantDiscount = detailReqDto.getMerchantDiscount()
                                            .multiply(rate)
                                            .setScale(2, RoundingMode.HALF_UP);
                                }
                                spiltDetailReqDto.setMerchantDiscount(itemMerchantDiscount);
                                lastOneMerchantDiscount = lastOneMerchantDiscount.subtract(itemMerchantDiscount);
                            }


                            spiltDetailReqDto.setNum(String.valueOf(Integer.parseInt(detailReqDto.getNum()) * assembleCommodityRelateDTO.getNumber()));
                            spiltDetailReqDto.setNum_iid(detailReqDto.getNum_iid());
                            spiltDetailReqDto.setItem_meal_id(detailReqDto.getItem_meal_id());
                            spiltDetailReqDto.setModified(detailReqDto.getModified());
                            spiltDetailReqDto.setTitle(assembleCommodityRelateDTO.getName());
                            spiltDetailReqDto.setSku_properties_name(assembleCommodityRelateDTO.getSpecJoins());
                            spiltDetailReqDto.setSku_id(String.valueOf(assembleCommodityRelateDTO.getSpecId()));
                            spiltDetailReqDto.setOuter_iid(assembleCommodityRelateDTO.getErpCode());
                            spiltDetailReqDto.setUpc(assembleCommodityRelateDTO.getBarCode());
                            spiltDetailReqDto.setIsdelete(detailReqDto.getIsdelete());
                            String orderDetailId = detailReqDto.getOrderdetailid() == null ? detailReqDto.getOuter_iid() + "_" + (i + 1) : detailReqDto.getOrderdetailid() + "_" + (i + 1);
                            spiltDetailReqDto.setOrderdetailid(orderDetailId);
                            spiltDetailReqDto.setIsgift(detailReqDto.getIsgift());
                            spiltDetailReqDto.setOrder_store_source(detailReqDto.getOrder_store_source());
                            spiltDetailReqDto.setStore_code(detailReqDto.getStore_code());
                            spiltDetailReqDto.setExpectdeliverytime(detailReqDto.getExpectdeliverytime());
                            spiltDetailReqDto.setOrdersuitlist(detailReqDto.getOrdersuitlist());
                            spiltDetailReqDto.setExtrainfo(detailReqDto.getExtrainfo());
                            spiltDetailReqDto.setOriginalErpCode(detailReqDto.getOuter_iid());
                            spiltDetailReqDto.setOriginalErpCodeNum(detailReqDto.getNum());
                            spiltOrderDetailList.add(spiltDetailReqDto);


                            //构建组合商品订单关系数据，防止美团同一个erpCode多条订单明细产生多条关系，唯一索引校验不通过
                            if (!relationErpCodeSet.contains(detailReqDto.getOuter_iid())) {
                                relationList.add(constructRelation(addOrderInfoReqDto, detailReqDto, assembleCommodityRelateDTO, orderDetailId, rate));
                            }
                        }
                        if (!relationErpCodeSet.contains(detailReqDto.getOuter_iid())) {
                            relationErpCodeSet.add(detailReqDto.getOuter_iid());
                        }
                    }
                    else if (DsConstants.INTEGER_FOUR.equals(commodityRspDto.getCommodityType()) && !CollectionUtils.isEmpty(commodityRspDto.getItems())) {
                        //拆零商品处理
                        List<AssembleCommodityRelateDTO> items = commodityRspDto.getItems();
                        AssembleCommodityRelateDTO assembleCommodityRelateDTO = items.get(0);

                        detailReqDto.setChaiLingOriginalErpCode(detailReqDto.getOuter_iid());
                        detailReqDto.setTitle(assembleCommodityRelateDTO.getName());
                        detailReqDto.setSku_properties_name(assembleCommodityRelateDTO.getSpecJoins());
                        detailReqDto.setSku_id(String.valueOf(assembleCommodityRelateDTO.getSpecId()));
                        detailReqDto.setOuter_iid(assembleCommodityRelateDTO.getErpCode());
                        detailReqDto.setUpc(assembleCommodityRelateDTO.getBarCode());

                        CommodityRspDto clCommodityRspDto = new CommodityRspDto();
                        clCommodityRspDto.setBarCode(assembleCommodityRelateDTO.getBarCode());
                        clCommodityRspDto.setErpCode(assembleCommodityRelateDTO.getErpCode());
                        clCommodityRspDto.setExist(DsConstants.INTEGER_ONE);
                        clCommodityRspDto.setMainPic(assembleCommodityRelateDTO.getMainPic());
                        clCommodityRspDto.setManufacture(assembleCommodityRelateDTO.getManufacture());
                        clCommodityRspDto.setApprovalNumber(assembleCommodityRelateDTO.getApprovalNumber());
                        clCommodityRspDto.setName(assembleCommodityRelateDTO.getName());
                        clCommodityRspDto.setSpecJoins(assembleCommodityRelateDTO.getSpecJoins());
                        clCommodityRspDto.setCommodityType(assembleCommodityRelateDTO.getCommodityType());
                        clCommodityRspDto.setChailing(DsConstants.INTEGER_TWO);
                        clCommodityRspDto.setChiLingNum(assembleCommodityRelateDTO.getNumber());
                        clCommodityRspDto.setFirstTypeName(assembleCommodityRelateDTO.getFirstTypeName());
                        clCommodityRspDto.setSecondTypeName(assembleCommodityRelateDTO.getSecondTypeName());
                        clCommodityRspDto.setTypeName(assembleCommodityRelateDTO.getTypeName());

                        spiltOrderDetailList.add(detailReqDto);
                        splitCommodityRspDtoList.add(clCommodityRspDto);
                    } else {
                        spiltOrderDetailList.add(detailReqDto);
                        splitCommodityRspDtoList.add(commodityRspDto);
                    }
                } else {
                    spiltOrderDetailList.add(detailReqDto);
                }
            }
            if (!CollectionUtils.isEmpty(relationList)) {
                try {
                    orderAssembleCommodityRelationMapper.batchInsert(relationList);
                } catch (Exception e) {
                    log.error("batchInsert relation error. olOrderNo:{}.", addOrderInfoReqDto.getOlorderno());
                }
            }
            if (split) {
                log.info("checkOrder success. olOrderNo:{}. spiltOrderDetailList:{}. splitCommodityRspDtoList:{}", addOrderInfoReqDto.getOlorderno(), spiltOrderDetailList, splitCommodityRspDtoList);
            }
            addOrderInfoReqDto.setOrderdetaillist(spiltOrderDetailList);
            return splitCommodityRspDtoList;
        } catch (Exception e) {
            log.error("checkOrder error. olOrderNo:{}.", addOrderInfoReqDto.getOlorderno(), e);
        }
        return commodityRspDtoList;
    }

    @Override
    public void checkRefundMessage(NetRefundNotifyMessage message,OrderInfo orderInfo) {
        //非部分退款，goods为空
        if (StringUtils.isEmpty(message.getGoods())) {
            return;
        }
        try {
            List<NetRefundNotifyMessage.GoodsItem> goodsItemList = JSON.parseArray(message.getGoods(), NetRefundNotifyMessage.GoodsItem.class);
            List<NetRefundNotifyMessage.GoodsItem> newGoodsItemList = new ArrayList<>();
            List<OrderAssembleCommodityRelation> relationList = orderAssembleCommodityRelationMapper.selectList(new QueryWrapper<OrderAssembleCommodityRelation>().lambda()
                    .eq(OrderAssembleCommodityRelation::getThirdOrderNo, message.getOlorderno())
                    .eq(OrderAssembleCommodityRelation::getEctype, message.getEctype()));
            //拆离商品还原
            Map<String, String> chailingMap = new HashMap<>();
            List<OrderDetail> chailingDetailList = orderDetailMapper.selectChailingOrderDetail(orderInfo.getOrderNo());
            if (!CollectionUtils.isEmpty(chailingDetailList)) {
                chailingMap = chailingDetailList.stream().collect(Collectors.toMap(OrderDetail::getChaiLingOriginalErpCode, OrderDetail::getErpCode, (a, b) -> a));
            }


            Map<String, List<OrderAssembleCommodityRelation>> relationMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(relationList)) {
                relationMap = relationList.stream().collect(Collectors.toMap(OrderAssembleCommodityRelation::getOriginalErpCode,
                        p -> {
                            List<OrderAssembleCommodityRelation> relations = new ArrayList<>();
                            relations.add(p);
                            return relations;
                        },
                        (List<OrderAssembleCommodityRelation> value1, List<OrderAssembleCommodityRelation> value2) -> {
                            value1.addAll(value2);
                            return value1;
                        }
                ));
            }

            if (chailingMap.isEmpty() && relationMap.isEmpty()) {
                return;
            }
            List<OrderDetail> detailList = orderDetailMapper.selectAllListByOrderNo(orderInfo.getOrderNo());
            Map<String, BigDecimal> platNameMap = detailList.stream().collect(Collectors.toMap(OrderDetail::getThirdDetailId, OrderDetail::getPrice, (value1, value2) -> value2));
            for (NetRefundNotifyMessage.GoodsItem item : goodsItemList) {
                if (relationMap.containsKey(item.getOutskuid())) {
                    List<OrderAssembleCommodityRelation> itemRelationList = relationMap.get(item.getOutskuid());
                    for (OrderAssembleCommodityRelation relation : itemRelationList) {
                        NetRefundNotifyMessage.GoodsItem newItem = new NetRefundNotifyMessage.GoodsItem();
                        newItem.setOrderdetailid(relation.getOrderDetailId());
                        newItem.setSkuid(item.getSkuid());
                        newItem.setOutskuid(relation.getErpCode());
                        newItem.setName(relation.getName());
                        newItem.setUpc(relation.getBarCode());
                        newItem.setCount(String.valueOf(relation.getCount() * Integer.parseInt(item.getCount())));
                        newItem.setPlatformRefundCount(item.getPlatformRefundCount());
                        BigDecimal shopRefund = BigDecimal.ZERO;
                        if (item.getShop_refund() != null) {
                            shopRefund = new BigDecimal(item.getShop_refund()).multiply(relation.getRate());
                        }
                        newItem.setShop_refund(shopRefund.toString());

                        BigDecimal activityDiscountAmount = BigDecimal.ZERO;
                        if (item.getActivityDiscountAmont() != null) {
                            activityDiscountAmount = new BigDecimal(item.getActivityDiscountAmont()).multiply(relation.getRate());
                        }
                        newItem.setActivityDiscountAmont(activityDiscountAmount.toString());

                        BigDecimal couponAmount = BigDecimal.ZERO;
                        if (item.getCouponAmount() != null) {
                            couponAmount = new BigDecimal(item.getCouponAmount()).multiply(relation.getRate());
                        }
                        newItem.setCouponAmount(couponAmount.toString());
                        // 组合商品金额问题 先取正单单价 无对应正单金额取退款金额除数量
                        BigDecimal refundMoney;
                        BigDecimal orDefault = platNameMap.getOrDefault(relation.getOrderDetailId(), new BigDecimal("-1"));
                        if (orDefault.compareTo(BigDecimal.ZERO)<0 && item.getRefundmoney() != null) {
                            BigDecimal multiply = new BigDecimal(item.getRefundmoney()).multiply(relation.getRate());
                            // 商品单价
                            refundMoney =multiply.divide(new BigDecimal(newItem.getCount()),4,RoundingMode.HALF_UP);
                        }else {
                            refundMoney=orDefault.compareTo(BigDecimal.ZERO)<0 ?BigDecimal.ZERO:orDefault;
                        }
                        newItem.setRefundmoney(refundMoney.toString());
                        newItem.setOriginalErpCode(item.getOutskuid());
                        newGoodsItemList.add(newItem);
                    }
                } else if (chailingMap.containsKey(item.getOutskuid())) {
                    item.setOutskuid(chailingMap.get(item.getOutskuid()));
                    item.setChailing(DsConstants.INTEGER_TWO);
                    newGoodsItemList.add(item);
                } else {
                    newGoodsItemList.add(item);
                }
            }
            log.info("checkRefundMessage success. olOrderNo:{}. message:{}. newGoodsItemList:{}", message.getOlorderno(), message, JSON.toJSONString(newGoodsItemList));
            message.setGoods(JSON.toJSONString(newGoodsItemList));
        } catch (Exception e) {
            log.error("checkRefundMessage error. olOrderNo:{}. message:{}.", message.getOlorderno(), message, e);
        }
    }

    private void JDDJCheck(NetRefundNotifyMessage message, OrderInfo orderInfo, List<NetRefundNotifyMessage.GoodsItem> goodsItemList) {
        if(org.apache.commons.lang3.StringUtils.isNotBlank(message.getType()) && message.getType().equals("0")){
            List<OrderDetail> orderDetails = orderDetailMapper.selectListWithExchangeByOrderNo(orderInfo.getOrderNo());
            Set<String> refundGoodsSet = goodsItemList.stream().map(NetRefundNotifyMessage.GoodsItem::getSkuid).filter(Objects::nonNull).collect(Collectors.toSet());
            // 不是正品 不是换货 不是已经退款
            List<OrderDetail> goods = orderDetails.stream().filter(orderDetail -> orderDetail.getIsGift() == 0
                    && !Objects.equals(orderDetail.getStatus(), OrderDetailStatusEnum.REFUND.getCode())).collect(Collectors.toList());
            Set<String> collect = goods.stream().map(OrderDetail::getPlatformSkuId).collect(Collectors.toSet());
            if(CollectionUtils.isEmpty(goods)  ||  refundGoodsSet.size() > collect.size()){
                message.setType("1");
            }

            if(goodsItemList.size()==goods.size()){
                Map<String, BigDecimal> oldGoods=new HashMap<>();
                goods.forEach(a->{
                    if(oldGoods.containsKey(a.getErpCode())){
                        oldGoods.put(a.getPlatformSkuId(),oldGoods.get(a.getPlatformSkuId()).add(a.getTotalAmount()));
                    }else {
                        oldGoods.put(a.getPlatformSkuId(),a.getTotalAmount());
                    }
                });
                Map<String, BigDecimal> refundGoods=new HashMap<>();
                goodsItemList.forEach(a->{
                    if(refundGoods.containsKey(a.getSkuid())){
                        String s = Objects.nonNull(a.getRefundmoney()) ? a.getRefundmoney() : "0";
                        refundGoods.put(a.getSkuid(),oldGoods.get(a.getSkuid()).add(new BigDecimal(s)));
                    }else {
                        String s = Objects.nonNull(a.getRefundmoney()) ? a.getRefundmoney() : "0";
                        refundGoods.put(a.getSkuid(),new BigDecimal(s));
                    }
                });
                if(oldGoods.entrySet().equals(refundGoods.entrySet())){
                    message.setType("1");
                }
            }
        }
    }


    @Override
    public void isAllRefund(NetRefundNotifyMessage message, OrderInfo orderInfo) {
        List<NetRefundNotifyMessage.GoodsItem> goodsItemList = JSON.parseArray(message.getGoods(), NetRefundNotifyMessage.GoodsItem.class);
        if(org.apache.commons.lang3.StringUtils.isNotBlank(message.getType()) && message.getType().equals("0")){
            // 退款商品明细
            List<NetRefundNotifyMessage.GoodsItem> refundGoodsList = goodsItemList.stream().filter(a -> Objects.nonNull(a.getSkuid())).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(refundGoodsList)){return;}
            // 原商品明细 原始拆零订单详情，如换过货，取得是换货之前的明细
            List<OrderDetail> goods = orderDetailMapper.selectOriDetailList(orderInfo.getOrderNo());
            // 不是正品 不是换货 不是已经退款
            List<OrderDetail> orderDetails = goods.stream().filter(orderDetail -> orderDetail.getIsGift() == 0
                    && !Objects.equals(orderDetail.getStatus(), OrderDetailStatusEnum.REFUND.getCode())).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(orderDetails)){return;}
            //
            Map<String, IntSummaryStatistics> orderGoodsGroupByGoodsCode = orderDetails.stream()
                    .collect(Collectors.groupingBy(OrderDetail::getPlatformSkuId, Collectors.summarizingInt(OrderDetail::getCommodityCount)));
            //
            Map<String, IntSummaryStatistics> refundGoodsGroupByGoodsCode = refundGoodsList.stream()
                    .collect(Collectors.groupingBy(NetRefundNotifyMessage.GoodsItem::getSkuid, Collectors.summarizingInt(a -> Integer.parseInt(a.getCount()))));

            // 比较
            for (Map.Entry<String, IntSummaryStatistics> entry : orderGoodsGroupByGoodsCode.entrySet()) {
                String goodsCode = entry.getKey();
                if(!refundGoodsGroupByGoodsCode.containsKey(goodsCode)) {
                    // 商品表中任一商品没有出现在退款清单中, 则表示本次退款并没有退掉所有商品
                    return;
                } else {
                    // 商品数量
                    long count1 = entry.getValue().getSum();
                    // 退款数量
                    IntSummaryStatistics intSummaryStatistics = refundGoodsGroupByGoodsCode.get(goodsCode);
                    long count2 = intSummaryStatistics.getSum();
                    if(count1 > count2) {
                        // 任一商品的数量大于本次退款商品数量, 则表示本次退款并没有退掉所有商品
                        return ;
                    }
                }
            }
            // 已全部退了
            message.setType("1");
        }
    }


    @Override
    public Integer getRefundOriginalCount(OrderDetail orderDetail){
        OrderInfo orderInfo = orderBasicManager.getOrderBaseInfo(orderDetail.getOrderNo());
        List<OrderAssembleCommodityRelation> relationList = orderAssembleCommodityRelationMapper.selectList(new QueryWrapper<OrderAssembleCommodityRelation>().lambda()
                .eq(OrderAssembleCommodityRelation::getMerCode, orderInfo.getMerCode())
                .eq(OrderAssembleCommodityRelation::getThirdOrderNo, orderInfo.getThirdOrderNo())
                .eq(OrderAssembleCommodityRelation::getEctype, orderInfo.getThirdPlatformCode())
                .eq(OrderAssembleCommodityRelation::getOriginalErpCode,orderDetail.getOriginalErpCode())
                .eq(OrderAssembleCommodityRelation::getErpCode,orderDetail.getErpCode())
                .eq(OrderAssembleCommodityRelation::getBarCode,orderDetail.getBarCode())
        );
        if(CollectionUtils.isEmpty(relationList)){
            return orderDetail.getRefundCount();
        }
        OrderAssembleCommodityRelation orderAssembleCommodityRelation = relationList.get(0);
        Integer refundCount = orderDetail.getRefundCount();
        Integer refundOriginalCount = refundCount/orderAssembleCommodityRelation.getCount();
        return refundOriginalCount;
    }

    private CommodityRspDto constructCommodityRspDto(AssembleCommodityRelateDTO assembleCommodityRelateDTO) {
        CommodityRspDto commodityRspDto = new CommodityRspDto();
        commodityRspDto.setBarCode(assembleCommodityRelateDTO.getBarCode());
        commodityRspDto.setErpCode(assembleCommodityRelateDTO.getErpCode());
        commodityRspDto.setExist(DsConstants.INTEGER_ONE);
        commodityRspDto.setMainPic(assembleCommodityRelateDTO.getMainPic());
        commodityRspDto.setManufacture(assembleCommodityRelateDTO.getManufacture());
        commodityRspDto.setApprovalNumber(assembleCommodityRelateDTO.getApprovalNumber());
        commodityRspDto.setName(assembleCommodityRelateDTO.getName());
        commodityRspDto.setSpecJoins(assembleCommodityRelateDTO.getSpecJoins());
        commodityRspDto.setCommodityType(DsConstants.INTEGER_ONE);
        commodityRspDto.setFirstTypeName(assembleCommodityRelateDTO.getFirstTypeName());
        commodityRspDto.setSecondTypeName(assembleCommodityRelateDTO.getSecondTypeName());
        commodityRspDto.setTypeName(assembleCommodityRelateDTO.getTypeName());
        return commodityRspDto;
    }

    private OrderAssembleCommodityRelation constructRelation(AddOrderInfoReqDto addOrderInfoReqDto, AddOrderDetailReqDto detailReqDto, AssembleCommodityRelateDTO assembleCommodityRelateDTO, String orderDetailId, BigDecimal rate) {
        Date now = new Date();
        OrderAssembleCommodityRelation relation = new OrderAssembleCommodityRelation();
        relation.setMerCode(addOrderInfoReqDto.getGroupid());
        relation.setThirdOrderNo(addOrderInfoReqDto.getOlorderno());
        relation.setEctype(addOrderInfoReqDto.getEctype());
        relation.setOrderDetailId(orderDetailId);
        relation.setAssembleCommodityCount(Integer.parseInt(detailReqDto.getNum()));
        relation.setCount(assembleCommodityRelateDTO.getNumber());
        relation.setBarCode(assembleCommodityRelateDTO.getBarCode());
        relation.setErpCode(assembleCommodityRelateDTO.getErpCode());
        relation.setName(assembleCommodityRelateDTO.getName());
        relation.setPrice(assembleCommodityRelateDTO.getPrice());
        relation.setThirdSkuId(detailReqDto.getSku_id());
        relation.setOriginalErpCode(detailReqDto.getOuter_iid());
        relation.setRate(rate);
        relation.setCreateTime(now);
        relation.setModifyTime(now);
        return relation;
    }

}
