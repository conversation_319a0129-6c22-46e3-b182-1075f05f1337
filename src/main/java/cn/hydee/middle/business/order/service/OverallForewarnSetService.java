package cn.hydee.middle.business.order.service;

import cn.hydee.middle.business.order.dto.req.ForewarnSetCreateReqDto;
import cn.hydee.middle.business.order.dto.req.ForewarnSetUpdateReqDto;
import cn.hydee.middle.business.order.dto.rsp.ForewarnSetRspDto;
import cn.hydee.middle.business.order.entity.OrderDetail;
import cn.hydee.middle.business.order.entity.OverallForewarnSet;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

public interface OverallForewarnSetService extends IService<OverallForewarnSet> {

  /**
   * 获取预警设置
   * @return
   */
  List<ForewarnSetRspDto> listForewarnSet();

  /**
   * 创建预警设置
   * @param userId
   * @param createReqDtos
   * @return
   */
  Integer createForewarnSet(String userId, List<ForewarnSetCreateReqDto> createReqDtos);

  /**
   * 更新预警设置
   * @param userId
   * @param updateReqDtos
   * @return
   */
  Integer updateForewarnSet(String userId, List<ForewarnSetUpdateReqDto> updateReqDtos);

  /**
   * O2O预警
   * @param orderDetails
   */
  void sendRobotForewarn(String thirdOrderNo, List<OrderDetail> orderDetails);
}
