package cn.hydee.middle.business.order.Enums;

import lombok.Getter;

/**
 * 线上店铺标签类型 标签类型（SYSTEM_TAG：系统标签，USER_DEFINE_TAG：自定义标签）
 * <AUTHOR>
 * @date 2024/8/9
 * @since 1.0
 */
@Getter
public enum StoreTagTypeEnum {

    SYSTEM_TAG(0,"系统标签"),
    USER_DEFINE_TAG(1,"自定义标签");

    private final Integer code;

    private final String name;

    StoreTagTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
