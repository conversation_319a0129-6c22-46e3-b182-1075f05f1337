package cn.hydee.middle.business.order.elasticsearch.model;

import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 与es字段  一一对应的实体类
 */
@Data
public class OrderDetailUpEsDto extends OrderInfoEsDto implements EsUpdateQueryModel {

    @Override
    public Map<String, Object> queryMap() {
        Map<String, Object> query = new HashMap<>();
        query.put("orderNo",this.getOrderNo());
        return query;
    }

    @Override
    public Map<String, Object> paramMap() {
        Map<String, Object> param = new HashMap<>();
        if(!CollectionUtils.isEmpty(this.getDetailList())){
            List<Map<String,Object>> list = new ArrayList<>();
            for(OrderDetailEsDto esSto : this.getDetailList()){
                Map<String,Object> map = new HashMap<>();
                map.put("erpCode",esSto.getErpCode());
                map.put("commodityName",esSto.getCommodityName());
                map.put("commodityCount",esSto.getCommodityCount());
                map.put("barCode",esSto.getBarCode());
                map.put("directDeliveryType",esSto.getDirectDeliveryType());
                map.put("status",esSto.getStatus());
                list.add(map);
            }
            param.put("detailList", list);
        }else{
            param.put("detailList", new ArrayList<>());
        }
        param.put("commodityCountAll",this.getCommodityCountAll());
        return param;
    }

    private static final  String idOrCode = "ctx._source.detailList = params.detailList;ctx._source.commodityCountAll = params.commodityCountAll; ";

    @Override
    public String getIdOrCode() {
        return idOrCode;
    }
}
