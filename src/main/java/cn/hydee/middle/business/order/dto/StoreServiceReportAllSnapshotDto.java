package cn.hydee.middle.business.order.dto;

import cn.hydee.middle.business.order.entity.OrganizationServiceReport;
import cn.hydee.middle.business.order.util.DateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.text.NumberFormat;
import java.util.Locale;

@Data
public class StoreServiceReportAllSnapshotDto {

    @ApiModelProperty(value = "有效订单总数")
    private Long allValidOrders = 0L;
    @ApiModelProperty(value = "拣货订单总数")
    private Long allPickOrders = 0L;
    @ApiModelProperty(value = "拣货总时间")
    private Long allPickTimeUsed = 0L;
    @ApiModelProperty(value = "拣货超时订单总数")
    private Long allPickOverTimeOrders = 0L;
    @ApiModelProperty(value = "拣货超时订单率")
    private String allPickOvertimePercent = "-";
    @ApiModelProperty(value = "平均拣货时间")
    private String allPickTimeAverage = "-";
    @ApiModelProperty(value = "配送订单总数")
    private Long allDeliveryOrders = 0L;
    @ApiModelProperty(value = "配送总时间")
    private Long allDeliveryTimeUsed = 0L;
    @ApiModelProperty(value = "配送超时订单总数")
    private Long allDeliveryOverTimeOrders = 0L;
    @ApiModelProperty(value = "配送超时订单率")
    private String allDeliveryOvertimePercent = "-";
    @ApiModelProperty(value = "平均配送时间")
    private String allDeliveryTimeAverage = "-";
    @ApiModelProperty(value = "无效订单总数")
    private Long allInvalidOrders = 0L;
    @ApiModelProperty(value = "无效订单率")
    private String allInvalidPercent = "-";
    @ApiModelProperty(value = "缺货订单总数")
    private Long allLackOrders = 0L;
    @ApiModelProperty(value = "缺货订单率")
    private String allLackPercent = "-";

    /**
     * 数据汇总
     * @param entity
     */
    public void summaryData(OrganizationServiceReport entity){
        addValidOrders(entity.getValideOrders().longValue());
        addPickOrders(entity.getPickedOrders());
        addPickTimeUsed(entity.getPickTimeUsed());
        addPickOverTimeOrders(entity.getPickOvertimeOrders().longValue());
        addDeliveryOrders(entity.getDeliveriedOrders());
        addDeliveryTimeUsed(entity.getDeliveryTimeUsed());
        addDeliveryOverTimeOrders(entity.getDeliveryOvertimeOrders().longValue());
        addInvalidOrders(entity.getInvalideOrders().longValue());
        addLackOrders(entity.getLackCommidityOrders().longValue());
    }

    private void addValidOrders(Long increment){
        allValidOrders += increment;
    }

    private void addPickOrders(Long increment){
        allPickOrders += increment;
    }

    private void addPickTimeUsed(Long increment){
        allPickTimeUsed += increment;
    }

    private void addDeliveryOrders(Long increment){
        allDeliveryOrders += increment;
    }

    private void addDeliveryTimeUsed(Long increment){
        allDeliveryTimeUsed += increment;
    }

    private void addPickOverTimeOrders(Long increment){
        allPickOverTimeOrders += increment;
    }

    private void addDeliveryOverTimeOrders(Long increment){
        allDeliveryOverTimeOrders += increment;
    }

    private void addInvalidOrders(Long increment){
        allInvalidOrders += increment;
    }

    private void addLackOrders(Long increment){
        allLackOrders += increment;
    }

    public void calculate(){
        calculatePickOverTimePercent();
        calculateAveragePickTime();
        calculateDeliveryOverTimePercent();
        calculateAverageDeliveryTime();
        calculateInvalidPercent();
        calculateLackPercent();
    }

    private void calculatePickOverTimePercent(){
        if(allValidOrders == 0){
            return;
        }
        NumberFormat numberFormat = NumberFormat.getPercentInstance(Locale.CHINA);
        numberFormat.setMinimumFractionDigits(2);
        allPickOvertimePercent = numberFormat.format((float) allPickOverTimeOrders / (float) allValidOrders);
    }

    private void calculateAveragePickTime(){
        if(allPickOrders == 0){
            return;
        }
        allPickTimeAverage = DateUtil.getTime(allPickTimeUsed/allPickOrders);
    }

    private void calculateDeliveryOverTimePercent(){
        if(allValidOrders == 0){
            return;
        }
        NumberFormat numberFormat = NumberFormat.getPercentInstance(Locale.CHINA);
        numberFormat.setMinimumFractionDigits(2);
        allDeliveryOvertimePercent = numberFormat.format((float) allDeliveryOverTimeOrders / (float) allValidOrders);
    }

    private void calculateAverageDeliveryTime(){
        if(allDeliveryOrders == 0){
            return;
        }
        allDeliveryTimeAverage = DateUtil.getTime(allDeliveryTimeUsed/allDeliveryOrders);
    }

    private void calculateInvalidPercent(){
        if(allValidOrders + allInvalidOrders == 0){
            return;
        }
        NumberFormat numberFormat = NumberFormat.getPercentInstance(Locale.CHINA);
        numberFormat.setMinimumFractionDigits(2);
        allInvalidPercent = numberFormat.format((float) allInvalidOrders / (float) (allValidOrders + allInvalidOrders));
    }

    private void calculateLackPercent(){
        if(allValidOrders + allInvalidOrders == 0){
            return;
        }
        NumberFormat numberFormat = NumberFormat.getPercentInstance(Locale.CHINA);
        numberFormat.setMinimumFractionDigits(2);
        allLackPercent = numberFormat.format((float) allLackOrders / (float) (allValidOrders + allInvalidOrders));
    }

}
