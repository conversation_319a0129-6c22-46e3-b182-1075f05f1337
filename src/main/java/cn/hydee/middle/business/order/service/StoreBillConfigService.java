package cn.hydee.middle.business.order.service;

import cn.hydee.middle.business.order.dto.req.batch.StoreBillConfigInfo;
import cn.hydee.middle.business.order.dto.StoreBillConfigDTO;
import cn.hydee.middle.business.order.dto.StoreBillInit;
import cn.hydee.middle.business.order.dto.StoreQueryBase;
import cn.hydee.middle.business.order.dto.req.ClientBillConfigReqDTO;
import cn.hydee.middle.business.order.dto.rsp.ClientBillConfigRspDTO;
import cn.hydee.middle.business.order.dto.rsp.ClientConfAndPayCodeResDto;
import cn.hydee.middle.business.order.entity.DsOnlineStore;
import cn.hydee.middle.business.order.entity.StoreBillConfig;
import cn.hydee.middle.business.order.storeautosync.constants.StoreBillConfigMode;
import cn.hydee.starter.dto.PageDTO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 商家网店下账配置信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-02
 */
public interface StoreBillConfigService extends IService<StoreBillConfig> {
	
	/**
	 * 获取最新下账配置信息
	 * @param queryBase 门店查询基础对象
	 * @return 门店下账配置信息
	 */
	StoreBillConfig getLatestBillConfig(StoreQueryBase queryBase);

	/**
	 * 新增网店时初始化门店默认配置
	 * @param billInit 初始化配置参数
	 * @return 创建数量
	 */
	int initStoreBillConf(StoreBillInit billInit, DsOnlineStore defaultOnlineStore);

	/**
	 * 创建门店下账配置
	 * @param userId 用户ID
	 * @param clientBillConfig 配置信息
	 * @return true-成功，false-失败
	 */
	Boolean createClientBillConf(String userId, StoreBillConfigInfo clientBillConfig);

	/**
	 * 根据ID查询配置
	 * @param clientConfId
	 * @return
	 */
	StoreBillConfig getBillConfigById(Long clientConfId);

	/**
	 * 分页查询门店下账配置
	 * @param req 请求类
	 * @return 分页数据
	 */
	PageDTO<ClientBillConfigRspDTO> getClientByPage(ClientBillConfigReqDTO req) throws InstantiationException, IllegalAccessException;

	ClientConfAndPayCodeResDto getClientConfigById(Long id);

	ClientConfAndPayCodeResDto queryCurrentUse(StoreQueryBase queryBase);

	/**
	 * (全自动下账配置用)根据线下门店编码查询所有不按批号下账的线上门店的收银员或班次配置
	 * 1. 当对应的所有线上门店都设置了关闭收银员或班次的录入，则对应线下门店机构自动下账的预设值不需要设置收银员或班次；
	 * 2. 当对应的某个或多个线上门店设置了开启收银员或班次的录入，则对应线下门店机构自动下账的预设值必须要设置收银员或班次。
	 * @param merCode
	 * @param organizationCode
	 * @return
	 */
	StoreBillConfigDTO selectByOrganizationCode(String merCode, String organizationCode);

	int initStoreBillConfAuto(StoreBillConfigMode model, StoreBillInit billInit, DsOnlineStore defaultOnlineStore);
}
