package cn.hydee.middle.business.order.dto.rsp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ErpPrescriptionQryRespDto {

    @ApiModelProperty(value = "系统订单号")
    private String orderNo;
    @ApiModelProperty(value = "订单处方信息")
    private List<OrderPrescriptionDetail> prescriptionDetail;

    @Data
    public static class OrderPrescriptionDetail{

        @ApiModelProperty(value = "系统订单号")
        private String orderNo;
        @ApiModelProperty(value = "OMS处方id")
        private String cfId;
        @ApiModelProperty(value = "用药人")
        private String usedrugName;
        @ApiModelProperty(value = "性别 0-男，1-女")
        private Integer sex;
        @ApiModelProperty(value = "用药人身份证号码")
        private String identityNumber;
        @ApiModelProperty(value = "用药人生日")
        private String birthday;
        @ApiModelProperty(value = "用药人联系方式")
        private String phoneNumber;
        @ApiModelProperty(value = "平台编码")
        private String ectype;
        @ApiModelProperty(value = "处方单状态：1-成功，0-失败，2-其他原因")
        private Integer status;
        @ApiModelProperty(value = "处方单描述")
        private String description;
        @ApiModelProperty(value = "审方人")
        private String checkName;
        @ApiModelProperty(value = "处方单类型：1-西药，2-中药")
        private Integer prescriptionType;
        @ApiModelProperty(value = "处方单备注")
        private String remark;
        @ApiModelProperty(value = "用药人年龄")
        private Integer useAge;
        @ApiModelProperty(value = "开方时间")
        private String openTime;
        @ApiModelProperty(value = "审方时间")
        private String checkTime;
        @ApiModelProperty(value = "最近推送审方时间")
        private String lastPushTime;
        @ApiModelProperty(value = "处方图片地址")
        private String cfPicUrl;
        @ApiModelProperty(value = "药事云处方申请单号")
        private String presNo;
        @ApiModelProperty(value = "平台处方id")
        private String rpId;
        @ApiModelProperty(value = "开方医院名称")
        private String hospitalName;
        @ApiModelProperty(value = "开方医生")
        private String doctorName;
        @ApiModelProperty(value = "诊断")
        private String icdName;

    }

}
