package cn.hydee.middle.business.order.mapper;

import cn.hydee.middle.business.order.dto.OrderNoTotalFee;
import cn.hydee.middle.business.order.entity.ErpBillInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;

import java.util.List;

/**
 * <p>
 * ERP下账金额信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-27
 */
@Repository
public interface ErpBillInfoMapper extends BaseMapper<ErpBillInfo> {

    ErpBillInfo getErpBillInfoByOrderNo(Long orderNo);

    void updateBillAmount(@Param("orderNo") Long orderNo,@Param("diff") BigDecimal diff);

    List<OrderNoTotalFee> selectOrderNoTotalFee(@Param("orderNoList") List<Long> tmpOmsAccountOrderNoList);

    List<OrderNoTotalFee> selectSimpleOrderNoTotalFee(@Param("orderNoList") List<Long> omsSideOrderNoList);

    int updateCommodityAveragePrice(@Param("param") ErpBillInfo erpBillInfo);

    int updateBillAmountComprehensive(@Param("param") ErpBillInfo erpBillInfo);

    List<ErpBillInfo> getErpBillInfoByOrderNoList(@Param("orderNoList") List<Long> orderNoList);

    ErpBillInfo getErpBillInfoForB2C(@Param("orderNo") Long orderNo);
}
