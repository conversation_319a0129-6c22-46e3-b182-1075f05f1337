package cn.hydee.middle.business.order.util;


import com.alibaba.fastjson.JSONObject;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 管理okHttpClient
 * <AUTHOR>
 * */
public class HttpManager {
    private static Logger log = LoggerFactory.getLogger(HttpManager.class);

    public final static OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(20, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .connectionPool(new ConnectionPool(50, 10, TimeUnit.SECONDS))
            .build();

    public HttpManager() {

    }

    /**
     * httpget请求方式
     ***/
    public String get(String url) {
        String result = null;
        Request request = new Request.Builder()
                .url(url)
                .build();
        Response response=getHttp(request);
        if(response!=null)
        {
            try {
                result= response.body().string();
            } catch (IOException e) {
                log.error("http get exception,url{}", url,e);
            }
        }

        return result;
    }

    /**
     * http发起请求
     * **/
    private Response getHttp(Request request)
    {
//        log.info("[HTTP REQUEST]time:" + System.currentTimeMillis() + ",url:" + request.url());
        Response response=null;
        Call call = client.newCall(request);
        if (call != null) {
            try {
                response= call.execute();
                if (response.isSuccessful()) {
                    return response;
                }
            } catch (IOException e) {
                log.error("[HTTP REQUEST] exception,url{}", request.url(),e);
            }
        }
        return response;
    }

    /**
     * 带参数get方法
     * **/
    public String get(String url, Map<String, Object> args) {
        log.info("[HTTP请求GET]time:" + System.currentTimeMillis() + ",url:" + url + ",args:" + (args == null ? "" : args.toString()));
        String result = null;
        url+="?"+mapToUrlArg(args);
        Request request = new Request.Builder()
                .url(url)
                .build();
        Response response=getHttp(request);
        try {
            if(response != null){
                result=response.body().string();
            }
        } catch (IOException e) {
            log.error("http get exception,url{}",url,e);
        }
        return result;
    }
    
	/**
	 * 发送POST请求, JSON数据格式
	 * 
	 * @param url      请求URL
	 * @param jsonBody 参数体为 JSON 字符串
	 * @return
	 */
	public String sendPostJson(String url, String jsonBody) {
		return sendPostJson(url, jsonBody, null);
	}

	/**
	 * 发送POST请求, JSON数据格式.带请求头
	 * 
	 * @param url      请求URL
	 * @param jsonBody 参数体为 JSON 字符串
	 * @param headers  请求头Map<String, String>
	 * @return
	 */
	public String sendPostJson(String url, String jsonBody, Map<String, String> headers) {
		String result = null;

		// 请求数据类型：JSON格式
		MediaType mediaType = MediaType.parse("application/json; charset=utf-8");

		// 创建请求体RequestBody
		RequestBody requestBody = RequestBody.create(jsonBody, mediaType);

		// 构建Request.Builder
		Request.Builder builder = new Request.Builder()
				.url(url) // 设置请求URL
				.post(requestBody); // 设置请求体

		// 设置请求头header
		if (headers != null && !headers.isEmpty()) {
			headers.forEach((key, value) -> builder.addHeader(key, value));
		}

		// 获取响应体Response
		Response response = getHttp(builder.build());
		//
		if (response != null) {
			try {
				result = response.body().string();
			} catch (IOException e) {
				log.error("http post-json exception,url{}", url, e);
			}
		}

		return result;
	}


    /**
     * post提交JSON数据格式
     * ***/
    public String postJson(String url, Map<String, Object> args)
    {
        String result=null;
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        JSONObject data = new JSONObject();
        data.putAll(args);
        RequestBody requestBody = RequestBody.create(String.valueOf(data),mediaType);
        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .build();
        Response response =getHttp(request);
        if(response!=null)
        {
            try {
                result=response.body().string();
            } catch (IOException e) {
                log.error("http post-json exception,url{}",url,e);
            }
        }
        return result;
    }

    /**
     * post提交JSON数据格式
     * ***/
    public String postJson(String url, Map<String, Object> args, Headers headers)
    {
        String result=null;
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        JSONObject data = new JSONObject();
        data.putAll(args);
        RequestBody requestBody = RequestBody.create(String.valueOf(data),mediaType);
        Request request = new Request.Builder()
                .headers(headers)
                .url(url)
                .post(requestBody)
                .build();
        Response response =getHttp(request);
        if(response!=null)
        {
            try {
                result=response.body().string();
            } catch (IOException e) {
                log.error("http post-json exception,url{}",url,e);
            }
        }
        return result;
    }

    /**
     * post提交JSON数据格式 带header
     * ***/
    public String postJson(String url, Map<String,Object> args, Map<String,String> headers)
    {
        if (headers == null){
            headers = new HashMap<>();
        }
        String result=null;
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        JSONObject data = new JSONObject();
        data.putAll(args);
        RequestBody requestBody = RequestBody.create(String.valueOf(data),mediaType);
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(requestBody);
        for(Map.Entry<String, String> entry: headers.entrySet()) {
            requestBuilder.addHeader(entry.getKey(), entry.getValue());
        }
        Response response =getHttp(requestBuilder.build());
        if(response!=null)
        {
            try {
                result=response.body().string();
            } catch (IOException e) {
                log.error("http post-json exception,url{}",url,e);
            }
        }
        return result;
    }


    /***
     * 将map转化为http get请求参数
     * **/
    private String mapToUrlArg(Map<String, Object> map){
        if(map==null||map.size()==0){
            return "";
        }
        StringBuilder sb=new StringBuilder();
        for(Map.Entry<String, Object> en:map.entrySet()){
            sb.append(en.getKey()+"="+(en.getValue()==null?"":en.getValue().toString())+"&");
        }
        sb.replace(sb.length()-1,sb.length(),"");
        return sb.toString();
    }

}
