package cn.hydee.middle.business.order.yxtadapter.infrastructure.domainserviceimpl.hdpos;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.Enums.OrderDetailStatusEnum;
import cn.hydee.middle.business.order.Enums.RefundStateEnum;
import cn.hydee.middle.business.order.domain.OrderDetailDomain;
import cn.hydee.middle.business.order.dto.req.tracecode.MedicalTraceCodeCheckReqDto;
import cn.hydee.middle.business.order.dto.rsp.GoodMedicalTraceCodeDTO;
import cn.hydee.middle.business.order.dto.rsp.MedicalTraceCodeDTOCheckResDTO;
import cn.hydee.middle.business.order.entity.OrderDetail;
import cn.hydee.middle.business.order.entity.RefundDetail;
import cn.hydee.middle.business.order.entity.RefundOrder;
import cn.hydee.middle.business.order.entity.MedicalTraceCode;
import cn.hydee.middle.business.order.mapper.OrderDetailMapper;
import cn.hydee.middle.business.order.mapper.RefundDetailMapper;
import cn.hydee.middle.business.order.mapper.RefundOrderMapper;
import cn.hydee.middle.business.order.mapper.MedicalTraceCodeMapper;
import cn.hydee.middle.business.order.service.MedicalTraceCodeService;
import cn.hydee.middle.business.order.yxtadapter.domain.pos.hdpos.OrderDetailDto;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/11/5 13:53
 */
@Slf4j
public class AccountingEcodeHelper {
    public AccountingEcodeHelper(MedicalTraceCodeMapper medicalTraceCodeMapper, RefundOrderMapper refundOrderMapper, RefundDetailMapper refundDetailMapper, MedicalTraceCodeService medicalTraceCodeService, OrderDetailMapper orderDetailMapper) {
        this.medicalTraceCodeMapper = medicalTraceCodeMapper;
        this.refundOrderMapper = refundOrderMapper;
        this.refundDetailMapper = refundDetailMapper;
        this.medicalTraceCodeService = medicalTraceCodeService;
        this.orderDetailMapper = orderDetailMapper;
    }

    private final MedicalTraceCodeMapper medicalTraceCodeMapper;
    private final RefundOrderMapper refundOrderMapper;
    private final OrderDetailMapper orderDetailMapper;
    private final RefundDetailMapper refundDetailMapper;
    private final MedicalTraceCodeService medicalTraceCodeService;
    Integer count;
    Integer allRefundCount;
    boolean checked;
    boolean refundChecked;
    Map<String, List<MedicalTraceCode>> medicalTraceCodeMap;

    public void setZhuiShuMa(OrderDetailDto dto, OrderDetailDomain orderDetail,HashMap<String, List<MedicalTraceCode>> hm) {
        //正单追溯码为空什么都不处理
        if(DsConstants.INTEGER_ZERO.compareTo(getZhengCount(orderDetail.getOrderNo()))==0){
            return;
        }
        //全部退,不会下账所以不做处理
        if(DsConstants.INTEGER_ZERO.compareTo(allRefundCount(orderDetail.getOrderNo()))< 0){
            return;
        }
        initNeedCheck(orderDetail.getOrderNo());
        //校验逆向单追溯码是否填写
        checkRefundZhuiShuMa(orderDetail.getOrderNo());
        //设置追溯码
        setZhuiShuMaEcode(dto,orderDetail,hm);
    }

    private Integer getZhengCount(Long orderNo){
        if(this.count==null){
            this.count = medicalTraceCodeMapper.queryOrderTraceCodeCount(orderNo);
        }
        return this.count;
    }

    private Integer allRefundCount(Long orderNo){
        if(this.allRefundCount==null){
            this.allRefundCount = refundOrderMapper.selectCount(Wrappers.<RefundOrder>lambdaQuery()
                    .eq(RefundOrder::getOrderNo,orderNo)
                    //全额退款
                    .eq(RefundOrder::getType, DsConstants.STRING_ONE));
        }
        return this.allRefundCount;
    }

    /**
     * 校验逆向单追溯码是否填写
     * @param orderNo
     */
    private void checkRefundZhuiShuMa(Long orderNo) {
        if(this.refundChecked){
            return;
        }
        List<RefundOrder> refundOrders =  refundOrderMapper.queryRefundOrder(orderNo);
        //没有退款直接返回
        if(CollectionUtils.isEmpty(refundOrders)){
            return;
        }
        List<Long> refundNos = refundOrders.stream().map(RefundOrder::getRefundNo).collect(Collectors.toList());
        List<RefundDetail> refundDetails = refundDetailMapper.selectListByRefundNoList(refundNos);
        //没有退款直接返回
        if(CollectionUtils.isEmpty(refundDetails)){
            return;
        }
        Map<Long, List<RefundDetail>> refundDetailMap = refundDetails.stream().collect(Collectors.groupingBy(RefundDetail::getId));
        Map<Long,Integer> refundCount = convert(refundDetails);

        List<MedicalTraceCode> allMedicalTraceCodes = queryAllOrderMedicalTraceCode(orderNo);
        Map<Long,Integer> medicalTraceCodeCount = convertMedicalTraceCodeCount(allMedicalTraceCodes);
        Map<Long, RefundOrder> refundOrderMap = refundOrders.stream().collect(Collectors.toMap(RefundOrder::getRefundNo, Function.identity()));
        for (Map.Entry<Long, Integer> entry : refundCount.entrySet()) {
            List<RefundDetail> refundDetails1 = refundDetailMap.get(entry.getKey());
            if(CollectionUtils.isEmpty(refundDetails1)){
                continue;
            }
            if(!needCheck(orderNo,refundDetails1.get(0).getErpCode())){
                log.info("追溯码 不需要校验追溯码 orderNo:{} erpCode:{}",orderNo,refundDetails1.get(0).getErpCode());
                continue;
            }
            Integer i = medicalTraceCodeCount.get(entry.getKey());
            Integer value = entry.getValue();
            RefundOrder refundOrder = refundOrderMap.get(refundDetails1.get(0).getRefundNo());
            if(!RefundStateEnum.SUCCESS.getCode().equals(refundOrder.getState()) && ObjectUtils.compare(i, value)!=0){
                log.error("追溯码 错误,下账失败 orderNo:{} 退款商品：refundDetailId:{} 退款数量:{} 追溯码数量:{}",orderNo,entry.getKey(),value,i==null?0:i);
                throw new MedicalTraceCodeException("追溯码录入数量小于退款数量,退款单需要录入追溯码");
            }
        }
        this.refundChecked = true;
    }
    private List<MedicalTraceCode> allMedicalTraceCodes;
    private List<MedicalTraceCode> queryAllOrderMedicalTraceCode(Long orderNo){
        if(allMedicalTraceCodes==null){
            allMedicalTraceCodes = medicalTraceCodeMapper.queryAllOrderTraceCode(orderNo);
        }
        return allMedicalTraceCodes;
    }


    private Map<Long, Integer> convertMedicalTraceCodeCount(List<MedicalTraceCode> MedicalTraceCodes) {
        HashMap<Long, Integer> hm = new HashMap<>();
        Map<Long, List<MedicalTraceCode>> collect = MedicalTraceCodes.stream()
                .filter(medicalTraceCode -> DsConstants.INTEGER_ONE.equals(medicalTraceCode.getType()))
                .filter(medicalTraceCode -> medicalTraceCode.getRefundDetailId()!=null)
                .collect(Collectors.groupingBy(MedicalTraceCode::getRefundDetailId));
        for (Map.Entry<Long, List<MedicalTraceCode>> entry : collect.entrySet()) {
            hm.put(entry.getKey(),entry.getValue().size());
        }
        return hm;
    }

    private Map<Long, Integer> convert(List<RefundDetail> refundDetails) {
        Map<Long, List<RefundDetail>> collect = refundDetails.stream().collect(Collectors.groupingBy(RefundDetail::getId));
        HashMap<Long, Integer> hm = new HashMap<>();
        for (Map.Entry<Long, List<RefundDetail>> entry : collect.entrySet()) {
            Integer i = hm.get(entry.getKey());
            if(i==null){
                 i = 0;
            }
            List<RefundDetail> value = entry.getValue();
            for (RefundDetail refundDetail : value) {
                i = i+refundDetail.getRefundCount();
            }
            hm.put(entry.getKey(),i);
        }
        return hm;
    }

    private void setZhuiShuMaEcode(OrderDetailDto dto, OrderDetailDomain orderDetail ,HashMap<String, List<MedicalTraceCode>> map) {
        List<MedicalTraceCode> medicalTraceCodeList = map.get(orderDetail.getId()+"_"+dto.getBatchNo());
        if(CollectionUtils.isEmpty(medicalTraceCodeList)){
            log.info("追溯码 正单下账记录 没有匹配到追溯码 orderDetailId:{} batchNo:{}",orderDetail.getId(),dto.getBatchNo());
            return;
        }
        if(DsConstants.INTEGER_ONE.equals(medicalTraceCodeList.get(0).getFlag())){
            dto.setIsyb(DsConstants.INTEGER_ONE);
            dto.setIsyj(DsConstants.INTEGER_ZERO);
        }else if(DsConstants.INTEGER_TWO.equals(medicalTraceCodeList.get(0).getFlag())){
            dto.setIsyb(DsConstants.INTEGER_ZERO);
            dto.setIsyj(DsConstants.INTEGER_ONE);
        }else if(DsConstants.INTEGER_THREE.equals(medicalTraceCodeList.get(0).getFlag())){
            dto.setIsyb(DsConstants.INTEGER_ONE);
            dto.setIsyj(DsConstants.INTEGER_ONE);
        }
        Set<String> medicalTraceCodeSet =  new HashSet<>();

        for(int i = 0; i <dto.getGoodsCount();i++){
            if(medicalTraceCodeList.isEmpty()){
                break;
            }
            MedicalTraceCode medicalTraceCode = medicalTraceCodeList.get(0);
            if(null!=medicalTraceCode){
                medicalTraceCodeSet.add(medicalTraceCode.getEcode());
                medicalTraceCodeList.remove(medicalTraceCode);
            }

        }
        map.put(orderDetail.getId()+"_"+dto.getBatchNo(),medicalTraceCodeList);
        String eCodes = medicalTraceCodeSet.stream().collect(Collectors.joining(","));
        if(eCodes.contains("00000000000000000000")){
            dto.setIsyb(DsConstants.INTEGER_ZERO);
            dto.setIsyj(DsConstants.INTEGER_ZERO);
            eCodes="";
        }else{
            Preconditions.checkArgument(medicalTraceCodeSet.size()==dto.getGoodsCount(),"追溯码数量和商品数量不一致");
        }

        log.info("追溯码 正单下账记录 orderDetailId:{} batchNo:{} MedicalTraceCode:{}",orderDetail.getId(),dto.getBatchNo(),eCodes);
        dto.setEcode(eCodes);
    }

    private Map<String, List<MedicalTraceCode>> queryMedicalTraceCode(Long orderNo) {
        if(this.medicalTraceCodeMap==null){
            List<MedicalTraceCode> medicalTraceCodes = medicalTraceCodeMapper.queryAllOrderTraceCode(orderNo);
            Map<String, List<MedicalTraceCode>> collect = medicalTraceCodes.stream().collect(Collectors.groupingBy(medicalTraceCode -> medicalTraceCode.getOrderDetailId()+"_"+medicalTraceCode.getCommodityBatchNo()));
            HashMap<String, List<MedicalTraceCode>> hm = new HashMap<>();
            for (Map.Entry<String, List<MedicalTraceCode>> entry : collect.entrySet()) {
                hm.put(entry.getKey(),convertMedicalTraceCode(entry.getValue()));
            }
            this.medicalTraceCodeMap = hm;
        }
        return this.medicalTraceCodeMap;
    }

    /**
     * 将逆向单数据抵扣掉正向单数据
     * @param value
     * @return
     */
    private List<MedicalTraceCode> convertMedicalTraceCode(List<MedicalTraceCode> value) {
        HashMap<String, MedicalTraceCode> hm = new HashMap<>();

        for (MedicalTraceCode MedicalTraceCode : value) {
            if(DsConstants.INTEGER_ZERO.equals(MedicalTraceCode.getType())){
                hm.put(MedicalTraceCode.getEcode(),MedicalTraceCode);
            }
        }
        for (MedicalTraceCode MedicalTraceCode : value) {
            if(DsConstants.INTEGER_ONE.equals(MedicalTraceCode.getType())){
                hm.remove(MedicalTraceCode.getEcode());
            }
        }
        return new ArrayList<>(hm.values());
    }

    Set<String> needRecordErpCodes;
    private boolean needCheck(Long orderNo,String erpCode){
        return needRecordErpCodes.contains(erpCode);
    }

    private void initNeedCheck(Long orderNo){
        if(needRecordErpCodes==null){
            List<OrderDetail> orderDetailList = orderDetailMapper.selectList(Wrappers.<OrderDetail>lambdaQuery()
                    .select(OrderDetail::getErpCode,OrderDetail::getId)
                    .eq(OrderDetail::getStatus, OrderDetailStatusEnum.NORMAL.getCode())
                    .eq(OrderDetail::getOrderNo, orderNo));
            Set<String> erpCodes = orderDetailList.stream().map(OrderDetail::getErpCode).collect(Collectors.toSet());

            MedicalTraceCodeCheckReqDto medicalTraceCodeCheckReqDto = new MedicalTraceCodeCheckReqDto();
            medicalTraceCodeCheckReqDto.setType(0);
            medicalTraceCodeCheckReqDto.setOrderNo(String.valueOf(orderNo));
            medicalTraceCodeCheckReqDto.setErpCodeList(erpCodes);
            MedicalTraceCodeDTOCheckResDTO dto = medicalTraceCodeService.needRecord(medicalTraceCodeCheckReqDto);
            if(org.apache.commons.collections.CollectionUtils.isNotEmpty(dto.getNeedRecords())){
                List<GoodMedicalTraceCodeDTO> needRecords = dto.getNeedRecords();
                needRecordErpCodes = needRecords.stream().map(GoodMedicalTraceCodeDTO::getErpCode).collect(Collectors.toSet());
            }else {
                needRecordErpCodes = new HashSet<>(0);
            }
        }
    }

}
