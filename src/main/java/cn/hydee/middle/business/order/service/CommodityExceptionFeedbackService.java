package cn.hydee.middle.business.order.service;

import cn.hydee.middle.business.order.dto.order.over.sold.OrderOverSoldFeedbackReqDto;
import cn.hydee.middle.business.order.entity.CommodityExceptionFeedback;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 异常商品原因反馈表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
public interface CommodityExceptionFeedbackService extends IService<CommodityExceptionFeedback> {

    void feedback(OrderOverSoldFeedbackReqDto reqDto);
}
