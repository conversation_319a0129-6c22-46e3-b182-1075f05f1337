package cn.hydee.middle.business.order.dto.req;

import cn.hydee.starter.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/4/14 上午9:55
 */
@Data
public class UpdateLogQueryReqDTO extends PageBase {

    @NotNull(message = "商户编码不能为空")
    @ApiModelProperty(value = "商户编码")
    private String merCode;
    @ApiModelProperty(value = "类型 1店铺配置，2订单处理设置，3自配送设置，4提示音设置")
    private Integer type;
    @NotNull(message = "业务主键不能为空")
    @ApiModelProperty(value = "业务主键 如onlineStoreId")
    private String targetKey;
    @ApiModelProperty(value = "开始时间")
    private Date beginTime;
    @ApiModelProperty(value = "结束时间")
    private Date endTime;
}
