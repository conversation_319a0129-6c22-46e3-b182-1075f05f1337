package cn.hydee.middle.business.order.dto.rsp;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 模板信息返回
 * @Author: chufeng(2910)
 * @Date: 2021/7/14 17:14
 */
@Data
public class TemplateInfoDTO {

    @ApiModelProperty(value = "订单状态")
    private Long templateId;

    @ApiModelProperty(value = "1-系统默认模板  2-拣货后打印的模板")
    private Integer templateType;

    @ApiModelProperty(value = "模板名称")
    private String templateName;
}