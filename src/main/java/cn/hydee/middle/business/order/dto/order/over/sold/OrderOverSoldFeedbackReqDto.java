package cn.hydee.middle.business.order.dto.order.over.sold;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/04/18
 */
@Data
public class OrderOverSoldFeedbackReqDto{

    @ApiModelProperty(value = "商户编码",hidden = true)
    private String merCode;

    @ApiModelProperty(value = "商户编码",hidden = true)
    private String userId;

    @ApiModelProperty(value = "商户编码",hidden = true)
    private String userName;

    @ApiModelProperty(value = "反馈时间",hidden = true)
    private Date feedbackTime;

    @ApiModelProperty(value = "超卖主键ID")
    @NotNull(message = "超卖主键id不能为空")
    private Long commodityExceptionId;

    @ApiModelProperty(value = "原因")
    @NotEmpty(message = "反馈原因不能为空")
    @Size(max = 1024,message = "反馈原因长度非法")
    private String reason;

}
