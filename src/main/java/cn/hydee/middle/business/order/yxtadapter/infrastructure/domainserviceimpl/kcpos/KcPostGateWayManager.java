package cn.hydee.middle.business.order.yxtadapter.infrastructure.domainserviceimpl.kcpos;

import cn.hutool.core.util.ObjectUtil;
import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.entity.RefundOrder;
import cn.hydee.middle.business.order.entity.b2c.OmsOrderInfo;
import cn.hydee.middle.business.order.mapper.RefundOrderMapper;
import cn.hydee.middle.business.order.mapper.b2c.OmsOrderInfoMapper;
import cn.hydee.middle.business.order.repository.AccountOrderRepository;
import cn.hydee.middle.business.order.repository.AccountRefundRepository;
import cn.hydee.middle.business.order.util.RedisKeyUtil;
import cn.hydee.middle.business.order.yxtadapter.domain.pos.callback.getorderlist.KcPosOrderSimpleInfo;
import cn.hydee.middle.business.order.yxtadapter.domain.pos.callback.getreturnorderlist.KcPosReturnOrderSimpleInfo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 科传下账管理器
 *
 * <AUTHOR>
 * @date 2024/06/04 16:35
 **/
@Service
@RequiredArgsConstructor
public class KcPostGateWayManager {

    private final AccountOrderRepository accountOrderRepository;
    private final AccountRefundRepository accountRefundRepository;
    private final StringRedisTemplate stringRedisTemplate;
    private final OmsOrderInfoMapper omsOrderInfoMapper;
    private final RefundOrderMapper refundOrderMapper;
    @Value("${kc.pos.account.cache.time:300}")
    private Long accountCacheTime;

    public void cacheOrderList(List<KcPosOrderSimpleInfo> orderList) {
        if(CollectionUtils.isEmpty(orderList)){return;}
        orderList.forEach(order->{
            if(Objects.nonNull(order) && ObjectUtil.isNotEmpty(order.getORDERCODE()) && ObjectUtil.isNotEmpty(order.getORDERTYPENAME()) ){
                stringRedisTemplate.opsForValue().set(RedisKeyUtil.getRedisKeyAccountOrderCache(order.getORDERCODE()), order.getORDERTYPENAME(), accountCacheTime, TimeUnit.SECONDS);
            }
        });


    }

    /**
     * 获取订单类型
     * */
    public String obtainOrderType(String ordercode) {
        //先获取缓存
//        String type = stringRedisTemplate.opsForValue().get(RedisKeyUtil.getRedisKeyAccountOrderCache(ordercode));
//        if (type != null) {
//            return type;
//        }

        //缓存获取不到， 查询下账单
//        AccountOrder accountOrder = accountOrderRepository.getByOrderNo(Long.valueOf(ordercode));
//        if (accountOrder != null) {
//            return accountOrder.getServiceMode();
//        }
        OmsOrderInfo omsOrderInfo = omsOrderInfoMapper.selectOne(new QueryWrapper<OmsOrderInfo>().lambda().eq(OmsOrderInfo::getOmsOrderNo, ordercode));
        if(Objects.nonNull(omsOrderInfo)){
            return DsConstants.B2C;
        }
        //下账单不存在返回O2O， 目前只有B2C订单先接入下账单 2024-06-04
        return DsConstants.O2O;
    }

    public void cacheRefundList(List<KcPosReturnOrderSimpleInfo> refundList) {
        refundList.forEach(refund->
            stringRedisTemplate.opsForValue().set(RedisKeyUtil.getRedisKeyAccountRefundCache(refund.getRETURNCODE()), refund.getORDERTYPENAME(), accountCacheTime, TimeUnit.SECONDS));
    }

    public String obtainRefundType(String returncode) {
        //先获取缓存
//        String type = stringRedisTemplate.opsForValue().get(RedisKeyUtil.getRedisKeyAccountRefundCache(returncode));
//        if (type != null) {
//            return type;
//        }
//        AccountRefund accountRefund = accountRefundRepository.getByRefundNo(Long.valueOf(returncode));
//        if (accountRefund != null) {
//            return accountRefund.getServiceMode();
//        }
        RefundOrder refundOrder = refundOrderMapper.selectByRefundNo(Long.parseLong(returncode));
        if(Objects.nonNull(refundOrder) && DsConstants.B2C.equals(refundOrder.getServiceMode())){
            return DsConstants.B2C;
        }
        //下账单不存在返回O2O， 目前只有B2C订单先接入下账单 2024-06-04
        return DsConstants.O2O;
    }
}
