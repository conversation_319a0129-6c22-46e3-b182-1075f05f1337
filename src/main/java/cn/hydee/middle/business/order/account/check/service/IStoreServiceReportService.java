
package cn.hydee.middle.business.order.account.check.service;

import cn.hydee.middle.business.order.account.check.base.dto.req.QueryOrganizationServiceReportReqDto;
import cn.hydee.middle.business.order.account.check.base.dto.req.QueryStoreServiceReportReqDto;
import cn.hydee.middle.business.order.dto.MerOrganizationCode;
import cn.hydee.middle.business.order.dto.StoreServiceReportExportReqDto;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.entity.OrganizationServiceReport;
import cn.hydee.middle.business.order.entity.StoreServiceReport;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.List;

public interface IStoreServiceReportService extends IService<StoreServiceReport> {

	IPage<StoreServiceReport> qryStoreServiceReportByOrg(QueryStoreServiceReportReqDto req);

    int countReport(QueryStoreServiceReportReqDto reqDTO);

	List<StoreServiceReport> qryOnlineStoreServiceReportByOrg(StoreServiceReportExportReqDto reqDTO);

	StoreServiceReport report(List<OrderInfo> orderInfos, String integrateKey, LocalDateTime statisticsDate, String jobId);

	OrganizationServiceReport qryOrganizationReport(MerOrganizationCode merOrg, String ytd);

    void reCountValideOrder(long minDays);

	int getValidOrderCount(OrganizationServiceReport organizationServiceReport);

    void deleteByDay(LocalDateTime statisticDate);

	IPage<OrganizationServiceReport> qryOrganizationServiceReport(QueryOrganizationServiceReportReqDto req);

	int countOrganizationServiceReport(QueryOrganizationServiceReportReqDto reqDTO);
}
  
