package cn.hydee.middle.business.order.doris.dto.circle;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/06/21
 */
@Data
public class CircleOrderBillCommonDto implements Serializable {
    
    private static final long serialVersionUID = -3765770956762134031L;
    
    @ApiModelProperty(value = "已下账订单数")
    private CircleDataDto<Integer> orderCount;
    @ApiModelProperty(value = "下账总金额")
    private CircleDataDto<BigDecimal> billTotalAmount;
    @ApiModelProperty(value = "下账商品金额")
    private CircleDataDto<BigDecimal> commodityBillTotal;
    @ApiModelProperty(value = "商品成本")
    private CircleDataDto<BigDecimal> commodityCostTotal;
    @ApiModelProperty(value = "配置参与计算的下账金额")
    private CircleDataDto<BigDecimal> billAmountComprehensive;
    @ApiModelProperty(value = "商家配送费")
    private CircleDataDto<BigDecimal> merchantDeliveryFee;
    @ApiModelProperty(value = "平台配送费")
    private CircleDataDto<BigDecimal> platformDeliveryFee;
    @ApiModelProperty(value = "商家包装费")
    private CircleDataDto<BigDecimal> merchantPackFee;
    @ApiModelProperty(value = "平台包装费")
    private CircleDataDto<BigDecimal> platformPackFee;
    @ApiModelProperty(value = "平台佣金")
    private CircleDataDto<BigDecimal> platBrokerageAmount;
    @ApiModelProperty(value = "商家优惠金额")
    private CircleDataDto<BigDecimal> merchantDiscount;
    @ApiModelProperty(value = "平台优惠金额")
    private CircleDataDto<BigDecimal> platformDiscount;
    @ApiModelProperty(value = "商家明细优惠金额")
    private CircleDataDto<BigDecimal> discountAmount;
}
