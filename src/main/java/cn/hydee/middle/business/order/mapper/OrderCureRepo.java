package cn.hydee.middle.business.order.mapper;

import cn.hydee.middle.business.order.entity.OrderCure;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * o2o平台门店信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-31
 */
@Repository
public interface OrderCureRepo extends BaseMapper<OrderCure> {
    OrderCure findLastCureRecord(Long orderNo);
}
