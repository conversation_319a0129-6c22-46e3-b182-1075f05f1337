package cn.hydee.middle.business.order.route.application.command;

import cn.hydee.middle.business.order.entity.OrderDeliveryAddress;
import cn.hydee.middle.business.order.entity.OrderDetail;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.entity.OrderPayInfo;
import java.util.List;
import lombok.Data;

/**
 * 路由规则校验指令
 * 暂时先继承OrderInfo
 * <AUTHOR>
 * @date 2024/03/28 14:54
 **/
@Data
public class RouteRuleCheckCommand extends OrderInfo {
    /**
     * 店铺Id
     * */
    private long dsOnlineStoreId;

    /**
     * 商品明细
     * */
    private List<OrderDetail> orderDetailList;


    /**
     * 收获人地址信息
     * */
    private OrderDeliveryAddress orderDeliveryAddress;

    /**
     * 财务信息
     * */
    private OrderPayInfo orderPayInfo;
}
