package cn.hydee.middle.business.order.batch.export.setting.service;


import cn.hydee.middle.business.order.batch.export.setting.dto.UserExportColumnReqDTO;
import cn.hydee.middle.business.order.batch.export.setting.dto.UserExportColumnResDTO;
import cn.hydee.middle.business.order.entity.UserExportColumn;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 用户与导出订单列关系表 业务逻辑的接口类 <br>
 */
public interface UserExportColumnService extends IService<UserExportColumn> {

    /**
     * 查询用户配置的列，没有过滤
     *
     * @param userId
     * @param type
     */
    List<UserExportColumnResDTO> queryUserConfig(String userId, Integer type);

    boolean saveOrUpdateUserConfig(String userId, UserExportColumnReqDTO exportColumns);

    /**
     * 查询用户需要导出的列 已过滤并排好序
     *
     * @param userId
     * @param type
     */
    List<UserExportColumnResDTO> queryUserColumnWithSeqForExport(String userId, Integer type);
}
