  
package cn.hydee.middle.business.order.service.rabbit.delayConsumer.base;  
/**  
 * 延迟消费- 消息类型 
 * <AUTHOR>  
 */
public enum MsgTypeServiceEnum {
	
	/** 释放库存 */
	UNLOCK_INVENTORY("unlock_inventory","unlockInventoryBusinessLogicHandelService",4),
	/** 退款前置延迟处理 */
	REFUND_FRONT("refund_front","refundFrontBusineLogicHandelService",4),
	/** 订单异步操作延迟处理 */
	ORDER_SYNC_OP("order_operate","orderOperateHandelService",1),
	/** 订单拣货操作延迟处理 */
	ORDER_PICK("order_pick2platform","orderPick2PlatformHandelService",1),
	/** 锁库存网络不通重试延迟处理 */
	LOCK_STOCK_RETRY("lock_stock_retry","lockStockRetryHandelService",4),
	/** 下账网络不通重试延迟处理 */
	ENTER_ACCOUNT_RETRY("enter_account_retry","enterAccountRetryHandelService",1),
	/** 获取部分退款佣金重试 */
	GET_PARTREFUND_COMMISSION_RETRY("get_partrefund_commission_retry","getPartRefundCommissionRetryService",1),
	/** 预约单延迟呼叫骑手重试 */
	NO_JD_HEALTH_CALL_RIDER_RETRY("no_jd_health_call_rider_retry","noJdHealthCallRiderRetryHandelService",3),
	;
	
	private String type;
	private String serviceName;
	// 死信队列数量，最大四个
	private Integer ttlQueueCount;
	
	MsgTypeServiceEnum(String type,String serviceName,Integer ttlQueueCount){
		this.type = type;
		this.serviceName = serviceName;
		this.ttlQueueCount = ttlQueueCount;
	}
	
	public String getType() {
		  
		return this.type;
	}
	public String getServiceName() {
		  
		return this.serviceName;
	}
	public Integer getTtlQueueCount() {
		  
		return this.ttlQueueCount;
	}
}
  
