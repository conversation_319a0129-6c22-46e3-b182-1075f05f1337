package cn.hydee.middle.business.order.storeautosync.domain.storeconfig.entity;

import lombok.Data;

@Data
public class StoreAutoConfigDTO {

    private Long id;

    /**
     * 商户编码
     */
    private String merCode;

    /**
     * o2o平台标记
     */
    private String platformCode;

    /**
     * o2o平台名
     */
    private String platformName;

    /**
     * 网店编码
     */
    private String onlineClientCode;

    /**
     * 网店名称
     */
    private String onlineClientName;

    /**
     * 门店编码
     */
    private String onlineStoreCode;

    /**
     * 门店名称
     */
    private String onlineStoreName;

    /**
     * 服务模式
     */
    private String serviceMode;

    /**
     * 配送方式
     *
     * @see cn.hydee.middle.business.order.storeautosync.constants.StoreBillConfigMode#code
     */
    private String deliveryModel;

    /**
     * 平台侧门店编码
     */
    private String platformShopId;

}
