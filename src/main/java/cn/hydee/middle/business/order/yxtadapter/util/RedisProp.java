package cn.hydee.middle.business.order.yxtadapter.util;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "spring.redis")
public class RedisProp {

    private String host;

    private String port;

    private String password;

    private Integer database;

    private Integer redissonDatabase;

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Integer getDatabase() {
        return database;
    }

    public void setDatabase(Integer database) {
        this.database = database;
    }

    public Integer getRedissonDatabase() {
        return redissonDatabase;
    }

    public void setRedissonDatabase(Integer redissonDatabase) {
        this.redissonDatabase = redissonDatabase;
    }

    @Override
    public String toString() {
        return "RedisProp{" +
                "host='" + host + '\'' +
                ", port='" + port + '\'' +
                ", password='" + password + '\'' +
                ", database=" + database +
                ", redissonDatabase=" + redissonDatabase +
                '}';
    }
}
