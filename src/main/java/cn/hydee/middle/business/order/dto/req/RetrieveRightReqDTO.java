package cn.hydee.middle.business.order.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RetrieveRightReqDTO {

  @ApiModelProperty(value = "退单号")
  private String orderNo;

  @ApiModelProperty(value = "退单号对应的正向单号")
  private String sourceOrderNo;

  @ApiModelProperty(value = "分类id")
  private String uniqueIdentification;

  @ApiModelProperty(value = "用户id")
  private String userId;

}
