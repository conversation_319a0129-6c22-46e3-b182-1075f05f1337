package cn.hydee.middle.business.order.dto.req;

import cn.hydee.starter.dto.PageBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "分页查询节约配送费记录参数对象")
public class PageDeliveryFeeEconomizeReqDto extends PageBase {

  @ApiModelProperty(value = "子公司编码")
  private String subCompanyCode;

  @ApiModelProperty(value = "门店编码")
  private String storeCode;

  @ApiModelProperty(value = "三方平台编码")
  private String thirdPlatformCode;

  @ApiModelProperty(value = "三方平台订单号")
  private String thirdOrderNo;

  @ApiModelProperty(value = "系统订单号")
  private String orderNo;

  @ApiModelProperty(value = "订单实际开始创建时间")
  private String orderBeginCreated;

  @ApiModelProperty(value = "订单实际结束创建时间")
  private String orderEndCreated;

}
