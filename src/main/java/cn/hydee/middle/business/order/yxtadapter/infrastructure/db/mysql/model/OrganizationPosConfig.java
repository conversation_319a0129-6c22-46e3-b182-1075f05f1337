package cn.hydee.middle.business.order.yxtadapter.infrastructure.db.mysql.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Data;

@Data
@TableName("ds_organization_pos_config")
public class OrganizationPosConfig {

  @TableId(type = IdType.AUTO)
  private String id;

  private String organizationCode;

  private String organizationName;

  private Integer organizationType;

  private String parentOrganizationCode;

  private Integer posMode;

  private String posUrl;

  private LocalDateTime createTime;

  private LocalDateTime modifyTime;

  private String createBy;

  private String updateBy;
}
