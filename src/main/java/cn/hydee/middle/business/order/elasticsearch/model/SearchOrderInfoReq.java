package cn.hydee.middle.business.order.elasticsearch.model;

import cn.hydee.starter.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 根据最终id查询线上订单信息
 * @Author: chuf<PERSON>(2910)
 * @Date: 2021/10/21 15:55
 */
@Data
public class SearchOrderInfoReq extends PageBase {

    @ApiModelProperty("结束id号")
    private Long lastId;

    @ApiModelProperty("开始id号")
    private Long beginId;

    @ApiModelProperty("限制条数")
    private Integer limit;

}
