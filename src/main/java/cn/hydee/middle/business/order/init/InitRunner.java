
package cn.hydee.middle.business.order.init;

import cn.hydee.middle.business.order.configuration.RabbitConfig;
import cn.hydee.middle.business.order.service.PlatformPayModeRelationService;
import cn.hydee.middle.business.order.service.TemplateInfoService;
import cn.hydee.middle.business.order.service.rabbit.delayConsumer.base.MsgTypeServiceEnum;
import cn.hydee.middle.business.order.util.RabbitUtil;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.stream.Stream;

/**  
 * ClassName:InitRunner <br/>  
 * Date:     2020年12月7日 上午11:20:59 <br/>  
 * <AUTHOR>  
 */
@Component
@Order(value=1)
public class InitRunner implements ApplicationRunner {
	@Autowired
	private RabbitUtil rabbitUtil;
	@Autowired
	private TemplateInfoService templateInfoService;
	@Autowired
	private PlatformPayModeRelationService platformPayModeRelationService;
    
    private void addQueueAndBiding(Queue queue, DirectExchange exchange, String routingKey) {
    	// 声明预备队列
		rabbitUtil.addQueue(queue);
	    // 绑定预备队列到交换机，路由Key设置
		rabbitUtil.addBinding(queue, exchange, routingKey);
    }
    
	@Override
	public void run(ApplicationArguments args) throws Exception {
		// 初始化死信队列
		Stream.of(MsgTypeServiceEnum.values()).forEach(enums -> {{
			// 声明预备队列,绑定预备队列到交换机，路由Key设置
			DirectExchange exchange = RabbitConfig.NOTIFY_UNLOCK_INVENTORY_EXCHANGE; 
			String typeStr = enums.getType();
			Integer ttlQueueCount = enums.getTtlQueueCount();
			if(1 <= ttlQueueCount) {
				addQueueAndBiding(
						new Queue(
							RabbitConfig.formatStr(RabbitConfig.OMS_DELAY_CONSUMER_QUEUE_READY_ONE,typeStr), true, false, 
								false, RabbitConfig.buildQueueArguments()
						), 
						exchange, 
						RabbitConfig.formatStr(RabbitConfig.OMS_DELAY_CONSUMER_KEY_READY_FIRST, typeStr));
			}
			if(2 <= ttlQueueCount) {
				addQueueAndBiding(
						new Queue(
							RabbitConfig.formatStr(RabbitConfig.OMS_DELAY_CONSUMER_QUEUE_READY_TWO,typeStr), true, false, 
								false,RabbitConfig.buildQueueArguments()
						), 
						exchange, 
						RabbitConfig.formatStr(RabbitConfig.OMS_DELAY_CONSUMER_KEY_READY_SECOND, typeStr));
			}
			if(3 <= ttlQueueCount) {
				addQueueAndBiding(
						new Queue(
							RabbitConfig.formatStr(RabbitConfig.OMS_DELAY_CONSUMER_QUEUE_READY_THREE,typeStr), true, false, 
								false,RabbitConfig.buildQueueArguments()
						), 
						exchange, 
						RabbitConfig.formatStr(RabbitConfig.OMS_DELAY_CONSUMER_KEY_READY_THIRD, typeStr));
			}
			if(4 <= ttlQueueCount) {
				addQueueAndBiding(
						new Queue(
							RabbitConfig.formatStr(RabbitConfig.OMS_DELAY_CONSUMER_QUEUE_READY_FOUR,typeStr), true, false, 
								false,RabbitConfig.buildQueueArguments()
						), 
						exchange, 
						RabbitConfig.formatStr(RabbitConfig.OMS_DELAY_CONSUMER_KEY_READY_FOURTH, typeStr));
			}
		}});

		// 初始化系统模板
		templateInfoService.defaultTemplateInit();
		// 初始化支付系统配置
		platformPayModeRelationService.initSystemData();
	}

}
  
