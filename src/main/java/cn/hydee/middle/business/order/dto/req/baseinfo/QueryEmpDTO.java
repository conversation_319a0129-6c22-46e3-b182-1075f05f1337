package cn.hydee.middle.business.order.dto.req.baseinfo;

import cn.hydee.starter.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * 查询员工请求类
 * <AUTHOR>
 * @version 1.0
 * @date 2019/7/18 14:18
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QueryEmpDTO extends PageBase {
    @ApiModelProperty(value = "商家编码")
    @NotEmpty
    private String merCode;
    @ApiModelProperty(value = "机构编码")
    private String subOrgCode;
    @ApiModelProperty(value = "状态（0：禁用，1：启用）")
    private Integer status;
    @ApiModelProperty(value = "员工名称")
    private String empName;
    @ApiModelProperty(value = "部门名称")
    private String subOrgName;
    @ApiModelProperty(value = "员工编码")
    private String userId;
    @ApiModelProperty(value = "是否要分页true分页，false不分页,默认为true")
    private Boolean pageFlag=true;
    @ApiModelProperty(value = "账户类型：0-员工账户；1-门店账户")
    private Integer accountType;
    @ApiModelProperty(value = "是否需要员工基本表之外的相关信息 0：不需要 null或其他值为需要")
    private Integer needAdditionalInfo = 0;
    @ApiModelProperty(value = "员工搜索：员工姓名或者编码模糊搜索")
    private String empCodeOrName;

}
