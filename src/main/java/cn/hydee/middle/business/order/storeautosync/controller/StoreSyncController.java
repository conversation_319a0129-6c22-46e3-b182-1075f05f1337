package cn.hydee.middle.business.order.storeautosync.controller;

import cn.hydee.middle.business.order.storeautosync.facade.storesync.StoreSyncService;
import cn.hydee.starter.dto.ResponseBase;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;


@RestController
@RequestMapping("/${api.version}/ds/store/sync")
@Api(tags = "网店同步")
public class StoreSyncController {

    @Autowired
    private StoreSyncService storeSyncService;

    @PostMapping("/by_excel")
    @ApiOperation("网店同步-通过excel")
    public ResponseBase<Void> storeSyncByExcel(
            @RequestParam MultipartFile file,
            @RequestParam @ApiParam("ds_online_client主键") String onlineClientId
    ) {
        storeSyncService.storeSyncByExcel(file, onlineClientId);
        return ResponseBase.success();
    }

}
