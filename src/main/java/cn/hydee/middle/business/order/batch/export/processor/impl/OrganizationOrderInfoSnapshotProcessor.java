package cn.hydee.middle.business.order.batch.export.processor.impl;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.Enums.DsErrorType;
import cn.hydee.middle.business.order.batch.export.constant.ExportTypeConstant;
import cn.hydee.middle.business.order.batch.export.controller.ExportController;
import cn.hydee.middle.business.order.batch.export.dto.OrganizationOrderInfoSnapshotDTO;
import cn.hydee.middle.business.order.dto.OrderInfoSnapshotExportReqDto;
import cn.hydee.middle.business.order.dto.dutycasher.rsp.QueryAuthInfoRspDto;
import cn.hydee.middle.business.order.dto.req.VerifyReqDto;
import cn.hydee.middle.business.order.dto.rsp.OrgOrderInfoSnapshotPageRsp;
import cn.hydee.middle.business.order.dto.rsp.VerifyRspDto;
import cn.hydee.middle.business.order.entity.ExportTask;
import cn.hydee.middle.business.order.service.OrderInfoService;
import cn.hydee.middle.business.order.service.OrderStoreDefaultDutyCasherService;
import cn.hydee.middle.business.order.service.VerifyService;
import cn.hydee.middle.business.order.v2.manager.ParentStoreManager;
import cn.hydee.starter.exception.WarnException;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service(ExportTypeConstant.SERVICE_PREFIX + ExportTypeConstant.ORGANIZATION_SNAPSHOT_EXPORT)
@Slf4j
public class OrganizationOrderInfoSnapshotProcessor extends AbstractExportTaskProcessor<OrganizationOrderInfoSnapshotDTO> {

    @Autowired
    private OrderInfoService orderInfoService;
    @Autowired
    private VerifyService verifyService;
    @Autowired
    private OrderStoreDefaultDutyCasherService orderStoreDefaultDutyCasherService;

    @Override
    public Class getExportClass() {
        return OrganizationOrderInfoSnapshotDTO.class;
    }

    @Override
    public List getExportDateList(ExportTask task) {

        OrderInfoSnapshotExportReqDto reqDTO = JSONObject.parseObject(task.getRequestJson(), OrderInfoSnapshotExportReqDto.class);
//        log.info("[OrganizationOrderInfoSnapshotProcessor] merCode:{}.", reqDTO.getMerCode());

        if (CollectionUtils.isEmpty(reqDTO.getOrganizationCodeList())) {
            VerifyRspDto verifyDto = verifyService.verifyOrganizationAndGet(VerifyReqDto.builder()
                    .merCode(reqDTO.getMerCode())
                    .organizationCode(DsConstants.ORGANIZATION_CODE_ALL)
                    .userId(reqDTO.getUserId())
                    .build());
            reqDTO.setOrganizationCodeList(verifyDto.getOrganizatioinList());
        } else {
            List<String> orgList = verifyService.verifyOrgList(reqDTO.getMerCode(),reqDTO.getUserId(), reqDTO.getOrganizationCodeList());
            if (CollectionUtils.isEmpty(orgList)) {
                throw WarnException.builder().code(DsErrorType.EXCEL_ROW_NULL.getCode()).
                        tipMessage(DsErrorType.EXCEL_ROW_NULL.getMsg()).build();
            }
            reqDTO.setOrganizationCodeList(orgList);
        }

        List<OrganizationOrderInfoSnapshotDTO> exportList = new ArrayList<>();

//        log.info("[OrganizationOrderInfoSnapshotProcessor]prepare query data. ");

        reqDTO.setCurrentPage(1);
        reqDTO.setPageSize(ExportController.MAX_EXPORT_COUNT);
        List<OrgOrderInfoSnapshotPageRsp> orderInfoSnapshots = orderInfoService.getOrgSnapshot(reqDTO).getRecords();

        // 【【新增】ERP全自动下账功能】 https://www.tapd.cn/********/prong/stories/view/11********001024255  补充全自动下账数据
        Map<String, QueryAuthInfoRspDto> queryAuthInfoRspDtoMap = orderStoreDefaultDutyCasherService.queryAuthInfo(reqDTO.getMerCode(),reqDTO.getOrganizationCodeList());
        orderInfoSnapshots.forEach(record ->{
            QueryAuthInfoRspDto queryAuthInfoRspDto = Optional.ofNullable(queryAuthInfoRspDtoMap.get(record.getOrganizationCode())).orElse(QueryAuthInfoRspDto.buildFalseBean());
            record.setAutoEnterAccountOpenFlag(queryAuthInfoRspDto.needAllAutoEnterAccount()?"是":"否");
        });

//        log.info("[organizationServiceReportTaskProcessor] orderInfoSnapshots. size:{}", orderInfoSnapshots.size());

        if (!CollectionUtils.isEmpty(orderInfoSnapshots)) {
            for (OrgOrderInfoSnapshotPageRsp organizationServiceReport : orderInfoSnapshots) {
                exportList.add(transfer(organizationServiceReport));
            }
        }

        if (CollectionUtils.isEmpty(exportList)) {
            throw WarnException.builder().code(DsErrorType.EXCEL_ROW_NULL.getCode()).
                    tipMessage(DsErrorType.EXCEL_ROW_NULL.getMsg()).build();
        }
        // 【【上海国药】【订单查询】：订单明细报表增加门店“上级机构”字段】 https://www.tapd.cn/********/prong/stories/view/11********001033404
        ParentStoreManager.convertOrganizationOrderInfoSnapshotExport(reqDTO.getMerCode(),exportList);
        return exportList;
    }

    @Override
    public String getFileNamePrefix() {
        return "门店订单处理进度看板-机构";
    }

    @Override
    public CellWriteHandler getCellWriteHandler(Integer dataSize) {
        return new SimpleColumnWidthStyleStrategy(20);
    }

    /**
     * 数据转化为excel内显示数据格式
     *
     * @param dto 数据
     * @return excel内显示数据格式
     */
    private OrganizationOrderInfoSnapshotDTO transfer(OrgOrderInfoSnapshotPageRsp dto) {
        OrganizationOrderInfoSnapshotDTO exportDTO = new OrganizationOrderInfoSnapshotDTO();
        BeanUtils.copyProperties(dto, exportDTO);
        return exportDTO;
    }

}
