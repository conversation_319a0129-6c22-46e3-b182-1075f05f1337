package cn.hydee.middle.business.order.doris.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/06/28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderReportColumnDto implements Serializable {

    private static final long serialVersionUID = -7498093265240470160L;

    @ApiModelProperty(value = "商户编码（不需要入参，系统从header中获取）")
    private String merCode;

    @ApiModelProperty(value = "用户id（不需要入参，系统从header中获取）")
    private String userId;

    @ApiModelProperty(value = "销售字段内容")
    private String payColumnContent;

    @ApiModelProperty(value = "下账字段内容")
    private String billColumnContent;
}
