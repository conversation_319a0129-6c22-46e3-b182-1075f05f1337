
package cn.hydee.middle.business.order.comment.base.enums;

public enum PullCommentDataPlatformTypeEnum {

    EB("commentDataPullEB", "饿百"),
    MT("commentDataPullMT", "美团");

    private String code;
    private String msg;

    PullCommentDataPlatformTypeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
  
