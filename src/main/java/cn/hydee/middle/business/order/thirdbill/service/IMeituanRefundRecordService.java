package cn.hydee.middle.business.order.thirdbill.service;

import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.entity.RefundOrder;
import cn.hydee.unified.model.order.MeituanRefundRecord;

import java.util.List;

/**
 * 美团退款详情记录服务接口
 *
 * <AUTHOR>
 * @version 3.9.2
 * @date 2020/09/07
 */
public interface IMeituanRefundRecordService {

    /**
     * 获取退款单详情
     *
     * @param orderInfo 订单详情
     * @return 美团退款记录详情
     */
    List<MeituanRefundRecord> obtainRefundRecordList(OrderInfo orderInfo);

    /**
     * 获取退款单详情
     *
     * @param orderInfo 订单详情
     * @param refundOrder 退款详情
     * @return 美团退款记录详情
     */
    MeituanRefundRecord obtainRefundRecord(OrderInfo orderInfo, RefundOrder refundOrder);
}
