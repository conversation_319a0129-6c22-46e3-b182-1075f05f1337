package cn.hydee.middle.business.order.entity;

import lombok.Data;

import java.util.Date;

/**
 * 订单优惠信息
 * @Author: chuf<PERSON>(2910)
 * @Date: 2021/7/21 14:56
 */
@Data
public class OrderCouponInfo {

    private Long id;
    //系统单号,退款时是退款单号
    private Long orderNo;
    //1-订单，2-退款单
    private Integer type;
    //优惠券id
    private String couponId;
    //优惠券内容
    private String couponContent;
    //优惠券码
    private String couponCode;
    //优惠名称
    private String couponName;
    //优惠金额
    private String couponPrice;
    //使用场景
    private String couponScene;

    private Date createTime;

    private Date modifyTime;

}
