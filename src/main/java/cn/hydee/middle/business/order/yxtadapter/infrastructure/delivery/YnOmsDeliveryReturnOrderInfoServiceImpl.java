package cn.hydee.middle.business.order.yxtadapter.infrastructure.delivery;

import cn.hydee.middle.business.order.yxtadapter.domain.oms.transform.ReturnorderDetail;
import cn.hydee.middle.business.order.yxtadapter.domainservice.delivery.YnOmsDeliveryReturnOrderInfoService;
import cn.hydee.middle.business.order.yxtadapter.infrastructure.db.mysql.mapper.YnOmsReturnOrderDetailMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * Date: 2023/9/25
 */
@Service
public class YnOmsDeliveryReturnOrderInfoServiceImpl implements YnOmsDeliveryReturnOrderInfoService {

    @Autowired
    private YnOmsReturnOrderDetailMapper omsReturnOrderDetailMapper;

    /**
     * 通过退单号查找订单详情
     *
     * @param returnCode
     * @return
     */
    @Override
    public List<ReturnorderDetail> queryReturnOrderDetailByNum(String returnCode) {
        LambdaQueryWrapper<ReturnorderDetail> queryWarpper = (new QueryWrapper<ReturnorderDetail>()).lambda();
        queryWarpper.eq(ReturnorderDetail::getReturncode, returnCode);
        List<ReturnorderDetail> orderInfos = omsReturnOrderDetailMapper.selectList(queryWarpper);
        return orderInfos;
    }

    @Override
    public Map<String, List<ReturnorderDetail>> batchQueryReturnOrderDetailByNum(List<String> returnCodeList) {
        LambdaQueryWrapper<ReturnorderDetail> queryWarpper = (new QueryWrapper<ReturnorderDetail>()).lambda();
        queryWarpper.in(ReturnorderDetail::getReturncode, returnCodeList)
                .eq(ReturnorderDetail::getInnerstatus, 1)
                //排除退款数量为0的明细
                .ne(ReturnorderDetail::getReturncount,0)
                .ne(ReturnorderDetail::getGoodscode,"PACKAGEFEE")
                .ne(ReturnorderDetail::getGoodscode,"POSTFEE")
                .ne(ReturnorderDetail::getGoodscode,"SERVICEFEE");
        return omsReturnOrderDetailMapper.selectList(queryWarpper).stream().collect(Collectors.groupingBy(ReturnorderDetail::getReturncode));
    }
}
