package cn.hydee.middle.business.order.yxtadapter.infrastructure.domainserviceimpl.kcpos.o2o;

import cn.hutool.core.collection.CollUtil;
import cn.hydee.middle.business.order.Enums.*;
import cn.hydee.middle.business.order.domain.OrderDetailDomain;
import cn.hydee.middle.business.order.dto.req.RefundBillReqDto;
import cn.hydee.middle.business.order.dto.rsp.RefundDetailRsp;
import cn.hydee.middle.business.order.entity.*;
import cn.hydee.middle.business.order.interceptor.GlobalInterceptor;
import cn.hydee.middle.business.order.mapper.*;
import cn.hydee.middle.business.order.refund.context.RefundOrderContext;
import cn.hydee.middle.business.order.service.ErpPayModeService;
import cn.hydee.middle.business.order.service.StoreBillConfigService;
import cn.hydee.middle.business.order.service.suport.OrderBasicService;
import cn.hydee.middle.business.order.service.suport.RefundHandlerService;
import cn.hydee.middle.business.order.thirdbill.service.IMeituanRefundService;
import cn.hydee.middle.business.order.util.ExceptionUtil;
import cn.hydee.middle.business.order.yxtadapter.domain.pos.callback.getorderinfo.KcPosBatchInfo;
import cn.hydee.middle.business.order.yxtadapter.domain.pos.callback.getreturnorderinfo.KcPosGetReturnOrderDetailCallBackResponse;
import cn.hydee.middle.business.order.yxtadapter.domain.pos.callback.getreturnorderinfo.KcPosReturnOrderDetail;
import com.alibaba.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import java.util.*;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.stream.Collectors;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR>
 * Date:  2023/9/11
 */
@Slf4j
@Component
public class KcPosGetReturnOrderDetailAdapter {

    @Autowired
    private RefundHandlerService refundHandlerService;

    @Autowired
    private RefundOrderMapper refundOrderMapper;

    @Autowired
    private OrderBasicService orderBasicService;

    @Autowired
    private IMeituanRefundService meituanRefundService;



    @Autowired
    private RefundDetailMapper refundDetailMapper;




    @Autowired
    OrderPickInfoMapper orderPickInfoMapper;
    @Autowired
    private OrderPayInfoMapper orderPayInfoMapper;

    @Value("${b2cAllRefundFlag:true}")
    private boolean b2cAllRefundFlag;


    public List<KcPosReturnOrderDetail> getReturnOrderDetailList(RefundDetailRsp refundDetailRsp, List<RefundDetail> changedPirce001List, List<OrderDetailDomain> orderDetailDomainList, BigDecimal newRefundGoodsTotal) {

        List<RefundDetail> refundDetailList = refundDetailRsp.getRefundDetailList();
        List<OrderPickInfo> orderPickInfoList = orderPickInfoMapper.selectPickInfoListByOrderNo(refundDetailRsp.getOrderNo());
        if (CollUtil.isEmpty(orderPickInfoList)) {
            return null;
        }

        Map<String, OrderDetailDomain> orderDetailDomainMap = orderDetailDomainList.stream().collect(Collectors.toMap(p -> String.format("%s_%s", p.getErpCode(), p.getThirdDetailId()), p -> p));
        Map<String, RefundDetail> refundDetailDomainMap = refundDetailList.stream().collect(Collectors.toMap(p -> String.format("%s_%s", p.getErpCode(), p.getThirdDetailId()), p -> p));
        Map<Long, List<OrderPickInfo>> pickListMap = orderPickInfoList.stream().collect(Collectors.groupingBy(OrderPickInfo::getOrderDetailId));


        List<KcPosReturnOrderDetail> result = Lists.newArrayList();

        if (RefundTypeEnum.ALL.getCode().equals(refundDetailRsp.getType())) {
            for (OrderDetailDomain od : orderDetailDomainList) {
                RefundDetail refundDetail = refundDetailDomainMap.get(String.format("%s_%s", od.getErpCode(), od.getThirdDetailId()));
                if (od.getStatus() == OrderDetailStatusEnum.REPLACE.getCode().intValue()) {
                    log.info("退款单号：{}，商品：{}，已换货，不退款", refundDetailRsp.getRefundNo(), od.getCommodityName());
                    continue;
                }
                if (null == refundDetail) {
                    log.info("退款单号：{}，商品：{}，美团/JDDJ 退款，不退款", refundDetailRsp.getRefundNo(), od.getCommodityName());
                    continue;
                }
                List<OrderPickInfo> list = pickListMap.get(od.getId());
                if (null == list) {
                    log.error("退款单号：{}，商品：{}，没有拣货信息", refundDetailRsp.getRefundNo(), od.getCommodityName());
                    return null;
                }
                Integer pickCount = od.getPickCount();
                BigDecimal actualNetAmount = od.getActualNetAmount();
                BigDecimal billPrice = od.getBillPrice();

                if (pickCount.intValue() != refundDetail.getRefundCount()) {
                    pickCount = refundDetail.getRefundCount();
                    actualNetAmount = refundDetail.getActualNetAmount();
                    billPrice = refundDetail.getBillPrice();
                }
                if (billPrice.doubleValue() != refundDetail.getBillPrice().doubleValue()) {
                    actualNetAmount = refundDetail.getActualNetAmount();
                    billPrice = refundDetail.getBillPrice();

                }
                if (pickCount <= 0) {
                    continue;
                }

                List<KcPosReturnOrderDetail> details = new ArrayList<>();
                KcPosReturnOrderDetail kcPosOrderDetail = new KcPosReturnOrderDetail();
                kcPosOrderDetail.setGOODSCODE(od.getErpCode());
                kcPosOrderDetail.setGOODSNAME(od.getCommodityName());
                BigDecimal bigDecimal = billPrice.setScale(2, RoundingMode.DOWN);
                BigDecimal multiply = new BigDecimal(pickCount).multiply(bigDecimal);
                if (multiply.doubleValue() != actualNetAmount.doubleValue()) {
                    kcPosOrderDetail.setRETURNCOUNT(new BigDecimal(pickCount).subtract(new BigDecimal(1)));
                    kcPosOrderDetail.setGOODSPRICE(billPrice.setScale(2, RoundingMode.DOWN));
                    kcPosOrderDetail.setGOODSAMOUNT(kcPosOrderDetail.getGOODSPRICE().multiply(kcPosOrderDetail.getRETURNCOUNT()));
                    kcPosOrderDetail.setISGIFT(od.getIsGift());
                    details.add(kcPosOrderDetail);
                    // 部分退款单最后一个
                    KcPosReturnOrderDetail kcPosOrderDetailLast = new KcPosReturnOrderDetail();
                    kcPosOrderDetailLast.setGOODSCODE(refundDetail.getErpCode());
                    kcPosOrderDetailLast.setGOODSNAME(refundDetail.getCommodityName());
                    kcPosOrderDetailLast.setISGIFT(od.getIsGift());
                    kcPosOrderDetailLast.setRETURNCOUNT(new BigDecimal(1));
                    // 部分最后一个退款单的金额 =  退款商品的下账金额 - 退款商品已经计算过的金额 只计算差值 差多少 就 补多少
                    BigDecimal amount = refundDetail.getActualNetAmount().subtract(kcPosOrderDetail.getGOODSAMOUNT()).setScale(2, RoundingMode.DOWN);
                    kcPosOrderDetailLast.setGOODSPRICE(amount);
                    kcPosOrderDetailLast.setGOODSAMOUNT(amount);
                    details.add(kcPosOrderDetailLast);
                }else {
                    kcPosOrderDetail.setRETURNCOUNT(new BigDecimal(pickCount));
                    kcPosOrderDetail.setGOODSPRICE(billPrice.setScale(2, RoundingMode.DOWN));
                    kcPosOrderDetail.setGOODSAMOUNT(actualNetAmount.setScale(2, RoundingMode.DOWN));
                    kcPosOrderDetail.setISGIFT(od.getIsGift());
                    details.add(kcPosOrderDetail);
                }
                // 添加赠品信息
                addIsGift(changedPirce001List, refundDetail, od, kcPosOrderDetail);
                // 添加批号信息
                addBatch(result, details, list);
            }

        } else {
            for (RefundDetail refundDetail : refundDetailList) {

                String key = String.format("%s_%s", refundDetail.getErpCode(), refundDetail.getThirdDetailId());
                OrderDetailDomain orderDetailDomain = orderDetailDomainMap.get(key);
                if (Objects.isNull(orderDetailDomain)) {
                    throw new RuntimeException("退款单号：" + refundDetailRsp.getRefundNo() + "，商品：" + refundDetail.getCommodityName() + "，没有对应的订单详情");
                }
                //正单小数位数超过2位需要进行拆分
                if(orderDetailDomain.getBillPrice().stripTrailingZeros().scale() > DsConstants.INTEGER_TWO){
                    this.splitOrderDetail(orderDetailDomain, refundDetail, result, pickListMap);
                    continue;
                }
                KcPosReturnOrderDetail kcPosOrderDetail = new KcPosReturnOrderDetail();
                kcPosOrderDetail.setGOODSCODE(refundDetail.getErpCode());
                kcPosOrderDetail.setGOODSNAME(refundDetail.getCommodityName());
                kcPosOrderDetail.setRETURNCOUNT(new BigDecimal(refundDetail.getRefundCount()));
                kcPosOrderDetail.setGOODSPRICE(orderDetailDomain.getBillPrice().setScale(2, RoundingMode.DOWN));
                kcPosOrderDetail.setGOODSAMOUNT(orderDetailDomain.getBillPrice().multiply(new BigDecimal(refundDetail.getRefundCount())).setScale(2, RoundingMode.DOWN));
                kcPosOrderDetail.setISGIFT(orderDetailDomain.getIsGift());

                // 添加赠品信息
                addIsGift(changedPirce001List, refundDetail, orderDetailDomain, kcPosOrderDetail);


                Integer refundCount = refundDetail.getRefundCount();
                List<OrderPickInfo> list = pickListMap.get(orderDetailDomain.getId());
                if (null == list) {
                    log.error("退款单号：{}，商品：{}，没有拣货信息", refundDetailRsp.getRefundNo(), orderDetailDomain.getCommodityName());
                    return null;
                }
                if (CollUtil.isNotEmpty(list)) {
                    List<KcPosBatchInfo> BATCH = Lists.newArrayList();
                    while (refundCount > 0) {
                        for (OrderPickInfo orderPickInfo : list) {
                            KcPosBatchInfo kcPosBatchInfo = new KcPosBatchInfo();
                            kcPosBatchInfo.setGOODSCODE(orderPickInfo.getErpCode());
                            kcPosBatchInfo.setMAKENO(orderPickInfo.getCommodityBatchNo());
                            kcPosBatchInfo.setBATCHNO(orderPickInfo.getCommodityBatchNo());
                            kcPosBatchInfo.setCHECKFLAG("1");
                            BATCH.add(kcPosBatchInfo);
                            if (refundCount > orderPickInfo.getCount()) {
                                kcPosBatchInfo.setCOUNT(new BigDecimal(orderPickInfo.getCount()));
                                refundCount = refundCount - orderPickInfo.getCount();
                            } else if (refundCount <= orderPickInfo.getCount()) {
                                kcPosBatchInfo.setCOUNT(new BigDecimal(refundCount));
                                refundCount = 0;
                                break;
                            }
                        }
                    }
                    kcPosOrderDetail.setBATCH(BATCH);
                    result.add(kcPosOrderDetail);
                }
            }
        }

        if (changedPirce001List.size() > 0) {

            BigDecimal refundDetailTotal = BigDecimal.ZERO;
            for (RefundDetail refundDetail : refundDetailList) {
                String key = String.format("%s_%s", refundDetail.getErpCode(), refundDetail.getThirdDetailId());
                OrderDetailDomain orderDetailDomain = orderDetailDomainMap.get(key);
                if (1 == orderDetailDomain.getIsGift() || refundDetail.getBillPrice().doubleValue() < 0.01) {
                } else {
                    refundDetailTotal = refundDetailTotal.add(refundDetail.getActualNetAmount());
                }
            }
            newRefundGoodsTotal = refundDetailTotal;

        }

        return result;


    }

    private static void addIsGift(List<RefundDetail> changedPirce001List, RefundDetail refundDetail, OrderDetailDomain orderDetailDomain, KcPosReturnOrderDetail kcPosOrderDetail) {
        if (1 == orderDetailDomain.getIsGift() || refundDetail.getBillPrice().doubleValue() < 0.01) {
            RefundDetail chargePrice = new RefundDetail();
            chargePrice.setErpCode(refundDetail.getErpCode());
            chargePrice.setRefundCount(refundDetail.getRefundCount());
            chargePrice.setActualNetAmount(BigDecimal.valueOf(0.01).multiply(BigDecimal.valueOf(refundDetail.getRefundCount())).setScale(2, RoundingMode.DOWN));
            chargePrice.setBillPrice(BigDecimal.valueOf(0.01).setScale(2, RoundingMode.DOWN));
            chargePrice.setBuyerAmount(BigDecimal.valueOf(0.01).multiply(BigDecimal.valueOf(refundDetail.getRefundCount())).setScale(2, RoundingMode.DOWN));
            chargePrice.setMerchantAmount(BigDecimal.ZERO);
            chargePrice.setUnitRefundPrice(BigDecimal.valueOf(0.01).setScale(2, RoundingMode.DOWN));
            chargePrice.setOriginDetailPrice(BigDecimal.valueOf(0.01).setScale(2, RoundingMode.DOWN));
            chargePrice.setRefundDiscountAmount(BigDecimal.ZERO);
            chargePrice.setCouponAmount(BigDecimal.ZERO);
            chargePrice.setActivityDiscountAmont(BigDecimal.ZERO);
            chargePrice.setHealthValue(BigDecimal.ZERO);
            chargePrice.setShareAmount(BigDecimal.ZERO);

            changedPirce001List.add(chargePrice);
            kcPosOrderDetail.setGOODSPRICE(chargePrice.getBillPrice());
            kcPosOrderDetail.setGOODSAMOUNT(chargePrice.getActualNetAmount());
            kcPosOrderDetail.setISGIFT(1);
        }
    }

    /**
     * 部分退款当正单下账单价小数超过两位时商品信息拆分
     * 拆分逻辑：
     * 买5个，退5个：拆成4 + 1
     * 买5个，部分退：直接正单billPrice正单billPrice截取后两位小数后乘以退款数量
     *       退最后一个，取正单下账总额 - （正单billPrice截取后两位小数后 * 已退款数量）
     *  yanghua:
     *      此处修改逻辑,害怕忘记,所以现在记下来
     *      拆分逻辑修改为只分最后一次退和非最后一次退
     *      查询退款商品已经下账的数量(不区分全部与部分),认为只要 正单下账的数量 - 已下账退款数量 = 本次退款数量即为最后一次退款,
     *      否则为最后一次
     *      非最后一次时
     *          1.退款数量 = 1时,
     *              billPrice 取正单商品的单价,数量取1,下账金额 = refundCount * billPrice
     *          2.退款数量 != 1时,拆为N+1
     *              N:billPrice 取正单商品的单价,数量取N,下账金额 = refundCount * billPrice
     *              1:billPrice 取本次退款单的下账金额 - N个的下账金额总和,数量取1
     *      最后一次时
     *          1.退款数量 =1 时,
     *              billPrice 取 正单商品的下账总金额 - 已下账退款商品的下账总额之和
     *              其中, 已下账退款商品的下账总额之和 = 正单商品的单价 * 已退款的商品数量
     *          2.退款数量 != 1时,拆为N+1
     *              N:billPrice 取正单商品的单价,数量取N,下账金额 = refundCount * billPrice
     *              1:billPrice 取正单商品的下账金额 - N个的下账金额总和,数量取1
     *          3.这里有个疑问:非最后一次退款数量 != 1时,存在拆单情况,使用上述公式有可能会存在差价,是否可行?
     * @param orderDetailDomain
     * @param refundDetail
     * @return void
     */
    private void splitOrderDetail(OrderDetailDomain orderDetailDomain, RefundDetail refundDetail, List<KcPosReturnOrderDetail> result, Map<Long, List<OrderPickInfo>> pickListMap){
        List<KcPosReturnOrderDetail> details = new ArrayList<>();
        List<OrderPickInfo> list = pickListMap.get(orderDetailDomain.getId());
        if (CollUtil.isEmpty(list)) {
            log.error("退款单号：{}，商品：{}，部分退款时没有拣货信息", refundDetail.getRefundNo(), orderDetailDomain.getCommodityName());
            return;
        }
        BigDecimal billPrice = orderDetailDomain.getBillPrice().setScale(2, RoundingMode.DOWN);
        // 只区分是不是为最后一次退款即可 全部退的alreadyRefundCount为0
        // 获取部分退款 且是已经下账的
        List<RefundDetail> refundDetails = refundDetailMapper.selectOrderByThirdDetailId(orderDetailDomain.getOrderNo(), orderDetailDomain.getThirdDetailId(), orderDetailDomain.getErpCode());
        int alreadyRefundCount = refundDetails.stream().mapToInt(RefundDetail::getRefundCount).sum();
        // 最后一次 (全部退也为最后一次)
        if(orderDetailDomain.getCommodityCount() - alreadyRefundCount == refundDetail.getRefundCount()){
            this.splitOrderDetailLast(refundDetail, orderDetailDomain, billPrice, details, refundDetails);
        } else  {
            // 非最后一次
            this.splitOrderDetailNotLast(refundDetail, orderDetailDomain, billPrice, details);
        }
        addBatch(result, details, list);
    }

    private static void addBatch(List<KcPosReturnOrderDetail> result, List<KcPosReturnOrderDetail> details, List<OrderPickInfo> list) {
        for (KcPosReturnOrderDetail kcPosOrderDetail: details) {
            int refundCount = Integer.parseInt(kcPosOrderDetail.getRETURNCOUNT().toString());
            List<KcPosBatchInfo> BATCH = Lists.newArrayList();
            while (refundCount > 0) {
                for (OrderPickInfo orderPickInfo : list) {
                    KcPosBatchInfo kcPosBatchInfo = new KcPosBatchInfo();
                    kcPosBatchInfo.setGOODSCODE(orderPickInfo.getErpCode());
                    kcPosBatchInfo.setMAKENO(orderPickInfo.getCommodityBatchNo());
                    kcPosBatchInfo.setBATCHNO(orderPickInfo.getCommodityBatchNo());
                    kcPosBatchInfo.setCHECKFLAG("1");
                    BATCH.add(kcPosBatchInfo);
                    if (refundCount > orderPickInfo.getCount()) {
                        kcPosBatchInfo.setCOUNT(new BigDecimal(orderPickInfo.getCount()));
                        refundCount = refundCount - orderPickInfo.getCount();
                    } else if (refundCount <= orderPickInfo.getCount()) {
                        kcPosBatchInfo.setCOUNT(new BigDecimal(refundCount));
                        refundCount = 0;
                        break;
                    }
                }
            }
            kcPosOrderDetail.setBATCH(BATCH);
            result.add(kcPosOrderDetail);
        }
    }

    private void splitOrderDetailNotLast(RefundDetail refundDetail, OrderDetailDomain orderDetailDomain, BigDecimal billPrice, List<KcPosReturnOrderDetail> details){
        if(refundDetail.getRefundCount().equals(DsConstants.INTEGER_ONE)){
            log.info("数量为1：erpCode:{}, orderNo:{}, refundNo:{}", orderDetailDomain.getErpCode(), orderDetailDomain.getOrderNo(), refundDetail.getRefundNo());
            KcPosReturnOrderDetail kcPosOrderDetail = new KcPosReturnOrderDetail();
            kcPosOrderDetail.setGOODSCODE(refundDetail.getErpCode());
            kcPosOrderDetail.setGOODSNAME(refundDetail.getCommodityName());
            kcPosOrderDetail.setISGIFT(orderDetailDomain.getIsGift());
            kcPosOrderDetail.setRETURNCOUNT(new BigDecimal(1));
            kcPosOrderDetail.setGOODSPRICE(billPrice);
            kcPosOrderDetail.setGOODSAMOUNT(billPrice);
            details.add(kcPosOrderDetail);
        }else{
            log.info("数量不为1：erpCode:{}, orderNo:{}, refundNo:{}", orderDetailDomain.getErpCode(), orderDetailDomain.getOrderNo(), refundDetail.getRefundNo());
            KcPosReturnOrderDetail kcPosOrderDetail = new KcPosReturnOrderDetail();
            kcPosOrderDetail.setGOODSCODE(refundDetail.getErpCode());
            kcPosOrderDetail.setGOODSNAME(refundDetail.getCommodityName());
            kcPosOrderDetail.setISGIFT(orderDetailDomain.getIsGift());
            kcPosOrderDetail.setRETURNCOUNT(new BigDecimal(refundDetail.getRefundCount() - 1));
            kcPosOrderDetail.setGOODSPRICE(billPrice);
            // GOODS AMOUNT = RETURN COUNT * GOODS PRICE
            kcPosOrderDetail.setGOODSAMOUNT(kcPosOrderDetail.getGOODSPRICE().multiply(kcPosOrderDetail.getRETURNCOUNT()));
            details.add(kcPosOrderDetail);
            // 部分退款单最后一个
            KcPosReturnOrderDetail kcPosOrderDetailLast = new KcPosReturnOrderDetail();
            kcPosOrderDetailLast.setGOODSCODE(refundDetail.getErpCode());
            kcPosOrderDetailLast.setGOODSNAME(refundDetail.getCommodityName());
            kcPosOrderDetailLast.setISGIFT(orderDetailDomain.getIsGift());
            kcPosOrderDetailLast.setRETURNCOUNT(new BigDecimal(1));
            // 部分最后一个退款单的金额 =  退款商品的下账金额 - 退款商品已经计算过的金额 只计算差值 差多少 就 补多少
            BigDecimal amount = refundDetail.getActualNetAmount().subtract(kcPosOrderDetail.getGOODSAMOUNT()).setScale(2, RoundingMode.DOWN);
            kcPosOrderDetailLast.setGOODSPRICE(amount);
            kcPosOrderDetailLast.setGOODSAMOUNT(amount);
            details.add(kcPosOrderDetailLast);
        }
    }

    private void splitOrderDetailLast(RefundDetail refundDetail, OrderDetailDomain orderDetailDomain, BigDecimal billPrice, List<KcPosReturnOrderDetail> details, List<RefundDetail> refundDetails){
        BigDecimal actualNetAmount = orderDetailDomain.getActualNetAmount();
        BigDecimal alreadyActualNetAmount = BigDecimal.ZERO;
        // 获取真实的退款单下账金额
        for (RefundDetail detail : refundDetails) {
            if (detail.getRefundCount() > 1) {
                // 进行合单计算
                BigDecimal nAmount = billPrice.multiply(new BigDecimal(detail.getRefundCount() - 1));
                BigDecimal amount = detail.getActualNetAmount().subtract(nAmount).setScale(2, RoundingMode.DOWN);
                alreadyActualNetAmount=alreadyActualNetAmount.add(amount).add(nAmount);
            } else {
                alreadyActualNetAmount=alreadyActualNetAmount.add(billPrice);
            }
        }


        if(refundDetail.getRefundCount().equals(DsConstants.INTEGER_ONE)){
            log.info("数量为1：erpCode:{}, orderNo:{}, refundNo:{}", orderDetailDomain.getErpCode(), orderDetailDomain.getOrderNo(), refundDetail.getRefundNo());
            KcPosReturnOrderDetail kcPosOrderDetail = new KcPosReturnOrderDetail();
            kcPosOrderDetail.setGOODSCODE(refundDetail.getErpCode());
            kcPosOrderDetail.setGOODSNAME(refundDetail.getCommodityName());
            kcPosOrderDetail.setISGIFT(orderDetailDomain.getIsGift());
            kcPosOrderDetail.setRETURNCOUNT(new BigDecimal(1));
            BigDecimal amount = actualNetAmount.subtract(alreadyActualNetAmount);
            kcPosOrderDetail.setGOODSPRICE(amount.setScale(2, RoundingMode.DOWN));
            kcPosOrderDetail.setGOODSAMOUNT(amount.setScale(2, RoundingMode.DOWN));
            details.add(kcPosOrderDetail);
        }else{
            log.info("数量不为1：erpCode:{}, orderNo:{}, refundNo:{}", orderDetailDomain.getErpCode(), orderDetailDomain.getOrderNo(), refundDetail.getRefundNo());
            KcPosReturnOrderDetail kcPosOrderDetail = new KcPosReturnOrderDetail();
            kcPosOrderDetail.setGOODSCODE(refundDetail.getErpCode());
            kcPosOrderDetail.setGOODSNAME(refundDetail.getCommodityName());
            kcPosOrderDetail.setISGIFT(orderDetailDomain.getIsGift());
            kcPosOrderDetail.setRETURNCOUNT(new BigDecimal(refundDetail.getRefundCount() - 1));
            kcPosOrderDetail.setGOODSPRICE(billPrice);
            // GOODS AMOUNT = RETURN COUNT * GOODS PRICE
            kcPosOrderDetail.setGOODSAMOUNT(kcPosOrderDetail.getGOODSPRICE().multiply(kcPosOrderDetail.getRETURNCOUNT()));
            details.add(kcPosOrderDetail);
            // 部分退款单最后一个
            KcPosReturnOrderDetail kcPosOrderDetailLast = new KcPosReturnOrderDetail();
            kcPosOrderDetailLast.setGOODSCODE(refundDetail.getErpCode());
            kcPosOrderDetailLast.setGOODSNAME(refundDetail.getCommodityName());
            kcPosOrderDetailLast.setISGIFT(orderDetailDomain.getIsGift());
            kcPosOrderDetailLast.setRETURNCOUNT(new BigDecimal(1));
            // 部分最后一个退款单的金额 =  正单商品的下账金额 - 退款商品已经计算过的金额 只计算差值 差多少 就 补多少
            BigDecimal amount = actualNetAmount.subtract(alreadyActualNetAmount).subtract(kcPosOrderDetail.getGOODSAMOUNT());
            kcPosOrderDetailLast.setGOODSPRICE(amount.setScale(2, RoundingMode.DOWN));
            kcPosOrderDetailLast.setGOODSAMOUNT(amount.setScale(2, RoundingMode.DOWN));
            details.add(kcPosOrderDetailLast);
        }
    }


    /**
     * cn.hydee.middle.business.order.service.impl.RefundServiceImpl#refundBill
     * // 只有正向单已下账的 逆向单才需要下账
     *
     * @param refundOrder 退款单
     * @return KcPosGetReturnOrderDetailCallBackResponse
     */
    public KcPosGetReturnOrderDetailCallBackResponse beforeToGetReturnOrderDetailCallBack(RefundOrder refundOrder, OrderPayInfo orderPayInfoOut) {
        // 前置1
        OrderInfo orderInfo = orderBasicService.getOrderBaseWithCheck(refundOrder.getOrderNo());
        // 销售单是否已下账
        boolean salesBillHasSink = ErpStateEnum.HAS_SALE.getCode().equals(orderInfo.getErpState());
        if (!salesBillHasSink) {
            return KcPosGetReturnOrderDetailCallBackResponse.fail("销售单还未下账，不允许退款下账");
        }


        RefundBillReqDto refundBillReqDto = new RefundBillReqDto();
        refundBillReqDto.setRefundBusinessFlag(1);
        refundBillReqDto.setRemark("");
        refundBillReqDto.setZeroEbaiCommission(1);
        refundBillReqDto.setRefundNo(refundOrder.getRefundNo());

        if (DsConstants.INTEGER_ONE.equals(refundBillReqDto.getZeroEbaiCommission())) {
            refundHandlerService.upEbaiCommissionFlag(refundOrder.getOrderNo(), refundOrder.getThirdPlatformCode(), refundOrder);
        }
        Boolean b = checkRefundAll(refundBillReqDto.getRefundNo(), orderInfo);
        if (Boolean.FALSE.equals(b)) {
            return KcPosGetReturnOrderDetailCallBackResponse.fail("退款单存在多条次下账存在的记录，请重新发起退款下账");
        }

        if (!RefundStateEnum.SUCCESS.getCode().equals(refundOrder.getState())
                || (!RefundErpStateEnum.WAIT_REFUND.getCode().equals(refundOrder.getErpState()) && DsConstants.O2O.equals(orderInfo.getServiceMode()))
                || (DsConstants.B2C.equals(orderInfo.getServiceMode()) && refundOrder.getErpState()!=30)) {
            return KcPosGetReturnOrderDetailCallBackResponse.fail("退款单状态不正确");
        }
        GlobalInterceptor.refundUserId.set("系统");
        int businessFlag = refundBillReqDto.getRefundBusinessFlag();
        RefundOrderContext context = new RefundOrderContext();
        context.setOrderInfo(orderInfo);
        context.setRefundOrder(refundOrder);
        // 前置2
        GlobalInterceptor.tObject.set(context.getOrderInfo());
        boolean autoSinkRefund = true;
        if (PlatformCodeEnum.MEITUAN.getCode().equals(refundOrder.getThirdPlatformCode())
                && RefundTypeEnum.PART.getCode().equals(refundOrder.getType())) {
            autoSinkRefund = meituanRefundService.autoAssignMtRefundAmountFromRecordByRefund(orderInfo, refundOrder);
        }
        context.setExtraAmountObjRefundAmountForMeiTuan(autoSinkRefund);
        // 前置3

        // 订单支付信息
        OrderPayInfo orderPayInfo;
        //兼容B2C
        if (DsConstants.B2C.equals(orderInfo.getServiceMode())) {
            orderPayInfo = orderPayInfoMapper.getOrderPayInfoForB2C(refundOrder.getOrderNo());
        }else {
            orderPayInfo = orderBasicService.getOrderPayInfoWithCheck(refundOrder.getOrderNo());
        }

        context.setOrderPayInfo(orderPayInfo);
        //外部 orderPayInfo 引用赋值
        orderPayInfoOut.setMedicareAmount(orderPayInfo.getMedicareAmount());
        // 饿百 退款单 佣金 检验
        refundHandlerService.partRefundCommissionCheckEBAI(orderInfo, orderPayInfo, refundOrder);

        //校验饿百佣金，会抛出异常
        refundHandlerService.checkEbaiCommissionFlag(refundOrder.getOrderNo(), refundOrder.getThirdPlatformCode(), refundOrder);
        //处理饿百零佣金退款单，获取到佣金并重算退款单后下账ERP数据错误问题
        //查询网店下账配置
//        StoreBillConfig storeBillConfig = storeBillConfigService.getBillConfigById(orderInfo.getClientConfId());
//        context.setStoreBillConfig(storeBillConfig);

//        List<RefundDetail> refundDetailList = refundDetailMapper.selectListByRefundNo(refundOrder.getRefundNo());
//        context.setRefundDetailList(refundDetailList);
        //查询ERP支付方式
//        List<Long> ids = new ArrayList<>();
//        ids.add(orderInfo.getClientConfId());
//        List<ErpPayMode> erpPayModeList = erpPayModeService.getErpPayCodeByClientConfId(ids);

        // 规避下账脏数据
        // 查询下账金额信息
//        ErpRefundInfo erpRefundInfo = erpRefundInfoMapper.selectOne(
//                new QueryWrapper<ErpRefundInfo>().lambda().eq(ErpRefundInfo::getRefundNo, refundOrder.getRefundNo())
//        );
//        context.setErpRefundInfo(erpRefundInfo);
//        String merCode = refundOrder.getMerCode();

        return KcPosGetReturnOrderDetailCallBackResponse.success(null);
    }


    private Boolean checkRefundAll(Long refundNo, OrderInfo orderInfo) {
        if (DsConstants.B2C.equals(orderInfo.getServiceMode())) { //B2C直接跳过
            return Boolean.TRUE;
        }
        RefundOrder refundOrder = refundOrderMapper.selectByRefundNo(refundNo);
        if (null == refundOrder) {
            throw ExceptionUtil.getWarnException(DsErrorType.ORDER_NO_INPUT_ERROR);
        }
        if (RefundTypeEnum.PART.getCode().equals(refundOrder.getType())) {
            List<RefundOrder> allRefund = refundOrderMapper.selectList(new QueryWrapper<RefundOrder>().lambda()
                    .eq(RefundOrder::getOrderNo, refundOrder.getOrderNo())
                    .lt(RefundOrder::getState, RefundStateEnum.REFUSED.getCode())
                    .eq(RefundOrder::getType, RefundTypeEnum.ALL.getCode())
            );
            if (!CollectionUtils.isEmpty(allRefund)) {
                    Optional<RefundOrder> first = allRefund.stream().filter(a -> a.getTotalFoodAmount().compareTo(refundOrder.getTotalFoodAmount()) == 0
                            && a.getTotalAmount().compareTo(refundOrder.getTotalAmount()) == 0).findFirst();
                    if(first.isPresent()){
                        RefundOrder update=new RefundOrder();
                        update.setErpState(RefundErpStateEnum.CANCELED.getCode());
                        update.setState(RefundStateEnum.REFUSED.getCode());
                        refundOrderMapper.update(update,new LambdaQueryWrapper<RefundOrder>().eq(RefundOrder::getOrderNo, refundOrder.getOrderNo()));
                        RefundOrder allRefundOrder = first.get();
                        allRefundOrder.setErpState(RefundErpStateEnum.WAIT_REFUND.getCode());
                        allRefundOrder.setState(RefundStateEnum.SUCCESS.getCode());
                        refundOrderMapper.update(allRefundOrder,new LambdaQueryWrapper<RefundOrder>().eq(RefundOrder::getRefundNo, allRefundOrder.getRefundNo()));
                        return Boolean.FALSE;
                    }else {
                        throw ExceptionUtil.getWarnException(DsErrorType.REFUND_NOT_BILL.getCode(), DsErrorType.REFUND_NOT_BILL.getMsg() + "" + refundNo);
                    }
            }
        }
        return Boolean.TRUE;
    }


}
