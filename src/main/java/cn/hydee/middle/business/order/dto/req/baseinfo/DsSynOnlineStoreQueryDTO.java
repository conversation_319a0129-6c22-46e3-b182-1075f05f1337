package cn.hydee.middle.business.order.dto.req.baseinfo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class DsSynOnlineStoreQueryDTO {

    @NotNull(message = "o2o平台编码必传")
    @ApiModelProperty(value = "o2o平台编码",required = true)
    private String platformCode;

    @NotNull(message = "o2o平台名称必传")
    @ApiModelProperty(value = "o2o平台名",required = true)
    private String platformName;

    @NotNull(message = "网店编码必传")
    @ApiModelProperty(value = "网店编码",required = true)
    private String clientCode;

    @NotNull(message = "网店名称必传")
    @ApiModelProperty(value = "网店名称",required = true)
    private String clientName;

    @ApiModelProperty(value = "线下门店编码")
    private String organizationCode;

    @ApiModelProperty(value = "商户编码")
    private String merCode;

}
