package cn.hydee.middle.business.order.service;

import cn.hydee.middle.business.order.domain.OrderDetailDomain;
import cn.hydee.middle.business.order.dto.erp.BatchStockReqDto;
import cn.hydee.middle.business.order.dto.erp.inner.BatchStock;
import cn.hydee.middle.business.order.dto.req.*;
import cn.hydee.middle.business.order.dto.rsp.BatchStockResDto;
import cn.hydee.middle.business.order.dto.rsp.OrderPickSearchDtoRsp;
import cn.hydee.middle.business.order.dto.rsp.PickConfirmCacheRspDto;
import cn.hydee.middle.business.order.entity.OrderDetail;

import java.util.List;

/**
 * <p>
 * 订单明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-11
 */
public interface OrderDetailService {
    // 获取商品批号库存
    List<BatchStock> getErpBatchStock(String merCode, BatchStockReqDto batchStockReqDto);

    BatchStockResDto getBatchStock(String merCode, BatchStockReqDto batchStockReqDto);

    /**
     * 获取手动录入的商品批号
     * @param orderNo
     * @return
     */
    List<BatchStock> getErpBatchStock(Long orderNo);

    /**
     * 获取手动录入的商品批号（根据erpCode合并拣货明细版）
     */
    List<BatchStock> getErpBatchStockV2(Long orderNo);

    // 商家换货
    void merchantModifyDetail(String merCode, String userId, OrderHandleModifyDetailReqDto orderHandleReqDto, String orderNo);
    // 商品erp编码商品替换
    void merchantModifyErpDetail(String merCode, String userId, OrderHandleModifyDetailReqDto orderHandleReqDto, String orderNo);
    /** 获取下单未卖出的商品数量 */
    Integer getGoodsCountInOrder(String merCode, String erpCode, String organizationCode);

    /** 明细id+订单号验证 返回批号列表 **/
    List<OrderPickSearchDtoRsp> getErpBatchStockList(OrderDetailBatchNoReqDto reqDto);

    /** 根据erp编码获取商品信息 **/
    void  recaptureDetails(RecaptureReqDto recaptureReqDto);

    /** 去掉订单中商品的异常信息 **/
    void deleteOrderDetailsExceptionState(Long orderNo,String merCode);

    List<OrderDetailDomain> loadOrderDetail(Long orderNo);

    /**
     * 复杂换货
     * */
    void merchantComplexModifyErpDetail(String merCode, String userId, ComplexModifyOrderDetailReqDTO complexModifyOrderDetailReqDTO, String orderNo);

    /**
     * 缓存拣货复核的数据
     * @param param
     */
    void cachePickConfirmInfo(PickConfirmCacheReqDto param);

    /**
     * 缓存拣货复核的数据(根据erpCode合并拣货明细版)
     * @param param
     */
    void cachePickConfirmInfoV2(PickConfirmCacheReqDto param);

    /**
     * 获取拣货复核的缓存数据
     * @param param
     * @return
     */
    PickConfirmCacheRspDto getCachePickConfirmInfo(PickConfirmCacheReqDto param);

    /**
     * 获取拣货复核的缓存数据(根据erpCode合并拣货明细版)
     * @param param
     * @return
     */
    PickConfirmCacheRspDto getCachePickConfirmInfoV2(PickConfirmCacheReqDto param);

    /**
     * 清除拣货复核缓存的数据
     * @Author: chufeng(2910)
     * @Date: 2021/7/23 21:37
     */
    void clearPickConfirmCacheInfo(Long orderNo);

    /**
     * 校验是否通过拣货复核
     * @param param
     * @return
     */
    Boolean checkPickConfirm(PickConfirmCacheReqDto param);
    /**
     * 校验是否通过拣货复核(根据erpCode合并拣货明细版)
     */
    Boolean checkPickConfirmV2(PickConfirmCacheReqDto param);

    List<OrderDetail> queryByOrderNoErpCodes(List<Long> orderNoList, List<String> erpCodeList);
}
