package cn.hydee.middle.business.order.route.domain.impl;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.Enums.DsErrorType;
import cn.hydee.middle.business.order.Enums.RefundTypeEnum;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.EmployeeResDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.SysEmployeeResDTO;
import cn.hydee.middle.business.order.entity.*;
import cn.hydee.middle.business.order.exception.OmsException;
import cn.hydee.middle.business.order.feign.MiddleIdClient;
import cn.hydee.middle.business.order.mapper.*;
import cn.hydee.middle.business.order.route.db.mysql.model.RouteAllot;
import cn.hydee.middle.business.order.route.db.mysql.model.RouteAllotDetail;
import cn.hydee.middle.business.order.route.domain.AllotAggregate;
import cn.hydee.middle.business.order.route.domain.AllotDomainService;
import cn.hydee.middle.business.order.route.domain.enums.AllotLogEnum;
import cn.hydee.middle.business.order.route.domain.enums.AllotStatusEnum;
import cn.hydee.middle.business.order.route.domain.enums.AllotTypeEnum;
import cn.hydee.middle.business.order.route.domain.external.RouteClientService;
import cn.hydee.middle.business.order.route.domain.repository.AllotRepository;
import cn.hydee.middle.business.order.service.async.HydeeEsSyncClientAsync;
import cn.hydee.middle.business.order.util.EnterpriseWeChatUtil;
import cn.hydee.middle.business.order.util.ExceptionUtil;
import cn.hydee.middle.business.order.v2.manager.base.BaseInfoManager;
import cn.hydee.middle.business.order.yxtadapter.domain.baseinfo.SysOrganizationDto;
import cn.hydee.middle.business.order.yxtadapter.domain.dictionary.InnerStoreDictionary;
import cn.hydee.middle.business.order.yxtadapter.domain.pos.hdpos.AllotCallBackRequest;
import cn.hydee.middle.business.order.yxtadapter.domain.pos.hdpos.AllotRequest;
import cn.hydee.middle.business.order.yxtadapter.domain.pos.hdpos.AllotResponse;
import cn.hydee.middle.business.order.yxtadapter.infrastructure.feign.PosInterfaceClient;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.util.UUIDUtil;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/04/01 17:46
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class AllotDomainServiceImpl implements AllotDomainService {

    private final AllotRepository allotRepository;
    private final OrderInfoMapper orderInfoMapper;
    private final OrderDetailMapper orderDetailMapper;
    private final OrderPickInfoMapper orderPickInfoMapper;
    private final RefundDetailMapper refundDetailMapper;
    private final RouteClientService routeClientService;
    private final PosInterfaceClient posInterfaceClient;
    private final InnerStoreDictionaryMapper innerStoreDictionaryMapper;
    private final EnterpriseWeChatUtil enterpriseWeChatUtil;
    private final BaseInfoManager baseInfoManager;
    private final MiddleIdClient middleIdClient;

    /* 海典调拨同步开关 */
    @Value("${allotAsyncFlag:true}")
    private Boolean allotAsyncFlag;

    @Override
    public boolean sendHdCreateOrderAllot(OrderInfo orderInfo, List<OrderDetail> orderDetails,List<OrderPickInfo> orderPickInfos) {
        RouteAllot allot = allotRepository.getByOmsNoAndType(orderInfo.getOrderNo(), AllotTypeEnum.ORDER.name());
        if (Objects.isNull(allot)) {
            AllotAggregate aggregate = new AllotAggregate();
            List<String> codes = Stream.of(orderInfo.getOrganizationCode(), orderInfo.getSourceOrganizationCode()).distinct().collect(Collectors.toList());
            List<SysOrganizationDto> sysOrganizationDtos = routeClientService.batchGetOrgByCodes(codes);
            Map<String, String> pathMap = sysOrganizationDtos.stream().collect(Collectors.toMap(SysOrganizationDto::getOrCode, SysOrganizationDto::getParentPath));
            EmployeeResDTO employeeResDTO = routeClientService.queryEmpByCondition(orderInfo.getMerCode(), orderInfo.getSourceOrganizationCode());
            Long omsAllotNo = middleIdClient.getId(1).get(0);
            aggregate.createOrderAggregate(orderInfo, orderDetails,orderPickInfos, employeeResDTO.getEmpCode(),pathMap,omsAllotNo);
            allotRepository.save(aggregate);
            allot = allotRepository.getByOmsNoAndType(aggregate.getOmsNo(), AllotTypeEnum.ORDER.name());
            List<RouteAllotDetail> allotDetails = allotRepository.getAllotDetailByAllotId(allot.getId());
            //新增ES日志
            StringBuilder sb =new StringBuilder();
            allotDetails.stream().forEach(detail -> sb.append("商品编码：").append(detail.getErpCode()).append(",")
                    .append("商品批号：").append(detail.getCommodityBatchNo()).append(",")
                    .append("数量：").append(detail.getCount()).append(";"));
            HydeeEsSyncClientAsync.allotLogToES(DsConstants.INTEGER_ONE,"500001",allot.getId().toString(), AllotLogEnum.CREATE_TRANSFER_ALLOT.getAction(),sb.toString(),null);
        }
        log.info("推送销售调拨单: orderNo:{}", orderInfo.getOrderNo());
        return sendAllotToHd(allot);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public RouteAllot saveAllot(AllotAggregate allotAggregate){
        allotRepository.save(allotAggregate);
        RouteAllot allot = allotRepository.getByOmsNoAndType(allotAggregate.getOmsNo(), AllotTypeEnum.ORDER.name());
        HydeeEsSyncClientAsync.allotLogToES(DsConstants.INTEGER_ONE,"500001",allot.getId().toString(), AllotLogEnum.CREATE_TRANSFER_ALLOT.getAction(),"创建调拨单",null);
        return allot;
    }

    @Override
    public boolean sendAllotToHd(RouteAllot allot) {
        if (AllotStatusEnum.SUCCESS.name().equals(allot.getAllotStatus())) {
            return true;
        }
        List<RouteAllotDetail> refundAllotDetails = allotRepository.getAllotDetailByAllotId(allot.getId());
        //拼接海典报文
        AllotRequest allotRequest = new AllotRequest();
        allotRequest.setDistNo(allot.getOmsAllotNo().toString());
        allotRequest.setSrcstoreCode(allot.getOutStoreCode());
        allotRequest.setObjstoreCode(allot.getInStoreCode());
        allotRequest.setCreateUser(allot.getCreateUser());
        allotRequest.setChekckUser(allot.getAuditUser());
        List<AllotRequest.ErpCode> erpCodeList = new ArrayList<>();
        refundAllotDetails.forEach(detail -> {
            AllotRequest.ErpCode erpCode = new AllotRequest.ErpCode();
            erpCode.setErpCode(detail.getErpCode());
            erpCode.setGoodsName(detail.getGoodName());
            erpCode.setBatchNo(detail.getCommodityBatchNo());
            // 订单中商品最细粒度只到batchNo，batId一直为空
            erpCode.setBatId("");
            erpCode.setGoodsCount(detail.getCount());
            erpCodeList.add(erpCode);
        });
        allotRequest.setErpCodeList(erpCodeList);
        if(CollectionUtils.isEmpty(erpCodeList)){
            log.error("调拨明细为空 allotId:{}",allot.getId());
            return false;
        }
        InnerStoreDictionary innerStoreDictionary = innerStoreDictionaryMapper.selectByOrganizationCode(allot.getInStoreCode());
        if(allotAsyncFlag){
            log.info("海典同步调拨：omsNo:{}", allot.getOmsNo());
            allotRepository.updateAllotById(allot.getId(), AllotStatusEnum.PROCEED.name(),null);
            ResponseBase<AllotResponse> responseBase = null;
            try{
                responseBase = posInterfaceClient.sendRouteAllot(allotRequest, innerStoreDictionary);
            }catch (Exception e){
                log.error("调用海典H2推送调拨单异常：{}", e.getMessage());
                allotRepository.updateAllotById(allot.getId(), AllotStatusEnum.FAIL.name(),null);
                HydeeEsSyncClientAsync.allotLogToES(DsConstants.INTEGER_ZERO,"500001",allot.getId().toString(), AllotLogEnum.SEND_ALLOT_FAIL.getAction(),"调用海典推送调拨单接口异常",null);
                enterpriseWeChatUtil.sendMsg(DsConstants.ALLOT_ORDER_ERROR, UUIDUtil.generateUuid(),allot.getThirdOrderNo(),e.getMessage());
                return false;
            }
            if (DsConstants.STRING_ONE.equals(responseBase.getCode())){
                allotRepository.updateAllotById(allot.getId(), AllotStatusEnum.SUCCESS.name(), responseBase.getData().getPosdistNo());
                HydeeEsSyncClientAsync.allotLogToES(DsConstants.INTEGER_ONE,"500001",allot.getId().toString(), AllotLogEnum.SEND_ALLOT_SUCCESS.getAction(),AllotLogEnum.SEND_ALLOT_SUCCESS.getInfo(),null);
                return true;
            }else{
                allotRepository.updateAllotById(allot.getId(), AllotStatusEnum.FAIL.name(),responseBase.getMsg(), null);
                HydeeEsSyncClientAsync.allotLogToES(DsConstants.INTEGER_ZERO,"500001",allot.getId().toString(), AllotLogEnum.SEND_ALLOT_FAIL.getAction(),responseBase.getMsg(),null);
                return false;
            }
        }else{
            log.info("海典异步调拨：omsNo:{}", allot.getOmsNo());
            ResponseBase<AllotResponse> responseBase = null;
            try{
                responseBase = posInterfaceClient.sendRouteAllot(allotRequest, innerStoreDictionary);
            }catch (Exception e){
                log.error("调用海典H2推送调拨单异常：{}", e.getMessage());
                allotRepository.updateAllotById(allot.getId(), AllotStatusEnum.FAIL.name(),null);
                HydeeEsSyncClientAsync.allotLogToES(DsConstants.INTEGER_ZERO,"500001",allot.getId().toString(), AllotLogEnum.SEND_ALLOT_FAIL.getAction(),"调拨失败：调用海典推送调拨单接口异常",null);
                enterpriseWeChatUtil.sendMsg(DsConstants.ALLOT_ORDER_ERROR, UUIDUtil.generateUuid(),allot.getThirdOrderNo(),e.getMessage());
                return false;
            }
            if (DsConstants.STRING_ONE.equals(responseBase.getCode())){
                allotRepository.updateAllotById(allot.getId(), AllotStatusEnum.PROCEED.name(), responseBase.getData().getPosdistNo());
                HydeeEsSyncClientAsync.allotLogToES(DsConstants.INTEGER_ONE,"500001",allot.getId().toString(), AllotLogEnum.SEND_ALLOT_SUCCESS.getAction(),AllotLogEnum.SEND_ALLOT_SUCCESS.getInfo(),null);
                return true;
            }else{
                allotRepository.updateAllotById(allot.getId(), AllotStatusEnum.FAIL.name(),responseBase.getMsg(),null);
                HydeeEsSyncClientAsync.allotLogToES(DsConstants.INTEGER_ZERO,"500001",allot.getId().toString(), AllotLogEnum.SEND_ALLOT_FAIL.getAction(),"调拨失败：" + responseBase.getMsg(),null);
                return false;
            }
        }
    }

    @Override
    public boolean sendHdCreateRefundAllot(RefundOrder refundOrder) {
        RouteAllot allot = allotRepository.getByOmsNoAndType(refundOrder.getRefundNo(), AllotTypeEnum.REFUND.name());
        if (Objects.isNull(allot)) {
            //正单调拨单
            RouteAllot orderAllot = allotRepository.getByOmsNoAndType(refundOrder.getOrderNo(), AllotTypeEnum.ORDER.name());
            List<RouteAllotDetail> allotDetails = allotRepository.listAllotDetail(orderAllot.getId());
            List<RefundDetail> refundDetailList = refundDetailMapper.selectListByRefundNo(refundOrder.getRefundNo());
            AllotAggregate aggregate = new AllotAggregate();
            Long omsAllotNo = middleIdClient.getId(1).get(0);
            aggregate.createRefundAggregate(orderAllot,refundOrder,refundDetailList,allotDetails,omsAllotNo);
            log.info("创建退款调拨单: refundNo:{}", refundOrder.getRefundNo());
            allotRepository.save(aggregate);
            allot = allotRepository.getByOmsNoAndType(refundOrder.getRefundNo(), AllotTypeEnum.REFUND.name());
            List<RouteAllotDetail> refundAllotDetails = allotRepository.getAllotDetailByAllotId(allot.getId());
            StringBuilder sb =new StringBuilder();
            refundAllotDetails.forEach(detail -> sb.append("商品编码：").append(detail.getErpCode()).append(",")
                    .append("商品批号：").append(detail.getCommodityBatchNo()).append(",")
                    .append("数量：").append(detail.getCount()).append(";"));
            HydeeEsSyncClientAsync.allotLogToES(DsConstants.INTEGER_ONE,"500001",allot.getId().toString(), AllotLogEnum.CREATE_TRANSFER_ALLOT.getAction(),sb.toString(),null);
        }
        log.info("推送退款调拨单: refundNo:{}", refundOrder.getRefundNo());
        return sendAllotToHd(allot);
    }

    @Transactional
    @Override
    public boolean sendHdCreateRefundAllot(OrderInfo orderInfo, RefundOrder refundOrder) {
        //非转单订单直接返回
        if(orderInfo.getOrganizationCode().equals(orderInfo.getSourceOrganizationCode())){
            return true;
        }

        //查询正向调拨单 没有正向调拨单直接返回
        RouteAllot allot = allotRepository.getByOmsNoAndType(refundOrder.getOrderNo(), AllotTypeEnum.ORDER.name());
        if (Objects.isNull(allot)) {
            return true;
        }
        //判断退款类型 全额/部分
        if (refundOrder.getType().equals(RefundTypeEnum.ALL.getCode())) {
            switch (AllotStatusEnum.valueOf(allot.getAllotStatus())) {
                case SUCCESS:
                    //正单调拨单成功创建退款调拨单
                   return  sendHdCreateRefundAllot(refundOrder);
                case CANCEL:
                    return true;
                case FAIL:
                case PROCEED:
                case WAIT:
                    //正单调拨单处理中
                    allotRepository.updateAllotById(allot.getId(), AllotStatusEnum.CANCEL.name(), null);
                    HydeeEsSyncClientAsync.allotLogToES(DsConstants.INTEGER_ONE,"500001",allot.getId().toString(), AllotLogEnum.TRANSFER_ALLOT_CANCEL_ALL.getAction(),AllotLogEnum.TRANSFER_ALLOT_CANCEL_ALL.getInfo(),null);
                    return true;
                default:
                    throw new OmsException("退款调拨单创建失败");
            }
        }

        if (refundOrder.getType().equals(RefundTypeEnum.PART.getCode())) {
            switch (AllotStatusEnum.valueOf(allot.getAllotStatus())) {
                case SUCCESS:
                    //正单调拨单成功创建退款调拨单
                    return sendHdCreateRefundAllot(refundOrder);
                case CANCEL:
                    return true;
                case FAIL:
                case PROCEED:
                case WAIT:
                    //正单调拨单处理中
                    allotRepository.updateAllotById(allot.getId(), AllotStatusEnum.CANCEL.name(), null);
                    HydeeEsSyncClientAsync.allotLogToES(DsConstants.INTEGER_ONE,"500001",allot.getId().toString(), AllotLogEnum.TRANSFER_ALLOT_CANCEL_PART.getAction(),AllotLogEnum.TRANSFER_ALLOT_CANCEL_PART.getInfo(),null);
                    return createNewOrderAllot(orderInfo.getOrderNo(), allot);
                default:
                    throw new OmsException("退款调拨单创建失败");
            }
        }
        return false;
    }

    @Override
    public void routeAllotCallback(AllotCallBackRequest request) {
        RouteAllot routeAllot = allotRepository.getAllotByOmsAllotNo(Long.parseLong(request.getOmsAllotNo()));
        if(Objects.isNull(routeAllot)){
            log.error("调拨回调查询调拨单为空 omsAllotNo:{}", request.getOmsAllotNo());
            throw ExceptionUtil.getWarnException(DsErrorType.PARAM_VALID_ERROR.getCode(),"调拨单不存在");
        }
        allotRepository.updateAllotById(routeAllot.getId(),request.getAllotStatus(), request.getFailMessage(), request.getPosAllotNo());
        if(AllotStatusEnum.SUCCESS.name().equals(request.getAllotStatus())){
            HydeeEsSyncClientAsync.allotLogToES(DsConstants.INTEGER_ONE,"500001",routeAllot.getId().toString(), AllotLogEnum.ALLOT_SUCCESS.getAction(),AllotLogEnum.ALLOT_SUCCESS.getInfo(),null);
        }
        if(AllotStatusEnum.FAIL.name().equals(request.getAllotStatus())){
            HydeeEsSyncClientAsync.allotLogToES(DsConstants.INTEGER_ZERO,"500001",routeAllot.getId().toString(), AllotLogEnum.SEND_ALLOT_FAIL.getAction(),"调拨失败：" + request.getFailMessage(),null);
        }
    }

    /**
     * 创建新的
     * */
    public boolean createNewOrderAllot(Long orderNo, RouteAllot routeAllot) {
        OrderInfo orderInfo = orderInfoMapper.selectOrderInfo(orderNo);
        List<OrderDetail> orderDetails = orderDetailMapper.selectListByOrderNo(orderNo);
        List<OrderPickInfo> orderPickInfos = orderPickInfoMapper.selectPickInfoListByOrderNo(orderNo);
        //创建新的调拨单
        AllotAggregate allotAggregate = new AllotAggregate();
        List<String> codes = Stream.of(orderInfo.getOrganizationCode(), orderInfo.getSourceOrganizationCode()).distinct().collect(Collectors.toList());
        List<SysOrganizationDto> sysOrganizationDtos = routeClientService.batchGetOrgByCodes(codes);
        Map<String, String> pathMap = sysOrganizationDtos.stream().collect(Collectors.toMap(SysOrganizationDto::getOrCode, SysOrganizationDto::getParentPath));
        Long omsAllotNo = middleIdClient.getId(1).get(0);
        allotAggregate.createOrderAggregate(orderInfo, orderDetails, orderPickInfos, routeAllot.getAuditUser(),pathMap,omsAllotNo);
        log.info("部分退款后创建新销售调拨单: orderNo:{}", orderInfo.getOrderNo());
        allotRepository.save(allotAggregate);
        RouteAllot allot = allotRepository.getByOmsNoAndTypeAndStatus(orderInfo.getOrderNo(), AllotTypeEnum.ORDER.name(),AllotStatusEnum.WAIT.name());
        List<RouteAllotDetail> allotDetails = allotRepository.getAllotDetailByAllotId(allot.getId());
        StringBuilder sb =new StringBuilder();
        allotDetails.forEach(detail -> sb.append("商品编码：").append(detail.getErpCode()).append(",")
                .append("商品批号：").append(detail.getCommodityBatchNo()).append(",")
                .append("数量：").append(detail.getCount()).append(";"));
        HydeeEsSyncClientAsync.allotLogToES(DsConstants.INTEGER_ONE,"500001",allot.getId().toString(), AllotLogEnum.CREATE_TRANSFER_ALLOT.getAction(),sb.toString(),null);
        log.info("部分退款后推送新销售调拨单: orderNo:{}", orderInfo.getOrderNo());
        return sendAllotToHd(allot);
    }
}
