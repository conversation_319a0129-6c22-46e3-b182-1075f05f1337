package cn.hydee.middle.business.order.Enums;

/**
 * <AUTHOR>
 */
public enum BillMultiPayTypeEnum {

    NOT_BILL_POST_AMOUNT("809086", "配送费不下账"),
    NOT_BILL_COMMODITY_DETAIL_DISCOUNT("809087", "明细优惠不下账"),
    COMMISSION_BILL("809089", "佣金下账")
    ;

    private String code;
    private String name;

    BillMultiPayTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
