package cn.hydee.middle.business.order.controller;

import cn.hydee.middle.business.order.Enums.*;
import cn.hydee.middle.business.order.Enums.refundReason.RefundReasonEnum;
import cn.hydee.middle.business.order.domain.RefundOrderInfoAllDomain;
import cn.hydee.middle.business.order.dto.req.*;
import cn.hydee.middle.business.order.dto.rsp.*;
import cn.hydee.middle.business.order.entity.RefundOrder;
import cn.hydee.middle.business.order.service.RefundService;
import cn.hydee.middle.business.order.service.VerifyService;
import cn.hydee.middle.business.order.service.rocket.MessageProducerService;
import cn.hydee.middle.business.order.util.ExceptionUtil;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ErrorType;
import cn.hydee.starter.dto.ResponseBase;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/31 17:46
 */
@Slf4j
@RestController
@RequestMapping("/${api.version}/ds/refund")
@Validated
@Api(tags = "退款单接口")
public class RefundOrderController extends AbstractController {

    @Autowired
    private RefundService refundService;

    @Autowired
    private VerifyService verifyService;

    @Autowired
    private MessageProducerService messageProducerService;

    @ApiOperation(value = "退款中列表", notes = "退款中列表")
    @PostMapping("/ing/page")
    public ResponseBase<IPage<RefundDetailRsp>> refundingList(
            @RequestHeader("merCode") String merCode,
            @RequestHeader("userId") String userId,
            @Valid @RequestBody RefundPageReqDto refundPageReqDto){
        refundPageReqDto.setMerCode(merCode);
        refundPageReqDto.setUserId(userId);
        IPage<RefundDetailRsp>  page= refundService.getRefundingPage(refundPageReqDto);
        return generateSuccess(page);
    }

    @ApiOperation(value = "退款单列表(所有)", notes = "退款单列表（所有）")
    @PostMapping("/page")
    public ResponseBase<IPage<RefundDetailRsp>> list(
                        @RequestHeader("merCode") String merCode,
                        @RequestHeader("userId") String userId,
                        @Valid @RequestBody RefundPageReqDto refundPageReqDto){
        refundPageReqDto.setMerCode(merCode);
        refundPageReqDto.setUserId(userId);
        return generateSuccess(refundService.getRefundAllPage(refundPageReqDto));
    }

    @ApiOperation(value = "退款单详情", notes = "退款单详情")
    @PostMapping("/detail/{refundNo}")
    public ResponseBase<RefundOrderInfoAllDomain> detail(@PathVariable Long refundNo){
        return generateSuccess(refundService.getRefundDetail(refundNo));
    }

    @ApiOperation(value = "订单号查询退款单详情", notes = "订单号查询退款单详情")
    @PostMapping("/detail/order/{orderNo}")
    public ResponseBase<RefundOrderInfoAllDomain> detailByOrderNo(@PathVariable Long orderNo){
        return generateSuccess(refundService.getRefundDetailByOrderNo(orderNo));
    }

    @ApiOperation(value = "卖家审核退款，1同意仅退款，2同意退货退款，3拒绝", notes = "卖家审核退款")
    @PostMapping("/audit")
    public ResponseBase<Void> refundAudit(@RequestHeader("merCode") String merCode,
                                             @RequestHeader("userId") String userId,
                                             @Valid @RequestBody RefundAuditHandleReq refundHandle){
        log.info("refundAudit,请求参数：{}", JSON.toJSONString(refundHandle));
        RefundOrderInfoAllDomain refundOrder = refundService.getRefundDetail(refundHandle.getRefundNo());
        if (refundOrder == null) {
            throw ExceptionUtil.getWarnException(DsErrorType.REFUND_ORDER_IS_NOT_EXIST);
        }
        if (!DsConstants.INTEGER_THREE.equals(refundHandle.getType()) && RefundTypeEnum.PART.getCode().equals(refundOrder.getType()) && CollectionUtils.isEmpty(refundOrder.getRefundDetailList())) {
            throw ExceptionUtil.getWarnException(DsErrorType.REFUND_NO_DETAIL_ERROR);
        }

        if (RefundAuditTypeEnum.MONEY_ONLY.getCode().equals(refundHandle.getType())){
            refundService.refundAgreeOnlyMoney(merCode, userId, refundHandle,String.valueOf(refundOrder.getOrderNo()));
        } else if (RefundAuditTypeEnum.MONEY_AND_GOODS.getCode().equals(refundHandle.getType())){
            // 退货退款
            refundService.refundAgreeWithGoods(merCode, userId, refundHandle,String.valueOf(refundOrder.getOrderNo()));
        } else {
            refundService.refundRefuse(merCode, userId, refundHandle,String.valueOf(refundOrder.getOrderNo()));
        }
        //订单更新异步消费
        messageProducerService.produceUpdateOrderMessage(Collections.singletonList(refundOrder.getOrderNo()), OrderUpdateCodeEnum.AGREE_REFUND.getCode());

        return generateSuccess(null);
    }

    @ApiOperation(value = "退库(确认收货)或者报损（验货不通过/未收到货）操作", notes = "退库(确认收货)或者报损（验货不通过/未收到货）操作")
    @PostMapping("/stock/operation")
    public ResponseBase<Void> returnOperation(@RequestHeader("merCode") String merCode,
                                           @RequestHeader("userId") String userId,
                                           @Valid @RequestBody RefundReturnOperationHandleReq refundHandle){
        log.info("returnOperation merCode:{},userId:{},refundHandle:{}",merCode,userId, JSON.toJSONString(refundHandle));
        //20210511 调整，如果退款单已完成，则直接提示
        RefundOrder refundOrder = refundService.getRefundByRefundNoWhithCheck(refundHandle.getRefundNo());
        // 退款单已完成，不接受新的退款单消息
        if (refundOrder.getState() >= RefundStateEnum.SUCCESS.getCode()) {
            throw ExceptionUtil.getWarnException(DsErrorType.REFUND_ORDER_STATENOTRIGHT);
        }
        try {
            refundService.refundStockOperation(merCode, userId, refundHandle,String.valueOf(refundOrder.getOrderNo()));
        } catch (Exception e) {
            log.warn("退单下账失败", e);
            throw ExceptionUtil.getWarnException(DsErrorType.REFUND_ORDER_SYNC_ACCOUNT_FAIL);
        }
        //订单更新异步消费
        messageProducerService.produceUpdateOrderMessage(Collections.singletonList(refundOrder.getOrderNo()), OrderUpdateCodeEnum.AGREE_REFUND_GOOD.getCode());
        return generateSuccess(null);
    }

    @ApiOperation(value = "修改退款金额", notes = "修改退款金额")
    @PutMapping("/updateRefundAmount")
    public ResponseBase<Void> updateRefundAmount(@Valid @RequestBody RefundAmountReqDTO refundAmountReqDTO, BindingResult result) {
        this.checkValid(result);
        RefundOrder refundOrder = refundService.getRefundByRefundNoWhithCheck(refundAmountReqDTO.getRefundNo());
        if(null == refundOrder){
            throw ExceptionUtil.getWarnException(DsErrorType.ORDER_DATA_CONTAINS_NUMBERS);
        }
        refundService.updateRefundAmount(refundAmountReqDTO);
        //订单更新异步消费
        messageProducerService.produceUpdateOrderMessage(Collections.singletonList(refundOrder.getOrderNo()), OrderUpdateCodeEnum.AGREE_REFUND_GOOD.getCode());
        return generateSuccess(null);
    }

    @ApiOperation(value="退款单下账列表数量",notes="退款单下账列表数量")
    @PostMapping("/RefundLedgerCount")
    public ResponseBase<Integer> getRefundLedgerCount(@RequestHeader("merCode")String merCode,
                                                      @RequestHeader("userId") String userId,
                                                      @RequestBody @Valid  RefundLedgerReqDto req){
        req.setMerCode(merCode);
        if(req.getOrganizationCode() == null){
            throw ExceptionUtil.getWarnException(DsErrorType.ORGANIZATION_CODE_IS_NULL);
        }
        Integer num = refundService.getRefundLedgerCount(req,userId);
        return generateSuccess(num);
    }

    @ApiOperation(value="退款单下账列表",notes="退款单下账列表")
    @PostMapping("/RefundLedgerList")
    public ResponseBase<IPage<RefundDetailRsp>>  getRefundLedgerList(@RequestHeader("merCode")String merCode,@RequestHeader("userId") String userId,@RequestBody @Valid  RefundLedgerReqDto req){
        req.setMerCode(merCode);
        IPage<RefundDetailRsp> page = null;
        if(req.getOrganizationCode() == null){
            throw ExceptionUtil.getWarnException(DsErrorType.ORGANIZATION_CODE_IS_NULL);
        }
        VerifyRspDto verifyDto = verifyService.verifyTimeAndOrganization(VerifyReqDto.builder()
                .merCode(merCode)
                .organizationCode(req.getOrganizationCode())
                .userId(userId)
                .storeFlag(0)
                .verifyFlag(1)
                .beginTime(req.getBillStartTime())
                .endTime(req.getBillEndTime())
                .build());

        List<String> platformCodeList = verifyService.verifyPlatformCodeAndGet(merCode, userId, Collections.singletonList(req.getPlatformCode()));

        if(req.getOrganizationCode().equals(DsConstants.ORGANIZATION_CODE_ALL)) {
            page = refundService.getRefundLedgerAllList(req,verifyDto.getOrganizatioinList(),platformCodeList);
        }else{
            page = refundService.getRefundLedgerList(req,platformCodeList);
        }
        return generateSuccess(page);
    }

    @ApiOperation(value="退款单下账",notes="退款单下账")
    @PostMapping("/bill")
    public ResponseBase<Void>  refundBill(@RequestHeader("merCode")String merCode,@RequestHeader("userId") String userId,
                                          @RequestBody @Valid  RefundBillReqDto req){
        refundService.refundBill(merCode,userId,req);
        return generateSuccess(null);
    }

    @ApiOperation(value="平安退款单下账",notes="平安退款单下账")
    @PostMapping("/platBill")
    public ResponseBase<Void> platBill(@RequestHeader("merCode")String merCode,
                                       @RequestHeader("userId") String userId,
                                       @RequestBody @Valid  PlatRefundBillReqDto req){
        refundService.platRefundBill(merCode,userId,req);
        return generateSuccess(null);
    }


    @ApiOperation(value="阿里退款单下账",notes="阿里退款单下账")
    @PostMapping("/aliBill")
    public ResponseBase<Void> aliBill(@RequestHeader("merCode")String merCode,
                                       @RequestHeader("userId") String userId,
                                       @RequestBody @Valid  PlatRefundBillReqDto req){
        refundService.aliRefundBill(merCode,userId,req);
        return generateSuccess(null);
    }



    @ApiOperation(value = "获取OMS存储的微商城退货单号列表", notes = "获取OMS存储的微商城退货单号列表")
    @PostMapping("/list/getYdjRefundNoList")
    public ResponseBase<List<String>> getYdjRefundNoList(@RequestBody List<String> reqYdjRefundNoList){
    	if (CollectionUtils.isEmpty(reqYdjRefundNoList)) {
    		return ResponseBase.error(ErrorType.PARA_ERROR);
    	}
    		
        List<String> ydjRefundNoList = refundService.getYdjRefundNoList(reqYdjRefundNoList);
        return generateSuccess(ydjRefundNoList);
    }

    @ApiOperation(value = "检查部分退款合法性", notes = "检查部分退款合法性")
    @GetMapping("/refund/part/valid")
    public ResponseBase<PartRefundCheck> checkPartRefundValid(@ApiParam("平台编号") @RequestParam("platform") String platformCode,
                                                              @ApiParam("订单号") @RequestParam("order_no") String orderNo) {
        return generateSuccess(refundService.checkPartRefundValid(platformCode, orderNo));
    }

    @ApiOperation(value = "手动创建京东、阿里健康、同城购退款单", notes = "手动创建京东、阿里健康、同城购退款单")
    @PostMapping("/refund/createJdAliRefundOrder")
    public ResponseBase<Void> createJdAliRefundOrder(@RequestHeader("merCode")String merCode,
                                                  @RequestHeader("userId") String userId,
                                                  @RequestBody @Valid RefundOrderCreateDto req) {
        refundService.createJdAliRefundOrder(merCode,userId, req);
        //订单更新异步消费
        messageProducerService.produceUpdateOrderMessage(Collections.singletonList(req.getOrderNo()), OrderUpdateCodeEnum.QUERY_REFUND_ORDER.getCode());
        return generateSuccess(null);
    }

    @ApiOperation(value = "针对复杂换货部分退款创建退款单", notes = "针对复杂换货部分退款创建退款单")
    @PostMapping("/refund/createComplexModifyDetailPartRefundOrder")
    public ResponseBase<Void> createComplexModifyDetailPartRefundOrder(@RequestHeader("merCode")String merCode,
                                                     @RequestHeader("userId") String userId,
                                                     @RequestBody @Valid RefundOrderCreateDto req) {
        refundService.createComplexModifyDetailPartRefundOrder(merCode,userId, req);
        //由于事务原因agree须单独调
        refundService.agree(req.getRefundNo(), req.getOrderNo());
        //订单更新异步消费
        messageProducerService.produceUpdateOrderMessage(Collections.singletonList(req.getOrderNo()), OrderUpdateCodeEnum.QUERY_REFUND_ORDER.getCode());
        return generateSuccess(null);
    }

    @ApiOperation(value = "获取退款/退货审核拒绝原因",notes = "获取退款/退货审核拒绝原因")
    @PostMapping("/refund/reason")
    public ResponseBase<List<RefundReasonRespDto>> getRefundReason(@RequestBody RefundReasonReqDto reqDto){
        return generateSuccess(RefundReasonEnum.getRefundReason(reqDto.getThirdPlatformCode(),reqDto.getType()));
    }

    /** 付费会员订单同意退款 供会员中台调用
     * <AUTHOR>
     * @Description
     * @date 2024/10/31 15:38
     */
//    @ApiOperation(value = "付费会员订单同意退款", notes = "付费会员订单同意退款")
//    @PostMapping("/auditVip")
//    public ResponseBase<Void> auditVip(@Valid @RequestBody VipRefundAuditHandleReq vipRefundAuditHandleReq){
//        log.info("auditVip,付费会员订单同意退款请求参数：{}", JSON.toJSONString(vipRefundAuditHandleReq));
//        RefundOrder refundOrder = refundService.selectByThirdPlatAndThirdRefundNo(vipRefundAuditHandleReq.getThirdRefundNo());
//        if (refundOrder == null) {
//            throw ExceptionUtil.getWarnException(DsErrorType.REFUND_ORDER_IS_NOT_EXIST);
//        }
//        if(RefundStateEnum.SUCCESS.getCode().equals(refundOrder.getState())){
//            return generateSuccess(null);
//        }
//        RefundAuditHandleReq refundHandle=new RefundAuditHandleReq();
//        refundHandle.setRemark("会员中台调用同意付费会员订单退款！");
//        refundHandle.setType(RefundAuditTypeEnum.MONEY_ONLY.getCode());
//        refundHandle.setDoubleCheck(0);
//        refundHandle.setRefundNo(refundOrder.getRefundNo());
//        refundService.refundAgreeOnlyMoney(refundOrder.getMerCode(), vipRefundAuditHandleReq.getOperatorId(), refundHandle,String.valueOf(refundOrder.getOrderNo()));
//        //订单更新异步消费
//        messageProducerService.produceUpdateOrderMessage(Collections.singletonList(refundOrder.getOrderNo()), OrderUpdateCodeEnum.AGREE_REFUND.getCode());
//        return generateSuccess(null);
//
//    }

    @ApiOperation(value = "会员查询付费会员退款订单信息", notes = "会员查询付费会员退款订单信息")
    @PostMapping("/getVipRefundOrder")
    public ResponseBase<List<MemberVipRefundOrderVo>> getVipRefundOrder(@Valid  @RequestBody List<String> thirdRefundNo){
        log.info("getVipRefundOrder,会员查询付费会员退款订单信息请求参数：{}", JSON.toJSONString(thirdRefundNo));
        return generateSuccess(refundService.getVipRefundOrder(thirdRefundNo));
    }
}
