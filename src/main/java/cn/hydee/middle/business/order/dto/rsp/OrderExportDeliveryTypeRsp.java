package cn.hydee.middle.business.order.dto.rsp;

import cn.hydee.middle.business.order.Enums.DeliveryPlatformEnum;
import cn.hydee.middle.business.order.Enums.DeliveryTypeEnum;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

@Data
@ApiModel(description = "订单导出配送方式响应结果")
public class OrderExportDeliveryTypeRsp {

  @ApiModelProperty(value = "配送方式编码")
  private String code;

  @ApiModelProperty(value = "配送方式描述")
  private String msg;

  @ApiModelProperty(value = "配送方式小类集合")
  private List<String> subclassMsgList;

  public static List<OrderExportDeliveryTypeRsp> init(){
    List<OrderExportDeliveryTypeRsp> results = new ArrayList<>();
    for (DeliveryTypeEnum deliveryTypeEnum : DeliveryTypeEnum.values()) {
      OrderExportDeliveryTypeRsp result = new OrderExportDeliveryTypeRsp();
      if(deliveryTypeEnum.getCode().equals(DeliveryTypeEnum.PLAT.getCode()) || deliveryTypeEnum.getCode().equals(DeliveryTypeEnum.BUYER_SELF.getCode())){
        result.setCode(deliveryTypeEnum.getCode());
        result.setMsg(deliveryTypeEnum.getMsg());
        result.setSubclassMsgList(Lists.newArrayList());
        results.add(result);
      }else if(deliveryTypeEnum.getCode().equals(DeliveryTypeEnum.SELLER_SELF.getCode())){
        result.setCode(deliveryTypeEnum.getCode());
        result.setMsg(deliveryTypeEnum.getMsg());
        List<String> subclassMsgList = new ArrayList<>();
        subclassMsgList.add(DeliveryPlatformEnum.DADA.getName());
        subclassMsgList.add(DeliveryPlatformEnum.SFTC.getName());
        subclassMsgList.add(DeliveryPlatformEnum.MEITUAN_RIDER.getName());
        subclassMsgList.add(DeliveryPlatformEnum.STAFF.getName());
        subclassMsgList.add(DeliveryPlatformEnum.SJEXPRESS.getName());
        result.setSubclassMsgList(subclassMsgList);
        results.add(result);
      }
    }
    return results;
  }
}
