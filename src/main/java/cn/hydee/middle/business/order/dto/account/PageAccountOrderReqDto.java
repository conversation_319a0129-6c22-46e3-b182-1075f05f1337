package cn.hydee.middle.business.order.dto.account;

import cn.hydee.starter.dto.PageBase;
import java.util.Collection;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/05/31 16:34
 **/
@Data
public class PageAccountOrderReqDto extends PageBase {

    /**
     * 下账组织结构集合
     * */
    private Collection<String> accOrganizationCode;

    /**
     * 下账状态 WAIT-待下账 PROCESS-下账中 SUCCESS-下账成功 FAIL-下账失败
     */
    private String state;

    /**
     * 订单类型 NORMAL-普通订单  POST_FEE-邮费单
     */
    private String orderType;

    /**
     * HD_H1-海典H1  HD_H2-海典H2  KC-科传
     */
    private String posMode;

    /**
     * OMS-心云作业   WMS-erp作业  O2O统一默认为 OMS
     */
    private String pickType;

}
