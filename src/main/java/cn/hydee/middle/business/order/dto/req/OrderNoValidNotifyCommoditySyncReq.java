package cn.hydee.middle.business.order.dto.req;

import lombok.Data;

import java.util.List;

/**
 * @author: xin.tu
 * @date: 2023/3/1 14:35
 * @menu:
 */
@Data
public class OrderNoValidNotifyCommoditySyncReq {
    /**
     * 系统订单号包括退款单号
     */
    private String orderNo;

    /**
     * 商户编码
     */
    private String merCode;

    /**
     * 门店编码
     */
    private String stCode;

    /**
     * 商品编码
     */
    private List<String> erpCodeList;

    /**
     * 场景
     * 场景字段只能传入 1 取消订单 2退款
     */
    private Integer scene;
}
