package cn.hydee.middle.business.order.feign;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.dto.honey.EMerchantCloudApplyDTO;
import cn.hydee.middle.business.order.dto.honey.VO.ResultVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * cn.hydee.middle.business.order.feign
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/5/12 9:35
 **/
//小蜜
@FeignClient(DsConstants.HONEY_MEDICAL_NAME)
public interface HoneyMedicalClient {

    /**
     * 查看审方结果
     * @param userToken 用户token
     * @param merCode 商户编码
     * @param cfNo 订单号
     * @return 审核结果信息
     */
    @GetMapping("/v1/prescriptionQuery/getByPingAnCfNo")
    ResultVO<EMerchantCloudApplyDTO> getByPingAnCfNo(@RequestHeader("userToken")String userToken, @RequestParam("merCode")String merCode,@RequestParam("cfNo") String cfNo);
}
