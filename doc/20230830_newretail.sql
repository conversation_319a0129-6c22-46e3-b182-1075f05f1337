  CREATE TABLE `assignment_engine_assignment` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `business_type` int NOT NULL COMMENT '业务类型',
  `business_unique_id` varchar(200)  NOT NULL COMMENT '业务数据唯一id',
  `assignment_status` tinyint NOT NULL DEFAULT 2 COMMENT '任务状态  1:ENQUEUE, 2:FAIELD, 3:SUCCEED',
  `sub_assignment_status` int  COMMENT '子任务状态',
  `pause_status_bit` bigint  COMMENT '二进制暂停状态',
  `next_execute_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '下次执行时间',
  `retry_times` int(11) NOT NULL DEFAULT '0' COMMENT '重试次数',
  `total_execute_times` int(11) NOT NULL DEFAULT '0' COMMENT '执行次数',
  `enable_status` int(11) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `failure_code` int(11) NULL COMMENT '错误码，用户自己定义  字段废弃',
  `extend_search_param` json COMMENT '扩展查询json字段， 需要使用字段查询建议创建虚拟列',
  `req_content` json NOT NULL COMMENT '请求参数json',
  `resp_content` longtext COLLATE utf8mb4_unicode_ci COMMENT '返回内容',
  `assignment_flag` bigint NOT NULL DEFAULT '-1' COMMENT '任务标识（2进制扩展）',
  `sharding_num` BIGINT DEFAULT NULL COMMENT '分片键' ,
  `remark` varchar(500)  DEFAULT NULL COMMENT '备注',
  `created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modified` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `group_id`       bigint(20)   NULL DEFAULT '-1' COMMENT '集团ID',
  `group_name`      varchar(200)   NULL DEFAULT ''  COMMENT '集团名称',
  `tenant_id` bigint(20) NOT NULL DEFAULT '-1',
  `yn` tinyint NOT NULL DEFAULT '1',
  `prescription_status` INT DEFAULT 0 NOT NULL COMMENT '处方单状态：0 非处方单, 1 处方单待审核 ,2处方单审核通过, 3处方单审核不通过',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_business_type_business_unique_id` (`business_type`,`business_unique_id`) USING BTREE,
  KEY `idx_fetch_assignment` (`business_type`, `assignment_status`, `next_execute_time`, `pause_status_bit`) USING BTREE,
  KEY `idx_assignment_status` (`assignment_status`) USING BTREE
) ENGINE=InnoDB   COMMENT='Assignment-Engine 任务表';




CREATE TABLE `order_commodity_cost_price_history` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `order_no` bigint NOT NULL COMMENT '订单号',
  `erp_sale_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'erp零售流水单号',
  `erp_code` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品erp编码',
  `commodity_make_no` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品批号',
  `commodity_batch` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品批次号',
  `commodity_count` double(16,6) DEFAULT NULL COMMENT '商品数量',
  `commodity_cost_price` decimal(16,6) DEFAULT '0.000000' COMMENT '商品成本单价',
  `commodity_cost_total` decimal(16,6) DEFAULT '0.000000' COMMENT '本单商品成本总额',
  `commodity_bill_total` decimal(16,6) DEFAULT '0.000000' COMMENT '本单商品下账总额',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `mer_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商户编码',
  `third_platform_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '平台编码',
  `online_store_code` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '下单线上门店编码',
  `online_store_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '下单线上门店名称',
  `organization_code` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '线下门店编码',
  `organization_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '线下门店名称',
  `bill_time` datetime DEFAULT NULL COMMENT '下账时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `u_order_erp_make_batch` (`order_no`,`erp_code`,`commodity_make_no`,`commodity_batch`) USING BTREE,
  KEY `order_commodity_cost_price_mer_code_IDX` (`mer_code`,`third_platform_code`,`online_store_code`,`organization_code`,`erp_code`) USING BTREE,
  KEY `order_commodity_cost_price_bill_time_IDX` (`bill_time`) USING BTREE,
  KEY `idx_order_no_erp_code` (`order_no`,`erp_code`,`commodity_bill_total`,`commodity_cost_total`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6297 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='商品明细成本价表';



CREATE TABLE `order_pick_info_history` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `order_detail_id` bigint NOT NULL COMMENT '订单详情id',
  `erp_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品erp编码',
  `commodity_batch_no` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品批号',
  `count` double(10,4) NOT NULL DEFAULT '0.0000' COMMENT '批号对应的数量',
  `purchase_price` decimal(16,2) DEFAULT NULL COMMENT '商品进价',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `package_id` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '包裹号',
  `alloc` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '货位编码',
  `alloc_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '货位名称',
  PRIMARY KEY (`id`),
  KEY `u_detail_id_batch` (`order_detail_id`,`commodity_batch_no`) USING BTREE,
  KEY `pack_id_idx` (`package_id`) USING BTREE COMMENT '包裹id索引'
) ENGINE=InnoDB AUTO_INCREMENT=23728021 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='订单拣货信息';

CREATE TABLE `inner_store_dictionary` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `organization_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '线下门店编码',
  `organization_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '线下门店名称',
  `inner_app_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '内部app_id',
  `inner_app_secret` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '内部app_secret',
  `is_open_new` varchar(2)  DEFAULT '1' COMMENT '是否开启新流程: 1-新,2老',
  `yn` tinyint NOT NULL DEFAULT '1' COMMENT '是否有效,1-有效,2-无效',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `organization_code_idx` (`organization_code`) USING BTREE COMMENT '线下门店编码索引',
  KEY `app_id_idx` (`inner_app_id`) USING BTREE COMMENT '内部app_id索引'
) ENGINE=InnoDB AUTO_INCREMENT=23728021 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='内部门店映射字典表';




ALTER TABLE `order_info`
ADD COLUMN `order_is_new` varchar(2)  DEFAULT '1' COMMENT '是否新订单: 1-新订单,2老订单' AFTER `order_type`;

ALTER TABLE `order_info`
ADD COLUMN `data_version` bigint  DEFAULT '0' COMMENT '数据版本,update订单信息默认+1' AFTER `order_is_new`;


ALTER TABLE `order_info`
ADD COLUMN `extend_info` json  DEFAULT NULL COMMENT '扩展信息';


ALTER TABLE order_delivery_record
      modify rider_name varchar(50) null comment '配送员id';

ALTER TABLE order_delivery_log
      modify rider_name varchar(50) null comment '配送员id';


ALTER TABLE ds_store_sound_config
      ADD COLUMN need_bill_order_fail  tinyint NOT NULL DEFAULT '0' COMMENT '销售单下账失败，0-不提示 其他-N次';


ALTER TABLE `refund_order`
ADD COLUMN `extend_info` json  DEFAULT NULL COMMENT '扩展信息';


alter table order_pick_info add column `is_valid` tinyint(1) default '1' comment '拣货信息是否有效 1-有效 0-无效';
alter table order_pick_info drop index u_detail_id_batch;
alter table order_pick_info add index u_detail_id_batch(order_detail_id, commodity_batch_no, is_valid);

alter table inner_store_dictionary
    add token varchar(255) not null comment '生成token';

alter table inner_store_dictionary
    add expiration_time bigint null comment 'token过期时间';



CREATE TABLE `migration_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `old_order_no` varchar(100) DEFAULT '' COMMENT '老系统订单号',
  `migration_type` varchar(10) DEFAULT '' COMMENT 'O2O ;B2C;CLOUD; ',
  `order_type` varchar(10) DEFAULT '' COMMENT 'order; refund ',
  `old_create_time` datetime  default null COMMENT '老系统创建时间,有就填,没就忽略',
  `new_order_no` varchar(100) DEFAULT '' COMMENT '新系统内部订单号',
  `organization_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '线下门店编码',
  `organization_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '线下门店名称',
  `req_content` json NOT NULL COMMENT '请求参数json',
  `data_version` bigint  DEFAULT '0' COMMENT '数据版本,有就填,没就忽略',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `message_id`  varchar(200)  NULL COMMENT '消息队列ID',
  PRIMARY KEY (`id`),
  KEY `idx_old_order_no` (`old_order_no`) USING BTREE COMMENT '老系统订单号'
) ENGINE=InnoDB AUTO_INCREMENT=23728021 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='迁移日志表';

alter table inner_store_dictionary add is_auto_bill_flow tinyint(1) default 0 comment '是否有自动下账流量(默认为0)';

alter table inner_store_dictionary add auto_bill_timestamp datetime null comment '最近一次自动下账心跳时间';


ALTER TABLE `dscloud`.`order_info`
ADD INDEX `idx_third_order_no`(`third_order_no`) USING BTREE;

ALTER TABLE `dscloud`.`ds_online_store_config`
ADD COLUMN `delivery_type` tinyint(1) NULL DEFAULT NULL COMMENT '配送方式 1平台配送  3自配送' AFTER `logistics_return_opportunity`;


