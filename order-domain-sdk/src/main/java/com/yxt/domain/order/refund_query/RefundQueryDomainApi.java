package com.yxt.domain.order.refund_query;

import static com.yxt.domain.order.BusinessOrderServiceName.API;

import com.yxt.domain.order.refund_query.req.RefundPageSearchReq;
import com.yxt.domain.order.refund_query.req.RefundSearchByRefundNoReq;
import com.yxt.domain.order.refund_query.res.RefundDomainRelatedRes;
import com.yxt.domain.order.refund_query.res.RefundSimpleRes;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface RefundQueryDomainApi {

  /**
   * 根据条件查询订单号
   */
  @PostMapping(API + "/refund/search/page")
  ResponseBase<PageDTO<RefundSimpleRes>> refundSearchPage(@RequestBody RefundPageSearchReq req);

  /**
   * 根据条件查询订单号
   */
  @PostMapping(API + "/refund/search/by-refund-no")
  ResponseBase<RefundDomainRelatedRes> refundSearchByRefundNo(@RequestBody RefundSearchByRefundNoReq req);

}
