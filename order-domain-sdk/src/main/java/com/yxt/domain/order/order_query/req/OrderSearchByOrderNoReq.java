package com.yxt.domain.order.order_query.req;

import com.yxt.order.types.order.enums.OrderQryScaleEnum;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class OrderSearchByScaleReq {

  @ApiModelProperty(value = "订单号")
  private String orderNo;

  @ApiModelProperty(value = "查询维度")
  private List<OrderQryScaleEnum> scaleList;
}
