package com.yxt.domain.order.aftersale;

import static com.yxt.domain.order.BusinessOrderServiceName.API;

import com.yxt.domain.order.aftersale.req.CreateRefundReq;
import com.yxt.lang.dto.api.ResponseBase;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月12日 16:58
 * @email: <EMAIL>
 */
public interface AfterSaleDomainApi {


  //  refundBasicService.createRefundSuccessful
  @PostMapping(API + "/after-sale/create-refund")
  ResponseBase<Boolean> createRefund(@RequestBody CreateRefundReq req);

}
