package com.yxt.domain.order.order_query.req;

import com.yxt.lang.dto.api.PageRequestDTO;
import java.time.LocalDate;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class OrderSearchReq extends PageRequestDTO {

  /**
   * 订单号列表
   */
  private List<String> orderNoList;

  /**
   * 网店编码列表
   */
  private List<String> clientCodeList;

  /**
   * 门店编码列表
   */
  private List<String> orgCodeList;

  /**
   * 订单状态
   */
  private List<Integer> orderStatusList;

  /**
   * 平台编码
   */
  private String platformCode;

  /**
   * 服务模式
   */
  private String serviceMode;

  /**
   * 开始日期,格式：yyyy-MM-dd
   */
  private LocalDate startOrderCreatedDate;

  /**
   * 结束日期,格式：yyyy-MM-dd
   */
  private LocalDate endOrderCreatedDate;
}
