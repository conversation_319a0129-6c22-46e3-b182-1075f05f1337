package com.yxt.domain.order.order.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/3/31
 * @since 1.0
 */
@Data
public class OrderInfoResp {

    @ApiModelProperty("平台订单号")
    private String thirdOrderNo;

    @ApiModelProperty("下账状态")
    private String erpState;

    @ApiModelProperty("下账金额")
    private BigDecimal billTotalAmount;

    @ApiModelProperty("服务模式")
    private String serviceMode;
}
