package com.yxt.domain.order.order_query;

import static com.yxt.domain.order.BusinessOrderServiceName.API;

import com.yxt.domain.order.order_query.req.OrderSearchByOrderNoReq;
import com.yxt.domain.order.order_query.req.OrderPageSearchReq;
import com.yxt.domain.order.order_query.res.OrderDomainRelatedRes;
import com.yxt.domain.order.order_query.res.OrderSimpleRes;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface OrderQueryDomainApi {

  /**
   * 根据条件查询订单号
   */
  @PostMapping(API + "/order/search/page")
  ResponseBase<PageDTO<OrderSimpleRes>> orderSearchPage(@RequestBody OrderPageSearchReq req);

  /**
   * 根据条件查询订单号
   */
  @PostMapping(API + "/order/search/by-scale")
  ResponseBase<OrderDomainRelatedRes> orderSearchByScale(@RequestBody OrderSearchByOrderNoReq req);

}
