package com.yxt.domain.order.order;

import com.yxt.domain.order.order.req.OrderInfoReq;
import com.yxt.domain.order.order.resp.OrderInfoResp;
import com.yxt.lang.dto.api.ResponseBase;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

import static com.yxt.domain.order.BusinessOrderServiceName.API;

/**
 * <AUTHOR>
 * @date 2025/3/31
 * @since 1.0
 * 订单Api
 */
public interface OrderDomainApi {

    /**
     * 查询订单对账信息
     *
     * @param reqDto
     * @return ResponseBase<List<OrderInfoResp>>
     */
    @PostMapping(API + "/order/selectOrderAccounts")
    ResponseBase<List<OrderInfoResp>> selectOrderAccounts(@Valid @RequestBody OrderInfoReq reqDto);
}
