package com.yxt.domain.order.accountcheck.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/25
 * @since 1.0
 */
@Data
public class AccountCheckPageReq {

    @ApiModelProperty(value = "开始账单日期")
    private String startAccountTime;

    @ApiModelProperty(value = "结束账单日期")
    private String endAccountTime;

    @ApiModelProperty(value = "商户编码")
    private String merCode;

    @ApiModelProperty(value = "三方平台编码")
    private String thirdPlatformCode;

    @ApiModelProperty(value = "机构编码")
    private String organizationCode;

    @ApiModelProperty(value = "平台订单号")
    private String thirdOrderNo;

    @ApiModelProperty(value = "当前页，从第1页开始，不传默认为1")
    private int currentPage=1;

    @ApiModelProperty(value = "每页显示条数，不传默认20")
    private int pageSize=20;
}
