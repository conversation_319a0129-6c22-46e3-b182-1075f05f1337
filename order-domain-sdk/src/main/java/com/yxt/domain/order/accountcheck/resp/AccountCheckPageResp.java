package com.yxt.domain.order.accountcheck.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/3/25
 * @since 1.0
 */
@Data
public class AccountCheckPageResp {

    @ApiModelProperty(value = "账单日期")
    private LocalDateTime accountTime;

    @ApiModelProperty(value = "三方平台编码")
    private String thirdPlatformCode;

    @ApiModelProperty("md5编码")
    private String md5Code;

    @ApiModelProperty("门店编码")
    private String onlineStoreCode;

    @ApiModelProperty("原始数据")
    private String orginData;
}
