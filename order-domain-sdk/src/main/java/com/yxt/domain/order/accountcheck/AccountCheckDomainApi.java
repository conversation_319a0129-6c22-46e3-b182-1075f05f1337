package com.yxt.domain.order.accountcheck;

import com.yxt.domain.order.accountcheck.req.AccountCheckPageReq;
import com.yxt.domain.order.accountcheck.resp.AccountCheckPageResp;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import static com.yxt.domain.order.BusinessOrderServiceName.API;

/**
 * <AUTHOR>
 * @date 2025/3/25
 * @since 1.0
 * 结算单Api
 */
public interface AccountCheckDomainApi {

    @PostMapping(API + "/account-check/selectPage")
    ResponseBase<PageDTO<AccountCheckPageResp>> selectAccountCheckPage(@RequestBody AccountCheckPageReq reqDto);

}
