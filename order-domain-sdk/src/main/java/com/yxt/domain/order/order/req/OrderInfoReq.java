package com.yxt.domain.order.order.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/31
 * @since 1.0
 */
@Data
public class OrderInfoReq {

    @ApiModelProperty("平台订单号列表")
    @Size(min = 1, max = 100, message = "列表长度必须在1-100之间")
    private List<String> thirdOrderNos;

    @ApiModelProperty("起始创建时间")
    private String startCreateTime;

    @ApiModelProperty("终止创建时间")
    private String endCreateTime;
}
